import {
   GoogleCPM,
   GoogleCPC,
   GoogleCPA,
   GoogleROAS,
   GoogleCTR,
   Googleconversion_rate,
   KPIFormula,
   KPIFormulaExtended,
} from '@/types/kpi';
import * as types from '../types';

function safeDivide(numerator: number, denominator: number): number {
   if (isNaN(numerator) || isNaN(denominator) || denominator === 0) {
      return NaN;
   }
   return numerator / denominator;
}
export const GOOGLE_KPI_CALCULATE = {
   roas: (dateAgg: types.GoogleDateAgg): number => {
      const roasSums = Object.values(dateAgg).reduce(
         (roas: GoogleROAS, kpis: types.GoogleKpiAggregate) => {
            return {
               totalConversionValue:
                  Number(roas.totalConversionValue) +
                  Number(kpis.conversions_value || 0),
               totalSpent: Number(roas.totalSpent) + Number(kpis.spend || 0),
            };
         },
         { totalConversionValue: 0, totalSpent: 0 },
      );
      return safeDivide(roasSums.totalConversionValue, roasSums.totalSpent);
   },
   cpa: (dateAgg: types.GoogleDateAgg): number => {
      const cpaSums = Object.values(dateAgg).reduce(
         (cpa: GoogleCPA, kpis: types.GoogleKpiAggregate) => {
            return {
               totalSpent: Number(cpa.totalSpent) + Number(kpis.spend || 0),
               totalConversions:
                  Number(cpa.totalConversions) + Number(kpis.conversions || 0),
            };
         },
         { totalSpent: 0, totalConversions: 0 },
      );
      return safeDivide(cpaSums.totalSpent, cpaSums.totalConversions);
   },
   cac: (dateAgg: types.GoogleDateAgg): number => {
      const cpaSums = Object.values(dateAgg).reduce(
         (cpa: GoogleCPA, kpis: types.GoogleKpiAggregate) => {
            return {
               totalSpent: Number(cpa.totalSpent) + Number(kpis.spend || 0),
               totalConversions:
                  Number(cpa.totalConversions) + Number(kpis.conversions || 0),
            };
         },
         { totalSpent: 0, totalConversions: 0 },
      );
      return safeDivide(cpaSums.totalSpent, cpaSums.totalConversions);
   },
   cpc: (dateAgg: types.GoogleDateAgg): number => {
      const cpcSums = Object.values(dateAgg).reduce(
         (cpc: GoogleCPC, kpis: types.GoogleKpiAggregate) => {
            return {
               totalSpent: Number(cpc.totalSpent) + Number(kpis.spend || 0),
               totalClicks: Number(cpc.totalClicks) + Number(kpis.clicks || 0),
            };
         },
         { totalSpent: 0, totalClicks: 0 },
      );
      return safeDivide(cpcSums.totalSpent, cpcSums.totalClicks);
   },
   cpm: (dateAgg: types.GoogleDateAgg): number => {
      const cpmSums = Object.values(dateAgg).reduce(
         (cpm: GoogleCPM, kpis: types.GoogleKpiAggregate) => {
            return {
               totalSpent: Number(cpm.totalSpent) + Number(kpis.spend || 0),
               totalImpressions:
                  Number(cpm.totalImpressions) + Number(kpis.impressions || 0),
            };
         },
         { totalSpent: 0, totalImpressions: 0 },
      );
      return safeDivide(cpmSums.totalSpent, cpmSums.totalImpressions) * 1000;
   },
   ctr: (dateAgg: types.GoogleDateAgg): number => {
      const ctrSums = Object.values(dateAgg).reduce(
         (ctr: GoogleCTR, kpis: types.GoogleKpiAggregate) => {
            return {
               totalClicks: Number(ctr.totalClicks) + Number(kpis.clicks || 0),
               totalImpressions:
                  Number(ctr.totalImpressions) + Number(kpis.impressions || 0),
            };
         },
         { totalClicks: 0, totalImpressions: 0 },
      );
      return safeDivide(ctrSums.totalClicks, ctrSums.totalImpressions) * 100;
   },
   conversion_rate: (dateAgg: types.GoogleDateAgg): number => {
      const conversion_rateSums = Object.values(dateAgg).reduce(
         (
            conversion_rate: Googleconversion_rate,
            kpis: types.GoogleKpiAggregate,
         ) => {
            return {
               totalConversions:
                  Number(conversion_rate.totalConversions) +
                  Number(kpis.conversions || 0),
               totalInteractions:
                  Number(conversion_rate.totalInteractions) +
                  Number(kpis.interactions || 0),
            };
         },
         { totalConversions: 0, totalInteractions: 0 },
      );
      return (
         safeDivide(
            conversion_rateSums.totalConversions,
            conversion_rateSums.totalInteractions,
         ) * 100
      );
   },
};

export const kpiFormulas: types.KpiFormulas = {
   roas: (aggregatedResults, keywordName) => {
      const conversion_value =
         aggregatedResults['conversions_value']?.get(keywordName) ?? NaN;
      const spend = aggregatedResults['spend']?.get(keywordName) ?? NaN;

      return safeDivide(conversion_value, spend);
   },

   cpa: (aggregatedResults, keywordName) => {
      const conversion_value =
         aggregatedResults['conversions']?.get(keywordName) ?? NaN;
      const spend = aggregatedResults['spend']?.get(keywordName) ?? NaN;

      return safeDivide(spend, conversion_value);
   },

   cpc: (aggregatedResults, keywordName) => {
      const spend = aggregatedResults['spend']?.get(keywordName) ?? NaN;
      const clicks = aggregatedResults['clicks']?.get(keywordName) ?? NaN;

      return safeDivide(spend, clicks);
   },

   cpm: (aggregatedResults, keywordName) => {
      const spend = aggregatedResults['spend']?.get(keywordName) ?? NaN;
      const impressions =
         aggregatedResults['impressions']?.get(keywordName) ?? NaN;

      return safeDivide(spend, impressions) * 1000;
   },
   ctr: (aggregatedResults, keywordName) => {
      const clicks = aggregatedResults['clicks']?.get(keywordName) ?? NaN;
      const impressions =
         aggregatedResults['impressions']?.get(keywordName) ?? NaN;

      return safeDivide(clicks, impressions) * 100;
   },
   conversion_rate: (aggregatedResults, keywordName) => {
      const conversions =
         aggregatedResults['conversions']?.get(keywordName) ?? NaN;
      const interactions =
         aggregatedResults['interactions']?.get(keywordName) ?? NaN;

      return safeDivide(conversions, interactions) * 100;
   },
};

export const KeywordKpiFormula = {
   roas: (aggregatedResults: Record<string, number>) => {
      const conversion_value = aggregatedResults['conversions_value'] ?? NaN;
      const spend = aggregatedResults['spend'] ?? NaN;

      return safeDivide(conversion_value, spend);
   },

   cpa: (aggregatedResults: Record<string, number>) => {
      const spend = aggregatedResults['spend'] ?? NaN;
      const conversions = aggregatedResults['conversions'] ?? NaN;

      return safeDivide(spend, conversions);
   },

   cpc: (aggregatedResults: Record<string, number>) => {
      const spend = aggregatedResults['spend'] ?? NaN;
      const clicks = aggregatedResults['clicks'] ?? NaN;

      return safeDivide(spend, clicks);
   },

   cpm: (aggregatedResults: Record<string, number>) => {
      const spend = aggregatedResults['spend'] ?? NaN;
      const impressions = aggregatedResults['impressions'] ?? NaN;

      return safeDivide(spend, impressions) * 1000;
   },

   ctr: (aggregatedResults: Record<string, number>) => {
      const clicks = aggregatedResults['clicks'] ?? NaN;
      const impressions = aggregatedResults['impressions'] ?? NaN;

      return safeDivide(clicks, impressions) * 100;
   },
   conversion_rate: (aggregatedResults: Record<string, number>) => {
      const conversions = aggregatedResults['conversions'] ?? NaN;
      const interactions = aggregatedResults['interactions'] ?? NaN;

      return safeDivide(conversions, interactions) * 100;
   },
};

export const KPI_CALCULATION_DAYWISE: Record<
   types.GoogleAdsKpiKeys,
   KPIFormula
> = {
   roas: (day, daywiseAgg) =>
      calculateKPI(
         daywiseAgg,
         { numerator: 'conversions_value', denominator: 'spend' },
         day,
      ),
   cpa: (day, daywiseAgg) =>
      calculateKPI(
         daywiseAgg,
         { numerator: 'spend', denominator: 'conversions' },
         day,
      ),
   cpm: (day, daywiseAgg) =>
      calculateKPI(
         daywiseAgg,
         { numerator: 'spend', denominator: 'clicks' },
         day,
      ),
   cpc: (day, daywiseAgg) =>
      calculateKPI(
         daywiseAgg,
         { numerator: 'spend', denominator: 'impressions' },
         day,
      ) * 1000,
   ctr: (day, daywiseAgg) =>
      calculateKPI(
         daywiseAgg,
         { numerator: 'clicks', denominator: 'impressions' },
         day,
      ) * 100,
   conversion_rate: (day, daywiseAgg) =>
      calculateKPI(
         daywiseAgg,
         { numerator: 'conversions', denominator: 'interactions' },
         day,
      ) * 100,
};

const calculateKPI: KPIFormulaExtended = (daywiseAgg, keys, day) => {
   const numerator = daywiseAgg.get(keys.numerator)?.[day] || 0;
   const denominator = daywiseAgg.get(keys.denominator)?.[day] || 0;
   return denominator > 0 ? numerator / denominator : NaN;
};
