{"name": "new-dashboard-backend", "version": "1.0.0", "description": "This is the new dashboard api repo for flable", "main": "server.ts", "scripts": {"dev": "cross-env NODE_ENV=dev nodemon server", "format": "prettier .", "format:check": "npm run format -- --check", "format:fix": "npm run format -- --write", "lint": "eslint .", "lint:fix": "npm run lint -- --fix", "prepare": "husky", "test": "jest --watch --runInBand", "start": "ts-node server.ts", "typeorm": "typeorm-ts-node-commonjs"}, "repository": {"type": "git", "url": "https://gitlab.flable.ai/lacritz/cas/new_dashboardapi"}, "keywords": ["mern"], "lint-staged": {"*.ts": ["npm run format:fix", "npm run lint:fix"]}, "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://gitlab.flable.ai/lacritz/cas/new_dashboardapi/-/issues"}, "homepage": "https://gitlab.flable.ai/lacritz/cas/new_dashboardapi", "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.14", "@types/express": "^4.17.17", "@types/http-errors": "^2.0.1", "@types/jest": "^29.5.5", "@types/jsonwebtoken": "^9.0.7", "@types/markdown-it": "^14.1.2", "@types/multer": "^1.4.11", "@types/node": "^16.11.10", "@types/node-cron": "^3.0.11", "@types/node-schedule": "^2.1.7", "@types/pdfkit": "^0.14.0", "@types/pg": "^8.11.6", "@types/supertest": "^2.0.13", "@types/uuid": "^9.0.8", "@types/winston": "^2.4.4", "@typescript-eslint/eslint-plugin": "^6.6.0", "@typescript-eslint/parser": "^6.6.0", "cross-env": "^7.0.3", "eslint": "^8.49.0", "eslint-config-prettier": "^9.0.0", "husky": "^9.0.11", "jest": "^29.7.0", "lint-staged": "^14.0.1", "nodemon": "^3.0.1", "prettier": "^3.0.3", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.7.0", "tsconfig-paths": "^4.2.0", "typescript": "4.5.2"}, "dependencies": {"@azure/storage-blob": "^12.18.0", "@runwayml/sdk": "^2.4.0", "@types/dotenv": "^8.2.0", "@types/ioredis": "^5.0.0", "axios": "^1.7.2", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.5", "express": "^4.18.2", "form-data": "^4.0.0", "googleapis": "^146.0.0", "http-errors": "^2.0.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "markdown-it": "^14.1.0", "mongodb": "^5.9.2", "mongoose": "^8.3.3", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "node-schedule": "^2.1.1", "nodemailer": "^6.9.15", "oauth-1.0a": "^2.2.6", "openai": "^4.33.0", "pdfkit": "^0.17.1", "pg": "^8.4.0", "reflect-metadata": "^0.1.13", "to-words": "^4.6.0", "typeorm": "0.3.17", "uuid": "^9.0.1", "winston": "^3.10.0"}}