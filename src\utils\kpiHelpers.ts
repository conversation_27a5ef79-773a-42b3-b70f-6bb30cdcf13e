import {
   AmazonConversionRate,
   AmazonReturnRate,
   CostPerLead,
   CPA,
   CPC,
   CPM,
   CPP,
   CPV,
   CTR,
   DateAgg,
   KPIList,
   ROAS,
   VTR,
   ACOS,
   MER,
   AmazonAOV,
   LeadConversionRate,
   PurchaseRate,
   Googleconversion_rate,
   ConversionRate,
   NetMargin,
   CostPerSession,
   ShopifyAOV,
} from '../types/kpi';

export const KPI_CALCULATE = {
   roas: (dateAgg: DateAgg): number => {
      const roasSums = Object.values(dateAgg).reduce(
         (roas: ROAS, kpis: KPIList) => {
            const weightedRoas = (kpis.total_spent || 0) * (kpis.roas || 0);
            return {
               weightedRoas: roas.weightedRoas + weightedRoas,
               totalSpent: roas.totalSpent + (kpis.total_spent || 0),
            };
         },
         { weightedRoas: 0, totalSpent: 0 },
      );
      return roasSums.weightedRoas / roasSums.totalSpent;
   },
   google_roas: (dateAgg: DateAgg): number => {
      const roasSums = Object.values(dateAgg).reduce(
         (roas: ROAS, kpis: KPIList) => {
            const weightedRoas =
               (kpis.google_total_spend || 0) * kpis.google_roas;
            return {
               weightedRoas: roas.weightedRoas + weightedRoas,
               totalSpent: roas.totalSpent + (kpis.google_total_spend || 0),
            };
         },
         { weightedRoas: 0, totalSpent: 0 },
      );
      return roasSums.weightedRoas / roasSums.totalSpent;
   },
   blended_roas: (dateAgg: DateAgg): number => {
      const roasSums = Object.values(dateAgg).reduce(
         (roas: ROAS, kpis: KPIList) => {
            const weightedRoas =
               (kpis.blended_ad_spend || 0) * (kpis.blended_roas || 0);
            return {
               weightedRoas: roas.weightedRoas + weightedRoas,
               totalSpent: roas.totalSpent + (kpis.blended_ad_spend || 0),
            };
         },
         { weightedRoas: 0, totalSpent: 0 },
      );
      return roasSums.weightedRoas / roasSums.totalSpent;
   },
   amazon_roas: (dateAgg: DateAgg): number => {
      const roasSums = Object.values(dateAgg).reduce(
         (roas: ROAS, kpis: KPIList) => {
            const weightedRoas =
               (kpis.amazon_ads_spent || 0) * (kpis.amazon_roas || 0);
            return {
               weightedRoas: roas.weightedRoas + weightedRoas,
               totalSpent: roas.totalSpent + (kpis.amazon_ads_spent || 0 || 0),
            };
         },
         { weightedRoas: 0, totalSpent: 0 },
      );

      return roasSums.weightedRoas / roasSums.totalSpent;
   },
   cpc: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (cpc: CPC, kpis: KPIList) => {
            return {
               totalSpent: cpc.totalSpent + (kpis.total_spent || 0),
               totalClicks: cpc.totalClicks + (kpis.total_clicks || 0),
            };
         },
         {
            totalSpent: 0,
            totalClicks: 0,
         },
      );
      return sums.totalSpent / sums.totalClicks;
   },
   google_cpc: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (cpc: CPC, kpis: KPIList) => {
            return {
               totalSpent: cpc.totalSpent + (kpis.google_total_spend || 0),
               totalClicks: cpc.totalClicks + (kpis.google_total_clicks || 0),
            };
         },
         {
            totalSpent: 0,
            totalClicks: 0,
         },
      );
      return sums.totalSpent / sums.totalClicks;
   },
   amazon_cpc: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (cpc: CPC, kpis: KPIList) => {
            return {
               totalSpent: cpc.totalSpent + (kpis.amazon_ads_spent || 0),
               totalClicks: cpc.totalClicks + (kpis.amazon_total_clicks || 0),
            };
         },
         {
            totalSpent: 0,
            totalClicks: 0,
         },
      );
      return sums.totalSpent / sums.totalClicks;
   },
   cpp: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (cpc: CPP, kpis: KPIList) => {
            return {
               totalSpent: cpc.totalSpent + (kpis.total_spent || 0),
               totalPurchase: cpc.totalPurchase + (kpis.total_purchase || 0),
            };
         },
         {
            totalSpent: 0,
            totalPurchase: 0,
         },
      );
      return sums.totalSpent / sums.totalPurchase;
   },
   cpm: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (cpc: CPM, kpis: KPIList) => {
            return {
               totalSpent: cpc.totalSpent + (kpis.total_spent || 0),
               totalImpressions:
                  cpc.totalImpressions + (kpis.total_impressions || 0),
            };
         },
         {
            totalSpent: 0,
            totalImpressions: 0,
         },
      );
      return (sums.totalSpent / sums.totalImpressions) * 1000;
   },
   amazon_vcpm: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (cpc: CPM, kpis: KPIList) => {
            return {
               totalSpent: cpc.totalSpent + (kpis.amazon_ads_spent || 0),
               totalImpressions:
                  cpc.totalImpressions + (kpis.amazon_total_impressions || 0),
            };
         },
         {
            totalSpent: 0,
            totalImpressions: 0,
         },
      );
      return (sums.totalSpent / sums.totalImpressions) * 1000;
   },
   cpv: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (cpc: CPV, kpis: KPIList) => {
            return {
               totalSpent: cpc.totalSpent + (kpis.total_spent || 0),
               totalVideoViews: cpc.totalVideoViews + (kpis.video_view || 0),
            };
         },
         {
            totalSpent: 0,
            totalVideoViews: 0,
         },
      );
      return sums.totalSpent / sums.totalVideoViews;
   },
   vtr: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (cpc: VTR, kpis: KPIList) => {
            return {
               totalP100Watched:
                  cpc.totalP100Watched + (kpis['100_percent_video_views'] || 0),
               totalImpressions:
                  cpc.totalImpressions + (kpis.total_impressions || 0),
            };
         },
         {
            totalP100Watched: 0,
            totalImpressions: 0,
         },
      );
      return (sums.totalP100Watched / sums.totalImpressions) * 100;
   },
   mer: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (mer: MER, kpis: KPIList) => {
            return {
               totalSpent: mer.totalSpent + (kpis.blended_ad_spend || 0 || 0),
               totalRevenue:
                  mer.totalRevenue + (kpis.blended_total_revenue || 0),
            };
         },
         {
            totalSpent: 0,
            totalRevenue: 0,
         },
      );
      return (sums.totalSpent / sums.totalRevenue) * 100;
   },
   cost_per_lead: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (cpc: CostPerLead, kpis: KPIList) => {
            return {
               totalSpent: cpc.totalSpent + (kpis.total_spent || 0),
               totalLeads: cpc.totalLeads + (kpis.leads || 0),
            };
         },
         {
            totalSpent: 0,
            totalLeads: 0,
         },
      );
      return sums.totalSpent / sums.totalLeads;
   },
   lead_conversion_rate: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (lcr: LeadConversionRate, kpis: KPIList) => {
            return {
               totalLeads: lcr.totalLeads + (kpis.leads || 0),
               totalClicks: lcr.totalClicks + (kpis.total_clicks || 0),
            };
         },
         {
            totalLeads: 0,
            totalClicks: 0,
         },
      );
      return (sums.totalLeads / sums.totalClicks) * 100;
   },
   conversion_rate: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (lcr: ConversionRate, kpis: KPIList) => {
            return {
               totalOrders: lcr.totalOrders + (kpis.total_orders || 0),
               totalSessions: lcr.totalSessions + (kpis.total_sessions || 0),
            };
         },
         {
            totalOrders: 0,
            totalSessions: 0,
         },
      );
      return (sums.totalOrders / sums.totalSessions) * 100;
   },
   cost_per_session: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (lcr: CostPerSession, kpis: KPIList) => {
            return {
               totalSpent:
                  lcr.totalSpent +
                  (kpis.total_spent || 0 + kpis.google_total_spend || 0),
               totalSessions: lcr.totalSessions + (kpis.total_sessions || 0),
            };
         },
         {
            totalSpent: 0,
            totalSessions: 0,
         },
      );
      return sums.totalSpent / sums.totalSessions;
   },
   net_margin: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (lcr: NetMargin, kpis: KPIList) => {
            return {
               totalNetProfit: lcr.totalNetProfit + (kpis.net_profit || 0),
               totalRevenue:
                  lcr.totalRevenue + (kpis.blended_total_revenue || 0),
            };
         },
         {
            totalNetProfit: 0,
            totalRevenue: 0,
         },
      );
      return (sums.totalNetProfit / sums.totalRevenue) * 100;
   },
   purchase_rate: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (lcr: PurchaseRate, kpis: KPIList) => {
            return {
               totalPurchase: lcr.totalPurchase + (kpis.total_purchase || 0),
               totalClicks: lcr.totalClicks + (kpis.total_clicks || 0),
            };
         },
         {
            totalPurchase: 0,
            totalClicks: 0,
         },
      );
      return (sums.totalPurchase / sums.totalClicks) * 100;
   },
   google_cpm: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (cpc: CPM, kpis: KPIList) => {
            return {
               totalSpent: cpc.totalSpent + (kpis.google_total_spend || 0),
               totalImpressions:
                  cpc.totalImpressions + kpis.google_total_impressions,
            };
         },
         {
            totalSpent: 0,
            totalImpressions: 0,
         },
      );
      return (sums.totalSpent / sums.totalImpressions) * 1000;
   },
   ctr: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (cpc: CTR, kpis: KPIList) => {
            return {
               totalClicks: cpc.totalClicks + (kpis.total_clicks || 0),
               totalImpressions:
                  cpc.totalImpressions + (kpis.total_impressions || 0),
            };
         },
         {
            totalClicks: 0,
            totalImpressions: 0,
         },
      );
      return (sums.totalClicks / sums.totalImpressions) * 100;
   },
   google_ctr: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (cpc: CTR, kpis: KPIList) => {
            return {
               totalClicks: cpc.totalClicks + (kpis.google_total_clicks || 0),
               totalImpressions:
                  cpc.totalImpressions + kpis.google_total_impressions,
            };
         },
         {
            totalClicks: 0,
            totalImpressions: 0,
         },
      );
      return (sums.totalClicks / sums.totalImpressions) * 100;
   },
   google_conversion_rate: (dateAgg: DateAgg): number => {
      const gcaSums = Object.values(dateAgg).reduce(
         (gca: Googleconversion_rate, kpis: KPIList) => {
            return {
               totalConversions:
                  gca.totalConversions + (kpis.google_total_conversions || 0),
               totalInteractions:
                  gca.totalInteractions + (kpis.google_interactions || 0),
            };
         },
         {
            totalConversions: 0,
            totalInteractions: 0,
         },
      );
      return (gcaSums.totalConversions / gcaSums.totalInteractions) * 100;
   },
   amazon_ctr: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (cpc: CTR, kpis: KPIList) => {
            return {
               totalClicks: cpc.totalClicks + (kpis.amazon_total_clicks || 0),
               totalImpressions:
                  cpc.totalImpressions + (kpis.amazon_total_impressions || 0),
            };
         },
         {
            totalClicks: 0,
            totalImpressions: 0,
         },
      );
      return (sums.totalClicks / sums.totalImpressions) * 100;
   },
   amazon_acos: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (cpc: ACOS, kpis: KPIList) => {
            return {
               totalSpent: cpc.totalSpent + (kpis.amazon_ads_spent || 0),
               totalRevenue:
                  cpc.totalRevenue + (kpis.amazon_ads_gross_sales || 0),
            };
         },
         {
            totalSpent: 0,
            totalRevenue: 0,
         },
      );
      return (sums.totalSpent / sums.totalRevenue) * 100;
   },
   google_cpa: (dateAgg: DateAgg): number => {
      const cpaSums = Object.values(dateAgg).reduce(
         (cpa: CPA, kpis: KPIList) => {
            return {
               totalSpent: cpa.totalSpent + (kpis.google_total_spend || 0),
               totalConversions:
                  cpa.totalConversions + (kpis.google_total_conversions || 0),
            };
         },
         {
            totalSpent: 0,
            totalConversions: 0,
         },
      );
      return cpaSums.totalSpent / cpaSums.totalConversions;
   },
   blended_cpa: (dateAgg: DateAgg): number => {
      const cpaSums = Object.values(dateAgg).reduce(
         (cpa: CPA, kpis: KPIList) => {
            return {
               totalSpent: cpa.totalSpent + (kpis.blended_ad_spend || 0),
               totalConversions:
                  cpa.totalConversions + (kpis.blended_total_orders || 0),
            };
         },
         {
            totalSpent: 0,
            totalConversions: 0,
         },
      );
      return cpaSums.totalSpent / cpaSums.totalConversions;
   },
   amazon_conversion_rate: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (acr: AmazonConversionRate, kpis: KPIList) => {
            return {
               totalOrders: acr.totalOrders + (kpis.amazon_total_orders || 0),
               totalSessions:
                  acr.totalSessions + (kpis.amazon_total_sessions || 0),
            };
         },
         {
            totalOrders: 0,
            totalSessions: 0,
         },
      );
      return (sums.totalOrders / sums.totalSessions) * 100;
   },
   amazon_average_order_value: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (acr: AmazonAOV, kpis: KPIList) => {
            return {
               totalSales: acr.totalSales + (kpis.amazon_gross_sales || 0),
               totalOrders: acr.totalOrders + (kpis.amazon_total_orders || 0),
            };
         },
         {
            totalSales: 0,
            totalOrders: 0,
         },
      );
      return sums.totalSales / sums.totalOrders;
   },
   average_order_value: (dateAgg: DateAgg): number => {
      const sums = Object.values(dateAgg).reduce(
         (saov: ShopifyAOV, kpis: KPIList) => {
            return {
               orderRevenue:
                  saov.orderRevenue +
                  ((kpis.order_revenue || 0) -
                     (kpis.shipping_charges || 0) -
                     kpis.taxes || 0),
               totalOrders: saov.totalOrders + (kpis.total_orders || 0),
            };
         },
         {
            orderRevenue: 0,
            totalOrders: 0,
         },
      );
      return sums.orderRevenue / sums.totalOrders;
   },
   amazon_return_rate: (dateAgg: DateAgg): number => {
      const arrSums = Object.values(dateAgg).reduce(
         (arr: AmazonReturnRate, kpis: KPIList) => {
            return {
               totalUnits: arr.totalUnits + (kpis.amazon_returns || 0),
               totalOrders: arr.totalOrders + (kpis.amazon_total_orders || 0),
            };
         },
         {
            totalUnits: 0,
            totalOrders: 0,
         },
      );
      return (arrSums.totalUnits / arrSums.totalOrders) * 100;
   },
};
