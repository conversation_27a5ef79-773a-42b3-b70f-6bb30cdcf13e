export interface KPIRules {
   metric: string;
   trend: 'increasing' | 'decreasing';
   value: string;
   value_type: 'absolute' | 'percentage';
   comparison: 'more_than' | 'less_than' | 'equal_to';
}

export interface AlertConditions {
   channel: string;
   campaigns: { id: string; name: string }[];
   metrics: string[];
   target_period: { value: string; label: string };
   reference_period: { value: string; label: string };
   kpi_rules: KPIRules[];
}

export interface CustomAlert {
   alert_id: string;
   client_id: string;
   user_id: string;
   alert_name: string;
   alert_description: string;
   alert_time: string;
   email_recipients: string[];
   alert_status: 'active' | 'inactive';
   alert_conditions: AlertConditions;
   user_timezone: string;
   emails_sent: number;
   alert_created_at: Date;
   alert_updated_at: Date;
}

export interface CustomAlertOptions {
   channels: string[];
   campaigns: {
      meta_ads_campaigns: { id: string; name: string; objective: string }[];
      google_ads_campaigns: { id: string; name: string; objective: string }[];
   };
   metrics: {
      dashboard_metrics: {
         facebookads: string[];
         googleads: string[];
         amazon_ads: string[];
         store: string[];
         web: string[];
         amazon_selling_partner: string[];
      };
      meta_campaigns_metrics: string[];
      google_campaigns_metrics: string[];
   };
}

export interface FetchDashboardMetricsPayload {
   clientId: string;
   channel: string;
   metrics: string[];
   period: string;
}

export interface FetchCampaignMetricsPayload {
   clientId: string;
   campaign: string;
   metrics: string[];
   period: string;
}
