import express from 'express';
import asyncHandler from '../../../midddleware/async-handler';

import { AuthService } from '../../../services/AuthService';
import { EmailService } from '../../../services/EmailService';
import { AppDataSource } from '../../../config/data-source';
import { logger } from '../../../config/logger';
import { AuthController } from '../../../controllers/AuthControllerNew';

const router = express.Router();

const entityManager = AppDataSource.manager;
const emailService = new EmailService();
const authService = new AuthService(logger, entityManager, emailService);
const authController = new AuthController(logger, authService);

router
   .route('/register')
   .post(asyncHandler(authController.handleRegister.bind(authController)));

router
   .route('/login')
   .post(asyncHandler(authController.handleLogin.bind(authController)));

router
   .route('/logout')
   .post(asyncHandler(authController.handleLogout.bind(authController)));

router
   .route('/refresh-token')
   .post(asyncHandler(authController.handleRefreshToken.bind(authController)));

router
   .route('/send-otp')
   .post(asyncHandler(authController.handleSendOtp.bind(authController)));

router
   .route('/verify-email')
   .post(asyncHandler(authController.handleVerifyEmail.bind(authController)));

router
   .route('/is-email-verified')
   .get(
      asyncHandler(authController.handleIsEmailVerified.bind(authController)),
   );

router
   .route('/reset-password')
   .post(asyncHandler(authController.handleResetPassword.bind(authController)));

export { router as auth1Router };
