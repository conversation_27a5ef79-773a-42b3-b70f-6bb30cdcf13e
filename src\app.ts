import express, { NextFunction, raw, Request, Response } from 'express';
import cors from 'cors';
import { HttpError } from 'http-errors';
import { logger } from './config/logger';
import routes from './routes/v1';
import { Config } from './config';
// express-fileupload

const app = express();

app.use(express.json({ limit: '10mb' })); // Adjust limit as needed
app.use(express.urlencoded({ limit: '10mb', extended: true }));
app.use(
   cors({
      origin: Config.FRONTEND_DOMAIN!.split(','),
      credentials: true,
   }),
);

app.get('/', (req: Request, res: Response) => {
   res.json({ message: 'Welcome to Flable backend service' });
});
app.use('/v1/subs/webhook', raw({ type: 'application/json' }));
app.use('/v1', routes);

// error middleware
app.use(
   // eslint-disable-next-line @typescript-eslint/no-unused-vars
   (err: HttpError, req: Request, res: Response, next: NextFunction): void => {
      logger.error(err.message);

      res.status(err.statusCode || 500).json({
         message: err.message,
      });
   },
);

export default app;
