import { EntityManager } from 'typeorm';
import { Config } from '../config';
import { google } from 'googleapis';
import { Logger } from 'winston';
import { GAAccountWithProperties } from '../types/googleanalytics';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
export class MetaAdsService {
   private readonly version: string;
   private readonly appId: string;
   private readonly appSecret: string;
   private readonly redirectUri: string;
   private readonly scopes: string[];

   constructor(
      private readonly entityManager: EntityManager,
      private readonly logger: Logger,
   ) {
      this.version = Config.META_OAUTH_VERSION || 'v21.0';
      this.appId = Config.META_APP_ID!;
      this.appSecret = Config.META_APP_SECRET!;
      this.redirectUri = Config.META_REDIRECT_URI!;
      this.scopes = ['ads_management', 'ads_read', 'read_insights'];
   }

   private async metaApiRequest<T = any>(
      endpoint: string,
      params: Record<string, any>,
      method: 'GET' | 'POST' = 'GET',
   ): Promise<T> {
      const url = `https://graph.facebook.com/${this.version}${endpoint}`;
      const config: AxiosRequestConfig = { method, url, params };

      try {
         const { data } = await axios(config);
         return data;
      } catch (error: any) {
         this.logger.error(`Meta API Request Failed: ${endpoint}`, {
            error: error.response?.data || error.message,
         });
         throw new Error(
            `Meta API call failed for ${endpoint}: ${error.response?.data?.error?.message || error.message}`,
         );
      }
   }

   private async exchangeCodeForShortLivedToken(code: string): Promise<string> {
      const data = await this.metaApiRequest('/oauth/access_token', {
         client_id: this.appId,
         client_secret: this.appSecret,
         redirect_uri: this.redirectUri,
         code,
      });

      return data.access_token;
   }
   private async exchangeForLongLivedToken(
      shortLivedToken: string,
   ): Promise<string> {
      const data = await this.metaApiRequest('/oauth/access_token', {
         grant_type: 'fb_exchange_token',
         client_id: this.appId,
         client_secret: this.appSecret,
         fb_exchange_token: shortLivedToken,
      });

      return data.access_token;
   }

   public getmetaAdsAuthUrl(): string {
      const authUrl = new URL(
         `https://www.facebook.com/${this.version}/dialog/oauth`,
      );
      authUrl.searchParams.append('client_id', this.appId);
      authUrl.searchParams.append('redirect_uri', this.redirectUri);
      authUrl.searchParams.append('scope', this.scopes.join(','));
      authUrl.searchParams.append('response_type', 'code');

      return authUrl.toString();
   }

   public async getLongLivedTokenFromCode(code: string): Promise<string> {
      console.log('inside service--->');
      const shortLivedToken = await this.exchangeCodeForShortLivedToken(code);
      const longLivedToken =
         await this.exchangeForLongLivedToken(shortLivedToken);
      return longLivedToken;
   }
   public async getAllAdAccounts(accessToken: string): Promise<any[]> {
      const fields = ['name', 'account_id', 'account_status'].join(',');

      const data = await this.metaApiRequest('/me/adaccounts', {
         access_token: accessToken,
         fields,
      });
      return data.data;
   }
}
