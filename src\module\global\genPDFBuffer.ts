import PDFDocument from 'pdfkit';
import { PassThrough } from 'stream';
import path from 'path';
import fs from 'fs';
import { GenerateInvoiceParams } from './Interface/genPDFBuffer';

// === Constants ===
const COLORS = {
    primary: '#1e40af',
    lightGray: '#f3f4f6',
    text: '#333',
    border: '#ccc',
    footer: '#555',
};
const FONTS = {
    normal: 'Helvetica',
    bold: 'Helvetica-Bold',
};
const SIZES = {
    title: 20,
    sectionHeader: 10,
    normal: 11,
    small: 9,
    footer: 14,
};
const PADDING = {
    cell: 8,
    tableTextY: 15,
    tableTextX: 10,
    tableRowHeight: 30,
    section: 40,
    addressBlock: 140,
    notesBox: 20,
    notesBoxHeight: 90,
    notesBoxY: 50,
    totalsCellHeight: 25,
    footerY: 40,
};

export class genPDFService {

    async generateInvoicePDFBuffer(params: GenerateInvoiceParams): Promise<Buffer> {
        return new Promise((resolve, reject) => {
            const doc = new PDFDocument({ size: 'A4', margins: { top: 40, bottom: 40, left: 40, right: 40 } });
            const stream = new PassThrough();
            const chunks: Buffer[] = [];

            stream.on('data', (chunk: Buffer) => chunks.push(chunk));
            stream.on('end', () => resolve(Buffer.concat(chunks)));
            stream.on('error', reject);

            doc.pipe(stream);

            const pageWidth = doc.page.width - doc.page.margins.left - doc.page.margins.right;
            const boxWidth = (pageWidth - 40) / 2;
            let y = doc.page.margins.top;

            // === Header ===
            y = this.drawHeader(doc, params, pageWidth, y);

            // === Company & Invoice Info ===
            y = this.drawCompanyAndInvoiceInfo(doc, params, pageWidth, boxWidth, y);

            // === Address Blocks ===
            y = this.drawAddressBlocks(doc, params, pageWidth, boxWidth, y);

            // === Table ===
            y = this.drawTable(doc, params, pageWidth, y);

            // === Notes & Totals ===
            y = this.drawNotesAndTotals(doc, params, pageWidth, boxWidth, y);

            // === Footer ===
            this.drawFooter(doc, params, pageWidth, y);

            doc.end();
        });
    }

    // --- Section Methods ---

    private drawHeader(doc: PDFKit.PDFDocument, params: GenerateInvoiceParams, pageWidth: number, y: number): number {
        doc.fillColor(COLORS.primary).font(FONTS.bold).fontSize(SIZES.title);
        doc.text(params.invoiceTitle, doc.page.margins.left, y, { width: pageWidth, align: 'center' });
        y += SIZES.title + 20;

        // Logo (Optional)
        const logoPath = path.join(__dirname, '../../../public/image.png');
        if (fs.existsSync(logoPath)) {
            doc.image(logoPath, doc.page.margins.left, y, { width: 120, height: 40 });
        }
        return y + 50;
    }

    private drawCompanyAndInvoiceInfo(doc: PDFKit.PDFDocument, params: GenerateInvoiceParams, pageWidth: number, boxWidth: number, y: number): number {
        // Company Info
        doc.fillColor(COLORS.text).font(FONTS.normal).fontSize(SIZES.normal);
        doc.text(params.companyInfo, doc.page.margins.left, y, { width: boxWidth });

        // Invoice Info
        const infoX = doc.page.margins.left + pageWidth - 200;
        const labelWidths = 120;
        const valueWidths = 100;
        const rowHeights = 16;
        params.invoiceInfo.forEach((item, i) => {
            const rowY = y + i * rowHeights;
            doc.font(FONTS.bold).fillColor(COLORS.text).fontSize(10)
                .text(item.label, infoX, rowY, { width: labelWidths, align: 'left' });
            doc.font(FONTS.normal)
                .text(item.value, infoX + labelWidths + 5, rowY, { width: valueWidths, align: 'left' });
        });
        return y + 100;
    }

    private drawAddressBlocks(doc: PDFKit.PDFDocument, params: GenerateInvoiceParams, pageWidth: number, boxWidth: number, y: number): number {
        // Billed To
        doc.fillColor(COLORS.primary).font(FONTS.bold).fontSize(SIZES.sectionHeader)
            .rect(doc.page.margins.left, y, boxWidth, 20).fill();
        doc.fillColor('white').text('Billed To', doc.page.margins.left + 5, y + 4);
        doc.fillColor(COLORS.text).font(FONTS.normal).fontSize(SIZES.normal);
        doc.text(params.billedTo, doc.page.margins.left, y + 28, { width: boxWidth });

        // Shipped To
        const shippedX = doc.page.margins.left + boxWidth + 40;
        doc.fillColor(COLORS.primary).font(FONTS.bold).fontSize(SIZES.sectionHeader)
            .rect(shippedX, y, boxWidth, 20).fill();
        doc.fillColor('white').text('Shipped To', shippedX + 5, y + 4);
        doc.fillColor(COLORS.text).font(FONTS.normal).fontSize(SIZES.normal);
        doc.text(params.shippedTo, shippedX, y + 28, { width: boxWidth });

        return y + PADDING.addressBlock;
    }

    private drawTable(doc: PDFKit.PDFDocument, params: GenerateInvoiceParams, pageWidth: number, y: number): number {
        // Table Headers
        console.log(params.items)
        const tableTop = y;
        const colDescWidth = pageWidth * 0.5;
        const colUnitCostWidth = pageWidth * 0.15;
        const colQtyWidth = pageWidth * 0.15;
        const colAmountWidth = pageWidth * 0.20;

        // Header background
        doc.fillColor(COLORS.primary)
            .rect(doc.page.margins.left, tableTop, colDescWidth, PADDING.tableRowHeight).fill()
            .rect(doc.page.margins.left + colDescWidth, tableTop, colUnitCostWidth, PADDING.tableRowHeight).fill()
            .rect(doc.page.margins.left + colDescWidth + colUnitCostWidth, tableTop, colQtyWidth, PADDING.tableRowHeight).fill()
            .rect(doc.page.margins.left + colDescWidth + colUnitCostWidth + colQtyWidth, tableTop, colAmountWidth, PADDING.tableRowHeight).fill();

        // Header text
        doc.fillColor('white').font(FONTS.bold).fontSize(SIZES.sectionHeader);
        doc.text('Description', doc.page.margins.left + PADDING.tableTextX, tableTop + PADDING.tableTextY - 7, { width: colDescWidth - 2 * PADDING.tableTextX });
        doc.text('Unit Cost', doc.page.margins.left + colDescWidth + PADDING.tableTextX, tableTop + PADDING.tableTextY - 7, { width: colUnitCostWidth - 2 * PADDING.tableTextX });
        doc.text('Quantity', doc.page.margins.left + colDescWidth + colUnitCostWidth + PADDING.tableTextX, tableTop + PADDING.tableTextY - 7, { width: colQtyWidth - 2 * PADDING.tableTextX, align: 'center' });
        doc.text('Amount', doc.page.margins.left + colDescWidth + colUnitCostWidth + colQtyWidth + PADDING.tableTextX, tableTop + PADDING.tableTextY - 7, { width: colAmountWidth - 2 * PADDING.tableTextX });

        y = tableTop + PADDING.tableRowHeight;

        // Table Rows
        params.items.forEach(item => {
            // Borders
            doc.lineWidth(1).strokeColor(COLORS.border);
            doc.rect(doc.page.margins.left, y, colDescWidth, PADDING.tableRowHeight).stroke();
            doc.rect(doc.page.margins.left + colDescWidth, y, colUnitCostWidth, PADDING.tableRowHeight).stroke();
            doc.rect(doc.page.margins.left + colDescWidth + colUnitCostWidth, y, colQtyWidth, PADDING.tableRowHeight).stroke();
            doc.rect(doc.page.margins.left + colDescWidth + colUnitCostWidth + colQtyWidth, y, colAmountWidth, PADDING.tableRowHeight).stroke();
            // Description (vertically centered)
            doc.fillColor(COLORS.text).font(FONTS.normal).fontSize(SIZES.normal);
            doc.text(item.description, doc.page.margins.left + PADDING.tableTextX, y + 4, {
                width: colDescWidth - 2 * PADDING.tableTextX,
            });
            // Unit Cost
            doc.text(item.unitCost, doc.page.margins.left + colDescWidth + PADDING.tableTextX, y + (PADDING.tableRowHeight - SIZES.normal) / 2, { width: colUnitCostWidth - 2 * PADDING.tableTextX });
            // Quantity
            doc.text(item.quantity, doc.page.margins.left + colDescWidth + colUnitCostWidth + PADDING.tableTextX, y + (PADDING.tableRowHeight - SIZES.normal) / 2, { width: colQtyWidth - 2 * PADDING.tableTextX, align: 'center' });
            // Amount
            doc.text(item.amount, doc.page.margins.left + colDescWidth + colUnitCostWidth + colQtyWidth + PADDING.tableTextX, y + (PADDING.tableRowHeight - SIZES.normal) / 2, { width: colAmountWidth - 2 * PADDING.tableTextX });
            y += PADDING.tableRowHeight;
        });
        return y + 20;
    }

    private drawNotesAndTotals(doc: PDFKit.PDFDocument, params: GenerateInvoiceParams, pageWidth: number, boxWidth: number, y: number): number {
        // Notes Box
        const notesX = doc.page.margins.left;
        const notesWidth = pageWidth * 0.4;
        const totalsWidth = pageWidth * 0.4;
        const totalsX = doc.page.margins.left + notesWidth + 30;

        doc.fillColor(COLORS.primary).font(FONTS.bold).fontSize(SIZES.sectionHeader)
            .rect(notesX, y, boxWidth, PADDING.notesBox).fill();
        doc.fillColor('white').text('Special Notes', notesX + 5, y + 4);
        doc.rect(notesX, y + PADDING.notesBox, boxWidth, PADDING.notesBoxHeight).fill(COLORS.lightGray);
        doc.fillColor(COLORS.text).font(FONTS.normal).fontSize(SIZES.small);
        doc.text(params.notes, notesX + 5, y + PADDING.notesBox + 20, { width: notesWidth, height: PADDING.notesBoxHeight });

        // Totals
        const cellHeight = PADDING.totalsCellHeight;
        const labelWidth = totalsWidth * 0.6;
        const valueWidth = totalsWidth * 0.6;
        const startY = y;
        const startX = totalsX + 35;
        const padding = 5;

        params.totals.forEach((row, i) => {
            const rowY = startY + i * cellHeight;
            doc.fillColor(COLORS.primary).rect(startX, rowY, labelWidth, cellHeight).fill();
            doc.strokeColor(COLORS.border).rect(startX, rowY, labelWidth, cellHeight).stroke();
            doc.fillColor('white').font(row.bold ? FONTS.bold : FONTS.normal).fontSize(SIZES.sectionHeader)
                .text(row.label, startX + padding, rowY + padding, { width: labelWidth - 2 * padding, align: 'right' });
            doc.fillColor('white').rect(startX + labelWidth, rowY, valueWidth, cellHeight).fill();
            doc.strokeColor(COLORS.border).rect(startX + labelWidth, rowY, valueWidth, cellHeight).stroke();
            doc.fillColor(COLORS.text).font(row.bold ? FONTS.bold : FONTS.normal).fontSize(SIZES.sectionHeader)
                .text(row.value, startX + labelWidth + padding, rowY + padding, { width: valueWidth - 3 * padding, align: 'right' });
        });
        return y + 120;
    }

    private drawFooter(doc: PDFKit.PDFDocument, params: GenerateInvoiceParams, pageWidth: number, y: number): void {
        const centerTextY = y + PADDING.footerY;
        doc.font(FONTS.bold).fontSize(SIZES.footer).fillColor(COLORS.footer);
        doc.text('Thank you for your business!', doc.page.margins.left, centerTextY, { width: pageWidth, align: 'center' });
        doc.font(FONTS.normal).fontSize(12);
        doc.text('Should you have any enquiries concerning this invoice, please contact.', doc.page.margins.left, centerTextY + 20, { width: pageWidth, align: 'center' });
        doc.fillColor(COLORS.primary);
        doc.text('Mail: ' + params.contact.email, doc.page.margins.left, centerTextY + 40, { width: pageWidth, align: 'center', link: 'mailto:' + params.contact.email });
        doc.text('Call: ' + params.contact.phone, doc.page.margins.left, centerTextY + 60, { width: pageWidth, align: 'center', link: 'tel:' + params.contact.phone });
        doc.text(params.contact.website, doc.page.margins.left, centerTextY + 80, { width: pageWidth, align: 'center', link: params.contact.website });
    }
}
