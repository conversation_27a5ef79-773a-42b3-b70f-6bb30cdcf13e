// services/redisService.ts
import { Config } from '../../config/index';
import Redis from 'ioredis';
import { logger } from '../../config/logger';

export class RedisServiceClass {
    private client: Redis;

    constructor() {
        this.client = new Redis({
            host: Config.REDIS_HOST,
            port: Config.REDIS_PORT,
            password: Config.REDIS_PASS
            //   tls: {},
        });

        this.client.on('connect', () => {
            logger.info('Redis connected');
        });

        this.client.on('error', (err) => {
            logger.error('Redis error:', err);
        });
    }

    private secondsUntilMidnight(): number {
        const now = new Date();
        const midnight = new Date(now);
        midnight.setHours(24, 0, 0, 0);
        return Math.floor((midnight.getTime() - now.getTime()) / 1000);
    }

    async get(key: string): Promise<string | null> {
        try {
            return await this.client.get(`${Config.REDIS_PREFIX}:${key}`);
        } catch (err) {
            logger.error(`Redis GET error for key "${key}":`, err);
            return null;
        }
    }

    async set(
        key: string,
        value: string,
        options?: { expireAtMidnight?: boolean; ttlInSeconds?: number }
    ): Promise<void> {
        try {
            if (options?.expireAtMidnight) {
                const ttl = this.secondsUntilMidnight();
                await this.client.set(`${Config.REDIS_PREFIX}:${key}`, value, 'EX', ttl);
            } else if (options?.ttlInSeconds) {
                await this.client.set(`${Config.REDIS_PREFIX}:${key}`, value, 'EX', options.ttlInSeconds);
            } else {
                await this.client.set(`${Config.REDIS_PREFIX}:${key}`, value); 
            }
        } catch (err) {
            logger.error(`Redis SET error for key "${Config.REDIS_PREFIX}:${key}":`, err);
        }
    }


    async del(key: string): Promise<void> {
        try {
            await this.client.del(`${Config.REDIS_PREFIX}:${key}`);
        } catch (err) {
            logger.error(`Redis DEL error for key "${Config.REDIS_PREFIX}:${key}":`, err);
        }
    }
}

const redisService = new RedisServiceClass();
export default redisService;
