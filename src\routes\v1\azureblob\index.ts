import { AzureService } from '../../../services/AzureService';
import express from 'express';
import multer from 'multer';

import { logger } from '../../../config/logger';
import { AzureController } from '../../../controllers/AzureController';
import asyncHandler from '../../../midddleware/async-handler'; // Fix the import statement
const router = express.Router();

const azureService = new AzureService(logger);
const azureController = new AzureController(azureService, logger);

const upload = multer({
   storage: multer.memoryStorage(),
   limits: { fileSize: 50 * 1024 * 1024 },
});

// decode blob data
router
   .route('/decode')
   .post(asyncHandler(azureController.handleDecodeBlob.bind(azureController)));

router
   .route('/encode')
   .post(
      upload.single('media'),
      asyncHandler(azureController.handleEncodeBlob.bind(azureController)),
   );

export { router as azureRouter };
