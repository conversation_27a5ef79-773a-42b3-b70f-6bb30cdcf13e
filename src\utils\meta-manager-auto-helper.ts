import { Config } from '../config';

import OpenAI from 'openai';

const openai = new OpenAI({
   apiKey: Config.OPENAI_API_KEY!,
});

export async function generateRunwayPrompt(
   caption: string,
   description: string,
): Promise<string> {
   const systemPrompt = `
You are an expert assistant for creating prompts for RunwayML's Gen-4 image generation model.

Your task is to write a single-line, visually descriptive, realistic prompt (under 470 characters) for ad creatives based on a product caption and description.

Your output must:
- Focus on one subject (e.g., child, woman, man) in a clear real-world or studio setting
- Use simple language with natural lighting and soft shadows
- Clearly describe the product’s appearance or setting
- Avoid quotes, text overlays, or typography instructions
- Mention product visibility, e.g., "centered and clearly visible"
- Match the look of real Instagram/Facebook ad images

Do not include: any direct text like “Shop Now”, quotes, or discount offers.
Output must be a single line and safe for image generation without errors.
`;

   const userPrompt = `Caption: ${caption}\nDescription: ${description}`;

   const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
         { role: 'system', content: systemPrompt },
         { role: 'user', content: userPrompt },
      ],
      temperature: 0.5,
   });

   return completion.choices?.[0]?.message?.content?.trim() ?? '';
}
