import { Request, Response } from 'express';
import { LoginService } from '../services/LoginService';
import { Logger } from 'winston';
import { LoginDto } from '../types';

export class LoginController {
   constructor(
      private loginService: LoginService,
      private logger: Logger,
   ) {}

   async handleLogin(req: Request, res: Response) {
      const { email, password } = req.body as LoginDto;
      const result = await this.loginService.login({
         email,
         password,
      });
      this.logger.info('User has been Logged in');
      res.status(200).send(result);
   }
}
