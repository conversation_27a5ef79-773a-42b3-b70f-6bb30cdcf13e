import {
   postOnLinkedin,
   fetchUserDetails,
   uploadImageToLinkedin,
   getUserOrganisations,
} from '../repositories/linkedin';
import { PostOnLinkedinPayload } from '../types';
import { Logger } from 'winston';

export class LinkedinService {
   constructor(private logger: Logger) {}

   async getSelfDetails(token: string) {
      const userDetails = await fetchUserDetails(token);

      if (!userDetails)
         throw new Error('Failed to fetch user details from linkedin');

      return userDetails;
   }
   async getUserPages(token: string) {
      const data = await getUserOrganisations(token);
      if (!data) throw new Error('No pages to fetch');
      return data;
   }
   async postOnLinkedin(payload: PostOnLinkedinPayload) {
      const { content, token, linkedinUserId, mediaId, pageId } = payload;

      const postId = await postOnLinkedin(
         content,
         token,
         linkedinUserId,
         pageId,
         mediaId,
      );

      if (!postId) throw new Error('Failed to post on linkedin');

      return postId;
   }

   async uploadImage(payload: {
      imageBuffer: Express.Multer.File;
      token: string;
      userId: string;
   }) {
      const { token, userId, imageBuffer } = payload;
      return uploadImageToLinkedin(imageBuffer, token, userId);
   }
}
