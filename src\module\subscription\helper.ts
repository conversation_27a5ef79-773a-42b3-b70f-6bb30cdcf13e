import { ToWords } from "to-words";
import { ClientDTO, InvoiceDTO, TopupItemsDTO } from "./types";

const toWords = new ToWords()

export function getCompanyInfo(): string {
    return `KEINSA export function LIMITED (Flable AI), 104 Flat, B Building, Meda Eletrnity Road, Kithaganur, BENGALURU Urban, Karnataka,560049, India, GSTIN: 29AAJCK2776R1ZH`;
}

export function getInvoiceInfo(invoice: InvoiceDTO) {
    const formattedDate = formatDate(invoice.created_dt);
    return [
        { label: 'Date:', value: formattedDate },
        { label: 'Invoice ID:', value: '200211' },
        { label: 'Customer ID:', value: invoice.client_id },
        { label: 'Payment done on:', value: formattedDate }
    ];
}

export function getAddressBlock(client: ClientDTO): string {
    return `${client.billing_address as string} ${client.gstin ? `GSTIN: ${client.gstin}` : ''}`;
}

export function formatDate(date: string | Date): string {
    return new Date(date).toLocaleDateString('en-GB', { timeZone: 'UTC' }).replace(/\//g, '-');
}

export function buildInvoiceItems(invoice: InvoiceDTO, items: TopupItemsDTO[]): { description: string; unitCost: string; quantity: string; amount: string; }[] {
    if (invoice.is_topup) {
        return items.map(item => ({
            description: `Top-up for ${item.agent_name}`,
            unitCost: `$${item.amount}`,
            quantity: `${item.tokens_added}`,
            amount: `$${item.amount}`
        }));
    } else {
        const formatter = new Intl.DateTimeFormat('en-US', {
            month: 'long',
            year: 'numeric',
            timeZone: 'UTC'
        });
        return [{
            description: `Subscription fees for plan ${invoice.plan_name} from ${formatter.format(new Date(invoice.billing_start_dt)).toUpperCase()} to ${formatter.format(new Date(invoice.billing_end_dt)).toUpperCase()}`,
            unitCost: `$${invoice.amount}`,
            quantity: '1',
            amount: `$${invoice.amount}`
        }];
    }
}

export function buildInvoiceNotes(invoice: InvoiceDTO): string {
    const formatter = new Intl.DateTimeFormat('en-US', {
        month: 'long',
        year: 'numeric',
        timeZone: 'UTC'
    });
    const readableAmount = toWords.convert(invoice.amount);
    const dateRange = `from ${formatter.format(new Date(invoice.billing_start_dt)).toUpperCase()} to ${formatter.format(new Date(invoice.billing_end_dt)).toUpperCase()}`;
    return invoice.is_topup
        ? `USD ${readableAmount} for Topup under plan ${invoice.plan_name} ${dateRange}`
        : `USD ${readableAmount} for plan ${invoice.plan_name} ${dateRange}`;
}

export function getInvoiceTotals(amount: number | string) {
    return [
        { label: 'Subtotal:', value: `$${amount}` },
        { label: 'Total:', value: `$${amount}`, bold: true }
    ];
}

export function getCompanyContact() {
    return {
        email: '<EMAIL>',
        phone: '+91 7769975889',
        website: 'https://flable.ai'
    };
}