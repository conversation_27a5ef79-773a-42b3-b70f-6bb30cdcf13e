import { DatabaseError } from 'pg';
import { <PERSON>tity<PERSON>anager } from 'typeorm';
import { Config } from '../../config';
import * as types from '../../types/notifications/notifications.types';

export class NotificationsModel {
   constructor(private entityManager: EntityManager) {}

   async fetchAllNotificationsByUserID(
      payload: types.FetchAllNotificationsByUserIDPayload,
   ): Promise<types.Notification[]> {
      try {
         const notifications = await this.entityManager.query(
            `SELECT * FROM ${Config.DB_Postgres_CONFIG_SCHEMA}.user_notifications WHERE client_id = $1 AND user_id = $2 ORDER BY created_at DESC`,
            [payload.client_id, payload.user_id],
         );

         return notifications;
      } catch (error) {
         if (error instanceof DatabaseError) {
            console.error('Database error:', error.message);
         } else {
            console.error('Unexpected error:', error);
         }
         return [];
      }
   }

   async createNotification(
      payload: types.CreateNotificationPayload,
   ): Promise<void> {
      try {
         await this.entityManager.query(
            `INSERT INTO ${Config.DB_Postgres_CONFIG_SCHEMA}.user_notifications (client_id, user_id, notification_title, notification_message, notification_type, notification_data) VALUES ($1, $2, $3, $4, $5, $6)`,
            [
               payload.client_id,
               payload.user_id,
               payload.notification_title,
               payload.notification_message,
               payload.notification_type,
               payload.notification_data,
            ],
         );
      } catch (error) {
         if (error instanceof DatabaseError) {
            console.error('Database error:', error.message);
         } else {
            console.error('Unexpected error:', error);
         }
      }
   }

   async markNotificationAsRead(
      payload: types.MarkNotificationAsReadPayload,
   ): Promise<void> {
      try {
         await this.entityManager.query(
            `UPDATE ${Config.DB_Postgres_CONFIG_SCHEMA}.user_notifications SET is_read = true, read_at = NOW() WHERE client_id = $1 AND user_id = $2 AND id = $3`,
            [payload.client_id, payload.user_id, payload.id],
         );
      } catch (error) {
         if (error instanceof DatabaseError) {
            console.error('Database error:', error.message);
         } else {
            console.error('Unexpected error:', error);
         }
      }
   }
}
