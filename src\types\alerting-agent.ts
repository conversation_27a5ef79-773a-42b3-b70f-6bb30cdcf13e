export interface Alerts {
   alert_id: string;
   client_id: string;
   chat_id: string;
   alert_name: string;
   alert_description: string;
   alert_instruction: string;
   recipients: string;
   channel: string;
   campaign_name: string;
   campaign_id: string;
   kpi: string;
   trend: string;
   value: string;
   value_type: string;
   comparison: string;
   comparison_type: string;
   alert_status: string;
   alert_time: string;
   alert_timeframe: string;
   created_at: string;
   updated_at: string;
}

export interface CreateAlertPayload {
   client_id: string;
   chat_id: string;
   alert_name: string;
   alert_description: string;
   alert_instruction: string;
   recipients: string;
   channel: string;
   campaign_name: string;
   campaign_id: string;
   kpi: string;
   trend: string;
   value: string;
   value_type: string;
   comparison: string;
   comparison_type: string;
   alert_status: string;
   alert_time: string;
   alert_timeframe: string;
}

export interface UpdateAlertPayload extends CreateAlertPayload {
   alert_id: string;
}

export interface UpdateEmailRecipientsPayload {
   alert_id: string;
   recipients: string[];
}

export interface CheckAlertPayload {
   alerts: Alerts[];
}

export interface DashboardKPIS {
   client_id: string;
   category: string;
   kpi_names: string;
   date: string;
   load_date_and_time: string;
   last_updated_date: string;
   kpi_value: number;
   kpi_unit: string;
   kpi_type: string;
   kpi_display_name: string;
}

export interface ChatDetails {
   msg_id: string;
   role: 'user' | 'agent';
   content: string;
   response_type?: 'text' | 'card';
   sent_at: string;
}

export interface ChatHistory {
   chat_id: string;
   client_id: string;
   session_id: string;
   chat_name: string;
   chat_history: ChatDetails[];
   created_at: string;
   updated_at: string;
}

export interface AddToHistoryPayload {
   chat_id: string;
   client_id: string;
   session_id: string;
   chat_name: string | null;
   chat_history: ChatDetails[];
}

export interface UpdateChatHistoryPayload {
   chat_id: string;
   chat_history: ChatDetails[];
}

export interface ChatHistoryResponse {
   status: string;
   message: string;
   data: ChatHistory[];
}