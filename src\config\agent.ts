import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { Config } from '.';
import { logger } from './logger';

interface AirbyteError {
   response: {
      status: number;
      data: { message: string; detail: string; data: { message: string } };
   };
}

// Create an Axios instance
const axiosInstance = axios.create({ baseURL: 'https://api.airbyte.com/v1' });

async function getAirbyteAccessToken() {
   const url = '/applications/token';

   const payload = {
      'grant-type': 'client_credentials',
      client_id: Config.AIRBYTE_CLIENT_ID!,
      client_secret: Config.AIRBYTE_CLIENT_SECRET!,
   };

   try {
      const response = await axios.post<{
         access_token: string;
         expires_in: number;
      }>(url, payload, {
         baseURL: 'https://api.airbyte.com/v1',
      });
      return response.data;
   } catch (error) {
      logger.error('Error fetching access token:', error);
      throw error;
   }
}

let cachedToken: string | null = null;
let tokenExpiration: number | null = null;

async function getCachedAccessToken(): Promise<string> {
   if (cachedToken && tokenExpiration && Date.now() < tokenExpiration) {
      logger.info('returning cached token', cachedToken);
      return cachedToken;
   }
   logger.info('fetching new token');
   const tokenResponse = await getAirbyteAccessToken();
   cachedToken = tokenResponse.access_token;
   tokenExpiration = Date.now() + tokenResponse.expires_in * 1000;
   return cachedToken;
}

axiosInstance.interceptors.request.use(
   async (config) => {
      try {
         const token = await getCachedAccessToken();
         if (token) {
            config.headers.Authorization = `Bearer ${token}`;
         }
      } catch (error) {
         logger.error('Error setting Authorization header:', error);
         throw error;
      }
      return config;
   },
   (error) => {
      return Promise.reject(error);
   },
);

function throwAirbyteError(error: unknown) {
   const err = error as AirbyteError;
   const msg = err.response.data.message || err.response.data.detail;
   logger.error(msg);
   return `Failed to make the request: ${msg} `;
}

async function makeGetRequest<T>(
   url: string,
   options?: AxiosRequestConfig,
): Promise<T> {
   try {
      const res: AxiosResponse<T> = await axiosInstance.get<T>(url, options);
      return res.data;
   } catch (error: unknown) {
      throw new Error(throwAirbyteError(error));
   }
}

async function makePostRequest<T>(
   url: string,
   data?: Record<string, string | number | null | object>,
   options?: AxiosRequestConfig,
): Promise<T> {
   try {
      const res: AxiosResponse<T> = await axiosInstance.post<T>(
         url,
         data,
         options,
      );
      return res.data;
   } catch (error: unknown) {
      throw new Error(throwAirbyteError(error));
   }
}

async function makePatchRequest<T>(
   url: string,
   data?: Record<string, string | number | null | object>,
   options?: AxiosRequestConfig,
): Promise<T> {
   try {
      const res: AxiosResponse<T> = await axiosInstance.patch<T>(
         url,
         data,
         options,
      );
      return res.data;
   } catch (error: unknown) {
      throw new Error(throwAirbyteError(error));
   }
}

async function makePutRequest<T>(
   url: string,
   data?: Record<string, string | number | null | object>,
   options?: AxiosRequestConfig,
): Promise<T> {
   try {
      const res: AxiosResponse<T> = await axiosInstance.put<T>(
         url,
         data,
         options,
      );
      return res.data;
   } catch (error: unknown) {
      throw new Error(throwAirbyteError(error));
   }
}

async function makeDeleteRequest<T>(
   url: string,
   options?: AxiosRequestConfig,
): Promise<T> {
   try {
      const res: AxiosResponse<T> = await axiosInstance.delete<T>(url, options);
      return res.data;
   } catch (error: unknown) {
      throw new Error(throwAirbyteError(error));
   }
}

export {
   makeGetRequest,
   makePostRequest,
   makePatchRequest,
   makePutRequest,
   makeDeleteRequest,
};
