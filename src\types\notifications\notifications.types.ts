/** MISCELLANEOUS */

export interface Notification {
   id: string;
   client_id: string;
   user_id: string;
   notification_title: string;
   notification_message: string;
   notification_type: string;
   notification_data: Record<string, unknown>;
   is_read: boolean;
   read_at: Date | null;
   created_at: Date;
   updated_at: Date;
}

/** PAYLOADS **/

export interface FetchAllNotificationsByUserIDPayload {
   user_id: string;
   client_id: string;
}

export interface CreateNotificationPayload {
   client_id: string;
   user_id: string;
   notification_title: string;
   notification_message: string;
   notification_type: string;
   notification_data: Record<string, unknown>;
}

export interface MarkNotificationAsReadPayload {
   user_id: string;
   client_id: string;
   id: string;
}
