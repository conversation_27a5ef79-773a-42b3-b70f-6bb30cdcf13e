import crypto from 'crypto';
import FormData from 'form-data';
import OAuth from 'oauth-1.0a';
import axios from 'axios';
import ContentCalendarSchema from '../entity/ContentCalendar';

import { Config } from '../config/index';
import {
   ContentCalendarDto,
   PostTweetDto,
   GetContentCalendarDto,
   GetCaptionPayload,
} from '../types';
import { postTweet } from '../repositories/twitter';
import { Logger } from 'winston';
// import { clientMongo } from './../config/mongoDb';

export function getTwitterAuth() {
   const consumerKey = Config.TWITTER_CONSUMER_KEY;
   const consumerSecret = Config.TWITTER_CONSUMER_SECRET;

   const oauth = new OAuth({
      consumer: { key: consumerKey || '', secret: consumerSecret || '' },
      signature_method: 'HMAC-SHA1',
      hash_function(base_string: string, key: string) {
         return crypto
            .createHmac('sha1', key)
            .update(base_string)
            .digest('base64');
      },
   });

   return oauth;
}

export function getAuthHeader(
   url: string,
   oauthToken: string,
   oauthTokenSecret: string,
) {
   const oauth = getTwitterAuth();

   const token = {
      key: oauthToken,
      secret: oauthTokenSecret,
   };

   const authHeader = oauth.toHeader(
      oauth.authorize(
         {
            url,
            method: 'POST',
         },
         token,
      ),
   );
   return authHeader;
}

export class TwitterService {
   constructor(private logger: Logger) {}
   postContentCalendar(payload: ContentCalendarDto) {
      return ContentCalendarSchema.create(payload);
   }

   async uploadVideoToTwitter(
      payload: Express.Multer.File,
      oauthToken: string,
      oauthTokenSecret: string,
   ) {
      if (!payload.buffer) {
         throw new Error('Invalid or missing file buffer');
      }

      const mediaEndpointURL =
         'https://upload.twitter.com/1.1/media/upload.json';
      const authHeader = getAuthHeader(
         mediaEndpointURL,
         oauthToken,
         oauthTokenSecret,
      );

      const initForm = new FormData();
      initForm.append('command', 'INIT');
      initForm.append('media_type', payload.mimetype);
      initForm.append('total_bytes', payload.size.toString());
      initForm.append('media_category', 'tweet_video');

      const initResponse = await axios.post<{ media_id_string: string }>(
         mediaEndpointURL,
         initForm,
         {
            headers: {
               Authorization: authHeader['Authorization'],
               ...initForm.getHeaders(),
            },
         },
      );

      const mediaId = initResponse.data.media_id_string;

      const appendForm = new FormData();
      appendForm.append('command', 'APPEND');
      appendForm.append('media_id', mediaId);
      appendForm.append('segment_index', '0');
      appendForm.append('media', payload.buffer, {
         filename: payload.originalname,
      });

      await axios.post(mediaEndpointURL, appendForm, {
         headers: {
            Authorization: authHeader['Authorization'],
            ...appendForm.getHeaders(),
         },
      });

      const finalizeForm = new FormData();
      finalizeForm.append('command', 'FINALIZE');
      finalizeForm.append('media_id', mediaId);

      const finalizeResponse = await axios.post<{ media_id_string: string }>(
         mediaEndpointURL,
         finalizeForm,
         {
            headers: {
               Authorization: authHeader['Authorization'],
               ...finalizeForm.getHeaders(),
            },
         },
      );

      return finalizeResponse.data.media_id_string;
   }

   async uploadImageToTwitter(
      payload: Express.Multer.File,
      oauthToken: string,
      oauthTokenSecret: string,
   ) {
      if (!payload.buffer) {
         throw new Error('Invalid or missing file buffer');
      }

      const form = new FormData();
      form.append('media', payload.buffer, { filename: payload.originalname });

      const mediaEndpointURL =
         'https://upload.twitter.com/1.1/media/upload.json';
      const authHeader = getAuthHeader(
         mediaEndpointURL,
         oauthToken,
         oauthTokenSecret,
      );
      const request = await axios.post<{ media_id_string: string }>(
         mediaEndpointURL,
         form,
         {
            headers: {
               Authorization: authHeader['Authorization'],
               ...form.getHeaders(),
            },
         },
      );
      const mediaData = request.data;
      return mediaData.media_id_string;
   }

   async uploadMediaToTwitter(
      payload: Express.Multer.File,
      oauthToken: string,
      oauthTokenSecret: string,
   ) {
      if (!payload.buffer) {
         throw new Error('Invalid or missing file buffer');
      }

      if (payload.mimetype.startsWith('image/')) {
         return await this.uploadImageToTwitter(
            payload,
            oauthToken,
            oauthTokenSecret,
         );
      } else if (payload.mimetype.startsWith('video/')) {
         return this.uploadVideoToTwitter(
            payload,
            oauthToken,
            oauthTokenSecret,
         );
      } else {
         throw new Error('Unsupported media type');
      }
   }

   async postTweets(payload: PostTweetDto) {
      const endpointURL = `https://api.twitter.com/2/tweets`;
      const { oauthToken, tweets, oauthTokenSecret, mediaId } = payload;

      const authHeader = getAuthHeader(
         endpointURL,
         oauthToken,
         oauthTokenSecret,
      );

      await postTweet(tweets, authHeader, mediaId);
   }

   async getContentCalendar(payload: GetContentCalendarDto) {
      const { client_id, social_media_types } = payload;
      const result = await ContentCalendarSchema.find({
         client_id: client_id,
         social_media_type: { $in: social_media_types },
      });
      return result;
   }
   async deleteTweets(payload: { uuid: string }) {
      const { uuid } = payload;
      const result = await ContentCalendarSchema.deleteOne({ uuid });
      if (result.deletedCount === 0) {
         throw new Error('No content found');
      }
      return {
         status: 200,
         message: 'Content Calendar deleted successfully',
         data: result,
      };
   }
   async updateContentCalendar(
      payload: Partial<ContentCalendarDto> & { uuid: string },
   ) {
      const { uuid, ...updateFields } = payload;

      const updatedTypedFields = updateFields as Partial<ContentCalendarDto>;

      if (!uuid) {
         throw new Error('UUID is required to update content calendar');
      }
      const newValues: Record<
         string,
         | string
         | boolean
         | null
         | Date
         | { media_id: string; image_link: string }[]
      > = {};
      (
         Object.keys(updatedTypedFields) as Array<keyof ContentCalendarDto>
      ).forEach((key) => {
         const item = updatedTypedFields[key];
         if (item !== undefined) {
            newValues[key] = item;
         }
      });
      const query = { uuid };
      const update = { $set: newValues };

      try {
         const result = await ContentCalendarSchema.findOneAndUpdate(
            query,
            update,
            { new: true },
         );

         this.logger.info(
            `Content calendar with UUID: ${uuid} updated successfully`,
         );

         return result;
      } catch (error) {
         this.logger.error(
            `Error updating content calendar with UUID: ${uuid}`,
            error,
         );
         throw new Error('Error updating content calendar');
      }
   }

   async getCaption(payload: GetCaptionPayload) {
      const response = await axios.post<{ captions: string[] }>(
         `${Config.COPILOT_URL}`,
         payload,
         {
            headers: {
               'Content-Type': 'application/json',
               Authorization: `Bearer ${Config.GPT_BEARER}`,
            },
         },
      );
      const captions = response.data.captions;
      if (!captions) {
         throw new Error('No caption found');
      }
      return captions;
   }
}
