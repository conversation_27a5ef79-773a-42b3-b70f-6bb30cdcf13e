import { Request, Response } from 'express';
import { GoogleAdsService } from '../services/GoogleAdsService';
import * as types from '../types';
import { Logger } from 'winston';

export class GoolgeAdsController {
   constructor(
      private googleAdsService: GoogleAdsService,
      private logger: Logger,
   ) {}

    async fetchChannelType(req: Request, res: Response) {
        const { client_id } = req.body as types.getDynamicChannelTypes;
        const result =
            await this.googleAdsService.getChannelType(
                client_id
            );

      if (result) {
         res.status(200).json(result);
      } else {
         res.status(404).json({ message: 'No data found' });
      }
   }

   async fetchKPIsNames(req: Request, res: Response) {
      const { client_id, channel_type } = req.body as types.getDynamicKpisName;
      const result = await this.googleAdsService.getKpisNames(
         client_id,
         channel_type,
      );

      if (result) {
         res.status(200).json(result);
      } else {
         res.status(404).json({ message: 'No data found' });
      }
   }

   async fetchCampaignsDaywise(req: Request, res: Response) {
      const {
         client_id,
         channel_type,
         kpis,
         start_date,
         end_date,
         prev_start_date,
         prev_end_date,
      } = req.body as types.getCampignDaywise;

      const result = await this.googleAdsService.getCampaignsDaywise(
         client_id,
         channel_type,
         kpis,
         start_date,
         end_date,
         prev_start_date,
         prev_end_date,
      );

      if (result) {
         res.status(200).json(result);
      } else {
         res.status(404).json({ message: 'No data found' });
      }
   }

   async fetchCampaignsKpiwise(req: Request, res: Response) {
      const {
         client_id,
         campaign_id,
         channel_type,
         kpis,
         start_date,
         end_date,
      } = req.body as types.getCampaignKpiwise;

      const result = await this.googleAdsService.getCampaignsKpiwise(
         client_id,
         campaign_id,
         channel_type,
         kpis,
         start_date,
         end_date,
      );

      if (result) {
         res.status(200).json(result);
      } else {
         res.status(404).json({ message: 'No data found' });
      }
   }

   async updateTrackedKpis(req: Request, res: Response) {
      const { client_id, objective, kpi_name, campaign_id, tracked, channel } =
         req.body as types.UpdateUserTrack;
      const response = await this.googleAdsService.updateUserTrack(
         client_id,
         objective,
         kpi_name,
         campaign_id,
         tracked,
         channel,
      );
      if (response) {
         this.logger.info('Updated successfully');
         res.status(200).json(response);
      } else {
         this.logger.error('Error while updating');
      }
   }

   async fetchAdgroups(req: Request, res: Response) {
      const {
         client_id,
         campaign_id,
         channel_type,
         kpis,
         start_date,
         end_date,
         prev_start_date,
         prev_end_date,
      } = req.body as types.getCampaignKpiwise;

      const result = await this.googleAdsService.getAdgroups(
         client_id,
         campaign_id,
         channel_type,
         kpis,
         start_date,
         end_date,
         prev_start_date,
         prev_end_date,
      );

      if (result) {
         res.status(200).json(result);
      } else {
         res.status(404).json({ message: 'No data found' });
      }
   }

   async fetchAds(req: Request, res: Response) {
      const {
         client_id,
         campaign_id,
         ad_group_id,
         channel_type,
         kpis,
         start_date,
         end_date,
         prev_start_date,
         prev_end_date,
      } = req.body as types.getAds;

      const result = await this.googleAdsService.getAds(
         client_id,
         campaign_id,
         ad_group_id,
         channel_type,
         kpis,
         start_date,
         end_date,
         prev_start_date,
         prev_end_date,
      );

      if (result) {
         res.status(200).json(result);
      } else {
         res.status(404).json({ message: 'No data found' });
      }
   }

   async fetchCampaignChartSummary(req: Request, res: Response) {
      const response = await this.googleAdsService.getCampaignChartSummary(
         req.body,
      );

      if (response) {
         this.logger.info('Data fetched successfully');
         res.status(200).json(response);
      } else {
         this.logger.error('Error while fetching summary');
      }
   }

   async fetchCampaignAdgroupSummary(req: Request, res: Response) {
      const response = await this.googleAdsService.getCampaignAdgroupSummary(
         req.body,
      );

      if (response) {
         this.logger.info('Data fetched successfully');
         res.status(200).json(response);
      } else {
         this.logger.error('Error while fetching summary');
      }
   }
   async fetchAdgroupKeywordSummary(req: Request, res: Response) {
      const response = await this.googleAdsService.getAdgroupKeywordSSummary(
         req.body,
      );

      if (response) {
         this.logger.info('Data fetched successfully');
         res.status(200).json(response);
      } else {
         this.logger.error('Error while fetching summary');
      }
   }
   async fetchAdgroupGraphSummary(req: Request, res: Response) {
      const response = await this.googleAdsService.getAdgroupGraphSummary(
         req.body,
      );

      if (response) {
         this.logger.info('Data fetched successfully');
         res.status(200).json(response);
      } else {
         this.logger.error('Error while fetching summary');
      }
   }

   async fetchAdgroupSummary(req: Request, res: Response) {
      const response = await this.googleAdsService.getAdgroupSummary(req.body);

      if (response) {
         this.logger.info('Data fetched successfully');
         res.status(200).json(response);
      } else {
         this.logger.error('Error while fetching summary');
      }
   }

   async fetchKeywords(req: Request, res: Response) {
      const {
         client_id,
         campaign_id,
         ad_group_id,
         kpis,
         start_date,
         end_date,
      } = req.body as types.getAds;

      const result = await this.googleAdsService.getKeywords(
         client_id,
         campaign_id,
         ad_group_id,
         kpis,
         start_date,
         end_date,
      );

      if (result) {
         res.status(200).json(result);
      } else {
         res.status(404).json({ message: 'No data found' });
      }
   }
   async fetchTrackedkpis(req: Request, res: Response) {
      const { client_id, channel, start_date, end_date, groupBy } =
         req.body as types.GetTrackedKpis;
      const response = await this.googleAdsService.getTrackedKpis(
         client_id,
         channel,
         start_date,
         end_date,
         groupBy,
      );
      if (response) {
         this.logger.info('Data fetched successfully');
         res.status(200).json(response);
      } else {
         this.logger.error('Error while fetching data');
      }
   }
   async fetchTrackedPrevkpis(req: Request, res: Response) {
      const {
         client_id,
         channel,
         groupBy,
         start_date,
         end_date,
         prev_start_date,
         prev_end_date,
      } = req.body as types.GetTrackedPrevKpis;
      const response = await this.googleAdsService.getTrackedPrevKpis(
         client_id,
         channel,
         groupBy,
         start_date,
         end_date,
         prev_start_date,
         prev_end_date,
      );
      if (response) {
         this.logger.info('Data fetched successfully');
         res.status(200).json(response);
      } else {
         this.logger.error('Error while fetching data');
      }
   }
}
