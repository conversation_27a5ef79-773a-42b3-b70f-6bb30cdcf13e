export interface TwitterAuthDto {
   clientID: string;
   oauthToken: string;
   oauthTokenSecret: string;
   login: boolean;
}

export interface ContentCalendarDto {
   time: string;
   date: string;
   social_media_type: SocialMediaType;
   post_data: string;
   client_id: string;
   uuid: string;
   is_schedule?: boolean;
   linkedinPageId?: string | null;
   media?: { media_id: string; image_link: string }[];
}

export interface PostTweetDto {
   oauthToken: string;
   tweets: string;
   oauthTokenSecret: string;
   mediaId?: string;
}

export interface GetContentCalendarDto {
   client_id: string;
   social_media_types: string[];
}

export interface GetCaptionPayload {
   client_id: string;
   social_channel: string;
   reference_text: string;
   tone: string;
   no_hashtags: string;
   word_size: string;
   top_posts: string;
   top_competitor_posts: string;
   top_hashtags: string;
}

export enum SocialMediaType {
   Twitter = 'twitter',
   Facebook = 'facebook',
   Instagram = 'instagram',
   LinkedIn = 'linkedin',
   YouTube = 'youtube',
}
