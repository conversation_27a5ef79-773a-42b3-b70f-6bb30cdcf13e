import { KPIService } from '@/services/KPIService';
import {
   KPIMetaPayload,
   KPIPayload,
   KPISummaryPayload,
   PinPayload,
   PinVisibleOrderPayload,
   VisblePayload,
} from '@/types/kpi';
import { Request, Response } from 'express';
import { Logger } from 'winston';

export class KPIController {
   constructor(
      private kpiService: KPIService,
      private logger: Logger,
   ) { }
   async getKpiData(req: Request, res: Response) {
      const payload = req.body as KPIPayload;
      const result = await this.kpiService.getKpiData(payload);
      this.logger.info(`Got KPI data for client ${payload.clientId}`);
      res.status(200).send(result);
   }
   async getGPTSummary(req: Request, res: Response) {
      const payload = req.body as KPISummaryPayload;
      const result = await this.kpiService.getGPTSummary(
         payload.data,
         payload.kpi,
         payload.category,
         payload.currency
      );
      this.logger.info('GPT***********', result);
      res.status(200).send(result.response);
   }
   async updatePinned(req: Request, res: Response) {
      const payload = req.body as PinPayload;
      const result = await this.kpiService.updatePinned(payload);
      res.status(200).send(result);
   }
   async updateVisible(req: Request, res: Response) {
      const payload = req.body as VisblePayload;
      const result = await this.kpiService.updateVisible(payload);
      res.status(200).send(result);
   }
   async getMeta(req: Request, res: Response) {
      const payload = { clientId: req.params.clientId } as KPIMetaPayload;
      const result = await this.kpiService.getMeta(payload);
      res.status(200).send(result);
   }
   async updateVisibleOrder(req: Request, res: Response) {
      const payload = req.body as PinVisibleOrderPayload;
      const result = await this.kpiService.updatePinVisibleOrder(payload);
      res.status(200).send(result);
   }

   async fetchAnomalyRootCause(req: Request, res: Response) {
      const {client_id, start_date, end_date, kpi } = req.body

      const response = await this.kpiService.getAnomalyRootCause(
         client_id, start_date, end_date, kpi
      );

      if (response) {
         this.logger.info('Data fetched successfully');
         res.status(200).json(response);
      }
      else {
         this.logger.error('Error while fetching summary');
      }

   }
}
