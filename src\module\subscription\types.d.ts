//#region RAZORPAY
interface RazorApiResponse {
    status: number;
    statusText: string;
    headers: never;
    config: never;
    request: never;
}

export interface RazorpayCustomerApiResponse extends RazorApiResponse {
    data: {
        id: string;
        notes: {
            client_id: string;
        };
    };
}

export interface RazorpayOrderApiResponse extends RazorApiResponse {
    data: {
        id: string;
    };
}

export interface RazorpaySubscriptionApiResponse extends RazorApiResponse {
    data: {
        id: string;
        entity: "subscription";
        plan_id: string;
        customer_id: string;
        status: string;
        current_start: number | null;
        current_end: number | null;
        ended_at: number | null;
        quantity: number;
        notes: {
            plan_id: string;
            plan_name: string;
            client_id: string;
            extra_connectors: string;
            max_users: string;
        };
        charge_at: number | null;
        start_at: number | null;
        end_at: number | null;
        auth_attempts: number;
        total_count: number;
        paid_count: number;
        customer_notify: boolean;
        created_at: number;
        expire_by: number | null;
        short_url: string;
        has_scheduled_changes: boolean;
        change_scheduled_at: number | null;
        source: string;
        remaining_count: number;
    }
}

export interface RazorpayPaymentEntity {
    amount: number;
    vpa: string;
    token_id: string;
    description: string;
    contact: string;
    email: string;
    notes: Notes;
    customer_id: string;
    currency: string;
    method: string;
    order_id: string;
    id: string;
    status: string;
}

export interface RazorpayOrderEntity {
    id: string;
    status: string;
    notes: Notes;
    customer_id: string;
    currency: string;
    method: string;
}

export interface RazorpaySubscriptionEntity {
    notes: Notes;
}

export interface SubscriptionWebhookEvent {
    event: string;
    payload: {
        order: {
            entity: RazorpayOrderEntity;
        };
        payment: {
            entity: RazorpayPaymentEntity;
        };
        subscription: {
            entity: RazorpaySubscriptionEntity;
        };
    };
}

// #region CONTROLLER
export type VerificationInput =
    | {
        type: 'order';
        razorpay_order_id: string;
        razorpay_payment_id: string;
        razorpay_signature: string;
    }
    | {
        type: 'subscription';
        razorpay_subscription_id: string;
        razorpay_payment_id: string;
        razorpay_signature: string;
    };

export type CreateCustomerPayload = {
    name: string;
    contact?: string | null;
    gstin?: string | null;
    client_id: string;
    email: string;
    billing_address: string;
}

interface Agent {
    level: string | null;
    access: boolean;
    value: number;
}

interface DashboardIntegrations {
    amazon_ads: boolean;
    amazon_selling_partner: boolean;
    meta_ads: boolean;
    shopify_store: boolean;
    web_analytics: boolean;
    hubspot: boolean;
    unicommerce: boolean;
    google_ads: boolean;
    google_search_console: boolean;
    social_media: {
        x: boolean;
        linkedin: boolean;
    };
}

interface Pulse {
    performance_insights: boolean;
    web_analytics: boolean;
}

export interface Attributes {
    agents: {
        alerting_agent: Agent;
        diagnostic_agent: Agent;
        analytics_agent: Agent;
        creative_agent: Agent;
        content_agent: Agent;
        performance_agent: Agent;
        seo_agent: Agent;
    };
    dashboard_integrations: DashboardIntegrations;
    pulse: Pulse;
    user_limit: number;
}

export type CreateRazorpaySubscriptionPayload = {
    plan_id: string;
    plan_name: string;
    client_id: string;
    client_Phone: string | null;
    client_Email: string;
    rzrPay_PlanId: string;
    rzrPay_ClientId: string;
    isYearly: boolean;
    amount: string;
    attribute: Attributes;
    autoRenew: boolean;
    extra_connectors: number;
    max_users: number;
    currency: string;
}

export interface AgentTopupPayload {
    id: string;
    name: string;
    tokens: number;
    pricePerToken: number;
    totalPrice: number;
}

export type CreateOrderPayload = {
    client_id: string;
    amount: number;
    currency: string;
    topup?: boolean;
    agents?: AgentTopupPayload[];
}


// #region SERVICE

export interface Plans {
    id: string;
    name: string;
    tier: number;
    monthly_price: string;
    yearly_price: string;
    plan_info: string;
    plan_desc: string;
    plan_feature: string[];
    action_label: string;
    monthly_ad_spend: string;
    monthly_revenue: string;
    agentic_fit: string;
    attribute: Attributes;
    period: number;
    currency: string;
    razorpay_monthly_planid: string;
    razorpay_yearly_planid: string;
    extra_connectors: number;
    max_users: number;
}

export type Agents = {
    agent_id: string;
    agent_name: string;
    agent_desc: string;
    pricepertoken: number;
    agent_features: string[];
    currency: string;
};

export interface Notes {
    [key: string]: string | boolean;
}

export type SubsIntsertPayload = {
    client_id: string;
    plan_id: string;
    plan_name: string;
    razorpay_plan_id: string;
    razorpay_subscription_id: string;
    currency: string;
    amount: string;
    extra_connectors: number;
    max_users: number;
    auto_renew: boolean;
    is_yearly: boolean;
    attribute: Attributes;
    subs_end_dt: string;
    notes: Notes;
}

export interface UpdateSubscriptionContractPayload {
    clientId: string;
    status: 'expired' | 'cancelled' | 'completed';
    remarks: string;
}

export interface SubscriptionRecordDTO {
    id: string;
    client_id: string;
    plan_id: string;
    plan_name: string;
    razorpay_subscription_id: string;
    currency: string;
    amount: string;
    auto_renew: boolean;
    is_active: boolean;
    is_yearly: boolean;
    status: string;
    notes: Notes;
    attribute: Attributes;
    subs_start_dt: string;
    subs_end_dt: string;
    created_at: string;
}

interface TopupItem {
    amount: string;
    agent_id: string;
    currency: string;
    tokens_added: number;
}

interface TopupRecord {
    id: string;
    razorpay_order_id: string;
    razorpay_payment_id: string;
    currency: string;
    amount: number;
    status: string;
    label: string;
    created_at: string;
    items: TopupItem[];
}

interface AgentUsage {
    agent_id: string;
    agent_name: string;
    is_expired: boolean;
    token_total: number;
    token_used: number;
    user_id: number;
}

export interface HistoryAttributes extends Attributes {
    topups: TopupRecord[];
    agent_usages: AgentUsage[];
    autoRenew: boolean;
    isYearly: boolean;
    notes: Notes;
}


export interface SubscriptionHistoryRecordDTO {
    id: string;
    client_id: string;
    subscription_id: string;
    plan_id: string;
    plan_name: string;
    razorpay_subscription_id: string;
    currency: string;
    amount: string;
    status: string;
    remarks: string;
    subs_start_dt: string;
    subs_end_dt: string;
    created_at: string;
    attribute: HistoryAttributes
}

export interface UpdateClientOrderStatusPayload {
    client_id?: string;
    customer_id?: string;
    currency: string;
    amount: number;
    razorPay_OrderId: string;
    razorPay_PaymentId?: string;
    status: 'created' | 'paid';
    notes: Notes;
}

export interface UpdateClientPaymentStatusPayload {
    client_id?: string;
    customer_id?: string;
    currency: string;
    amount: number;
    method: string;
    razorPay_OrderId: string;
    rzrPay_PaymentId: string;
    status: 'authorized' | 'captured';
    notes: Notes;
}

export interface PaymentRecordsDTO {
    id: string;
    client_id: string;
    subscription_id: string;
    plan_name: string;
    razorpay_subscription_id: string;
    razorpay_order_id: string;
    razorpay_payment_id: string;
    currency: string;
    amount: number;
    method: string;
    status: string;
    label: string;
    notes?: {
        vpa: string;
        desc: string;
        email: string;
        contact: string;
        token_id?: string;
    };
    created_at: string;
}

export interface UpdateClientTopupPayload {
    client_id: string;
    currency: string;
    amount: number;
    razorPay_OrderId: string;
    razorPay_PaymentId?: string;
    status: string;
    agents: AgentTopupPayload[];
}

export interface AgentUsageRecordsDTO {
    agent_id: string;
    agent_name: string;
    total_tokens: number;
    total_tokens_used: number;
}

export interface CreateInvocieRecordPayload {
    client_id?: string;
    customer_id?: string;
    is_topup: boolean;
    currency: string;
    amount: number;
    razorpay_order_id: string;
    razorpay_payment_id: string;
}

export interface TopupRecordsDTO {
    id: string;
    client_id: string;
    subscription_id: string;
    plan_id: string;
    plan_name: string;
    razorpay_subscription_id: string;
    razorpay_order_id: string;
    razorpay_payment_id: string;
    currency: string;
    amount: number;
    status: string;
    label: string;
    created_at: string;
}

export interface InvoiceDTO {
    id: string;
    client_id: string;
    subscription_id: string;
    plan_id: string;
    plan_name: string;
    is_topup: boolean;
    topup_id?: string;
    razorpay_subscription_id: string;
    razorpay_order_id: string;
    razorpay_payment_id: string;
    currency: string;
    amount: number
    status: string;
    url?: string;
    billing_start_dt: string;
    billing_end_dt: string;
    label: string;
    created_dt: string;
}

export interface ClientDTO {
    client_id: string;
    organization_type: string;
    agency_name?: string;
    agency_url?: string;
    company_name: string;
    company_url: string;
    company_business_type?: string;
    company_platform?: string;
    company_traffic?: string;
    company_country: string;
    last_update_date: string;
    package_current?: string;
    register_progress: string;
    test_connection_succeeded_timestamp?: string;
    company_annual_revenue?: string;
    company_currency?: string;
    razorpay_customerid: string;
    gstin?: string;
    billing_address?: string;
}

export interface TopupItemsDTO {
    id: string;
    client_id: string;
    topup_id: string;
    subscription_id: string;
    agent_id: string;
    plan_id: string;
    plan_name: string;
    razorpay_subscription_id: string;
    agent_name: string;
    tokens_added: number;
    currency: string;
    amount: number;
    created_at: string;
}

export interface InvoiceRecordsResponseDTO {
    success: boolean;
    message: string;
    invoice_id?: string;
}

export interface InvoiceRecordsDTO {
    id: string;
    client_id: string;
    subscription_id: string;
    plan_id: string;
    plan_name: string;
    is_topup: boolean
    topup_id?: string;
    razorpay_subscription_id: string;
    razorpay_order_id: string;
    razorpay_payment_id: string;
    currency: string;
    amount: number;
    status: string;
    url: string;
    billing_start_dt: string;
    billing_end_dt: string;
    label: string;
    created_dt: string;
}

