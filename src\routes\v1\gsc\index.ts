import express from 'express';
import asyncHandler from '../../../midddleware/async-handler';

import { logger } from '../../../config/logger';
import { GscController } from '../../../controllers/GscController';

const router = express.Router();

const gscController = new GscController(logger);

router.get(
   '/callback',
   asyncHandler(gscController.fetchRefreshToken.bind(gscController)),
);

router.get(
   '/login',
   asyncHandler(gscController.getGoogleAuthUrl.bind(gscController)),
);

export { router as gscRouter };
