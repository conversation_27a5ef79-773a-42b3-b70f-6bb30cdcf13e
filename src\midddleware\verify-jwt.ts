import { NextFunction, Request, Response } from 'express';
import { createLogger } from 'winston';
import jwt from 'jsonwebtoken';
import { Config } from '../config';
import { LogoutDetails } from '../types';

const logger = createLogger({});

const verifyJWT = (req: Request, res: Response, next: NextFunction) => {
   const authHeader: string | undefined = req.headers['authorization'];

   if (!authHeader) {
      logger.info('Missing authorization header');
      res.sendStatus(401);
   }

   const token: string = (authHeader && authHeader.split(' ')[1]) as string;
   jwt.verify(token, `${Config.JWT_ACCESS_TOKEN_SECRET}`, (err, decoded) => {
      if (err) {
         logger.error(err);
         res.sendStatus(403);
      } else {
         const user: LogoutDetails = decoded as LogoutDetails;
         req.body.email_address = user.email_address;
         next();
      }
   });
};

export default verifyJWT;
