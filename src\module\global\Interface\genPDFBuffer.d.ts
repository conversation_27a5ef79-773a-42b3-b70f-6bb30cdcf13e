export interface InvoiceInfoItem {
    label: string;
    value: string;
}

export interface InvoiceItem {
    description: string;
    unitCost: string;
    quantity: string;
    amount: string;
}

export interface TotalRow {
    label: string;
    value: string;
    bold?: boolean;
}

export interface ContactInfo {
    email: string;
    phone: string;
    website: string;
}

export interface GenerateInvoiceParams {
    invoiceTitle: string;
    companyInfo: string;
    invoiceInfo: InvoiceInfoItem[];
    billedTo: string;
    shippedTo: string;
    items: InvoiceItem[];
    notes: string;
    totals: TotalRow[];
    contact: ContactInfo;
}