import { Request, Response } from 'express';
import { AzureService } from '../services/AzureService';
import { Logger } from 'winston';

export class AzureController {
   constructor(
      private azureService: AzureService,
      private logger: Logger,
   ) {}

   async handleDecodeBlob(req: Request, res: Response) {
      const uri = await this.azureService.decodeBlob(
         req.body as { image_url: string },
      );
      this.logger.info('Blob Data Has been Decoded with expiry');
      res.status(200).send({ uri });
   }

   async handleEncodeBlob(req: Request, res: Response) {
      if (!req.file) throw new Error('No file provided');

      const uri = await this.azureService.encodeBlob(req.file);
      this.logger.info('Blob Data Has been Encoded with expiry');
      res.json({ uri });
   }
}
