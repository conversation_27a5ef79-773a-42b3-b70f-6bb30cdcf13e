import { Request, Response } from 'express';
import { Logger } from 'winston';
import { AirbyteService } from '../services/AirbyteService';
import * as types from '../types';

export class AirbyteController {
   constructor(
      private airbyteService: AirbyteService,
      private logger: Logger,
   ) {}

   async handleWorkspaceCreation(req: Request, res: Response) {
      const { name } = req.body as types.WorkspaceDto;

      const workspaceData = await this.airbyteService.createWorkspace(name);
      this.logger.info('Workspace details of ' + req.params.workspaceId);
      res.status(201).json({ workspaceData });
   }
   async handleWorkspaceDetails(req: Request, res: Response) {
      try {
         const workspaceData = await this.airbyteService.getWorkspaceDetails(
            req.params.workspaceId,
         );
         this.logger.info('Workspace details of ' + req.params.workspaceId);
         res.status(201).json({ workspaceData });
      } catch (err) {
         res.json({ message: 'Error' });
      }
   }

   async handleInitiateAuthForSource(req: Request, res: Response) {
      const body = req.body as { sourceType: string };
      const authData = await this.airbyteService.initiateAuthForSource(
         body.sourceType,
      );
      this.logger.info('Initiated auth for ' + body.sourceType);
      res.status(200).json({ authData });
   }

   async handleSourceCreation(req: Request, res: Response) {
      const { name, workspaceId, sourceType, secretId, dateRange } =
         req.body as types.SourceCreationDto;
      const { sourceId } = await this.airbyteService.createSource(
         name,
         workspaceId,
         sourceType,
         req.body as Record<string, object>,
         secretId,
         dateRange,
      );

      this.logger.info('Source created ' + name);
      res.status(201).json({ sourceId });
   }

   async handleRemoveSource(req: Request, res: Response) {
      const { sourceId } = req.body as { sourceId: string };
      await this.airbyteService.deleteSource(sourceId);
      res.sendStatus(203);
   }

   async handleDestinationCreation(req: Request, res: Response) {
      const { name, workspaceId, schema } =
         req.body as types.DestinationCreationDto;
      const { destinationId } = await this.airbyteService.createDestination(
         name,
         workspaceId,
         schema,
      );

      this.logger.info('Destination created created ' + name);
      res.status(201).json({ destinationId });
   }

   async handleConnectionCreation(req: Request, res: Response) {
      const { name, sourceId, destinationId, prefix } =
         req.body as types.ConnectionCreationDto;
      const { connectionId } = await this.airbyteService.createConnection(
         name,
         sourceId,
         destinationId,
         prefix,
      );

      this.logger.info('Connection created ' + name);
      res.status(201).json({ connectionId });
   }

   async handleTriggerSync(req: Request, res: Response) {
      const { connectionId } = req.body as types.TriggerSyncDto;

      const { jobId } = await this.airbyteService.triggerSync(connectionId);
      this.logger.info('Started the sync for ' + connectionId);
      res.status(200).json({ jobId });
   }
}
