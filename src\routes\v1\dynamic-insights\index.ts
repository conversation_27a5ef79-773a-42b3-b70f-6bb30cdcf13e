import express from 'express';
import asyncHandler from '../../../midddleware/async-handler';

import { AppDataSource } from '../../../config/data-source';
import { logger } from '../../../config/logger';
import { DynamicInsightService } from '../../../services/DynamicInsightService';
import { DynamicInsightController } from '../../../controllers/DynamicInsightController';

const router = express.Router();

const entityManager = AppDataSource.manager;
const dynamicInsightService = new DynamicInsightService(entityManager);
const dynamicInsightController = new DynamicInsightController(
   dynamicInsightService,
   logger,
);

router
   .route('/')
   .post(
      asyncHandler(
         dynamicInsightController.handleFetchRank.bind(
            dynamicInsightController,
         ),
      ),
   );

router.post(
   '/tracked-insights',
   asyncHandler(
      dynamicInsightController.handleFetchWebInsightTrackedIds.bind(
         dynamicInsightController,
      ),
   ),
);

export { router as dynamicInsightRouter };
