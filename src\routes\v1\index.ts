import express from 'express';
import { authRouter } from './auth';
import { loginRouter } from './login';
import { twitterRouter } from './twitter';
import { airbyteRouter } from './airbyte';
import { azureRouter } from './azureblob';
import { dynamicInsightRouter } from './dynamic-insights';
import { kpiRouter } from './kpi/index';
import cfoRouter from './cfo/index';
import { linkedinRouter } from './linkedin';
import { pulseMetaAdsRouter } from './performance-insights/meta-ads';
import { settingsRouter } from './settings';
import { fivetranRouter } from './fivetran';
import { googleAdsRouter } from './performance-insights/google-ads';
import { auth1Router } from './auth1';
import { onboardingRouter } from './onboarding';
import { twitterAuthRouter } from './twitter/twitter-auth';
import { linkedinAuthRouter } from './linkedin/linkedin-auth';
import { usersRouter } from './users';
import { metaAdsRouter } from './meta-ads';
import { gscRouter } from './gsc';
import { googleAnalyticsRouter } from './google-analytics';
import { metaAdsManagerRouter } from './meta-ads-manager';
import { flableRouter } from './flable';
import { alertingAgentRouter } from './agents/alerting-agent/alerting-agent.route';
import { analyticsAgentRouter } from './agents/analytics-agent/analytics-agent.route';
import { subscriptionRouter } from '../../module/subscription/subs.routes';
import { notificationsRouter } from './notifications/notifications.route';
import { customAlertRouter } from '../../module/custom-alert/custom-alert.routes';
import { shipRocketRouter } from './shiprocket';
const router = express.Router();

const defaultRoutes = [
   {
      path: '/auth',
      route: authRouter,
   },
   {
      path: '/airbyte',
      route: airbyteRouter,
   },
   {
      path: '/fivetran',
      route: fivetranRouter,
   },

   // Keep adding more routes
   {
      path: '/auth1',
      route: auth1Router,
   },
   {
      path: '/login',
      route: loginRouter,
   },
   {
      path: '/twitter',
      route: twitterRouter,
   },
   {
      path: '/twitter-auth',
      route: twitterAuthRouter,
   },
   {
      path: '/linkedin',
      route: linkedinRouter,
   },
   {
      path: '/linkedin-auth',
      route: linkedinAuthRouter,
   },
   {
      path: '/azureblob',
      route: azureRouter,
   },
   {
      path: '/dynamic-insights',
      route: dynamicInsightRouter,
   },
   {
      path: '/kpi',
      route: kpiRouter,
   },
   {
      path: '/performance-insights',
      route: pulseMetaAdsRouter,
   },
   {
      path: '/google-ads',
      route: googleAdsRouter,
   },
   {
      path: '/meta-ads',
      route: metaAdsRouter,
   },
   {
      path: '/google-gsc',
      route: gscRouter,
   },
   {
      path: '/google-analytics',
      route: googleAnalyticsRouter,
   },
   {
      path: '/settings',
      route: settingsRouter,
   },
   {
      path: '/cfo',
      route: cfoRouter,
   },
   // Keep adding more routes
   {
      path: '/onboarding',
      route: onboardingRouter,
   },
   {
      path: '/meta-ads-manager',
      route: metaAdsManagerRouter,
   },
   {
      path: '/user',
      route: usersRouter,
   },

   {
      path: '/alerting-agent',
      route: alertingAgentRouter,
   },
   {
      path: '/website',
      route: flableRouter,
   },
   {
      path: '/analytics-agent',
      route: analyticsAgentRouter,
   },
   {
      path: '/subs',
      route: subscriptionRouter,
   },
   {
      path: '/notifications',
      route: notificationsRouter,
   },
   {
      path: '/custom-alert',
      route: customAlertRouter,
   },
   {
      path: '/shiprocket',
      route: shipRocketRouter,
   },
];
defaultRoutes.forEach(({ path, route }) => {
   router.use(path, route);
});

export default router;
