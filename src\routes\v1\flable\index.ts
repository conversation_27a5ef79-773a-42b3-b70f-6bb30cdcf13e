import express from 'express'
import { logger } from '../../../config/logger';
import { EmailService } from '../../../services/EmailService';
import { FlableService } from '../../../services/Flable/FlableService';
import { FlableController } from '../../../controllers/flable/flableController';
import asyncHandler from '../../../midddleware/async-handler';

const router = express.Router()
const emailService = new EmailService();
const flableService = new FlableService(
    logger,
    emailService,);
const flableController = new FlableController(
    flableService,
    logger,
);

router
    .route('/leademail')
    .post(
        asyncHandler(
            flableController.sendLeadEmail.bind(flableController)
        )
    )

    export { router as flableRouter };