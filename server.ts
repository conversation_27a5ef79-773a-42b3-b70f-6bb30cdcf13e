import 'reflect-metadata';
import { logger } from './src/config/logger';
import app from './src/app';
import { Config } from './src/config';
import { AppDataSource } from './src/config/data-source';
// import { initializeMongoDb } from './src/config/mongoDb';

const port = Config.PORT || 8003;

const initializeDb = async () => {
   const connection = await AppDataSource.initialize();
   return connection;
};

const startServer = async (port: number) => {
   try {
      await initializeDb();
      // if(Config.ENVIRONMENT !==)
      // await connection.synchronize();
      // await initializeMongoDb();

      app.listen(port, () => {
         logger.info(
            `Server running on ${port} in ${process.env.NODE_ENV} mode`,
         );
      });
   } catch (error: unknown) {
      // if (connection) void connection.destroy();
      error instanceof Error
         ? logger.error(error.message)
         : logger.error('Something went wrong while running the server');
   }
};

void startServer(port as number);
