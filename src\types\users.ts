// DETAILS
export interface AllUserDetails {
   agency_name: string | null;
   agency_url: string | null;
   client_id: string;
   company_business_type: string;
   company_country: string;
   company_name: string;
   company_platform: string;
   company_traffic: string;
   company_url: string;
   country: string;
   email_address: string;
   full_name: string;
   language: string;
   organization_type: 'Individual Business' | 'Marketing Agency';
   product_register_progress: string;
   test_connection_succeeded_timestamp: string | null;
   user_active: 'Y' | 'N';
   user_role: 'Admin' | 'Contributor';
   user_id: string;
}

// QUERY RESULTS
export interface GetAllUsersQueryResult {
   [key: string]: AllUserDetails[];
}

// PAYLOADS
export interface GetAllUsersPayload {
   [key: string]: string;
   company_url: string;
}

export interface CreateUpdateUserPayload {
   client_id: string;
   is_active: 'Y' | 'N';
   email_address: string;
   cb_product_updates: 'Y' | 'N';
   agency_name: string;
   agency_url: string;
   company_name: string;
   company_url: string;
   country: string;
   create_date: string;
   full_name: string;
   language: string;
   last_update_date: string;
   user_active: string;
   user_role: 'Admin' | 'Contributor';
   user_confirmed?: 'Y' | 'N';
   password?: string;
   confirm_password?: string;
}

export interface DeleteUserPayload {
   [key: string]: string;
   email_address: string;
}

// RESPONSES
export interface GetAllUsersResponse {
   response: {
      status: string;
      message: string;
      details: AllUserDetails[];
   };
}

export interface CreateUpdateUserResponse {
   response: {
      status: string;
      message: string;
   };
}

export interface DeleteUserResponse {
   response: {
      status: string;
      message: string;
   };
}
