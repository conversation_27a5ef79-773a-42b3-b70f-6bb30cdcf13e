/* eslint-disable @typescript-eslint/no-unnecessary-type-assertion */
import { EntityManager } from 'typeorm';
import * as types from '../types';
import { Config } from '../config';

export class DynamicInsightService {
   constructor(private entityManager: EntityManager) {}

   async fetchRank(
      client_id: string,
      days: string,
   ): Promise<types.DynamicInsightRankData[] | null> {
      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_dynamic_insights_rank_get($1, $2)`;

      const result = (await this.entityManager.query(query, [
         client_id,
         days,
      ])) as types.DynamicInsightsRankDataResponse[];

      return result.length > 0 ? result[0].fn_dynamic_insights_rank_get : null;
   }

   async fetchTrackedInsights(clientId: string, days: string) {
      const query = `SELECT ${Config.DB_Postgres_SCHEMA}.fn_get_dynamic_tracked_data($1,$2)`;
      const result = (await this.entityManager.query(query, [
         clientId,
         days,
      ])) as types.WebInsightTrackedResponse[];
      return result.length > 0 ? result[0].fn_get_dynamic_tracked_data : null;
   }
}
