import express from 'express';
const router = express.Router();

import { logger } from '../../../config/logger';
import { AppDataSource } from '../../../config/data-source';
import { CFOController } from '../../../controllers/CFOController';
import { CFOService } from '../../../services/CFOService';
import asyncHandler from '../../../midddleware/async-handler';
const entityManager = AppDataSource.manager;
const cfoService = new CFOService(entityManager, logger);
const cfoController = new CFOController(cfoService, logger);

router
   .route('/cogs/:clientId')
   .get(asyncHandler(cfoController.getCogs.bind(cfoController)));
router
   .route('/payment-method/:clientId')
   .get(asyncHandler(cfoController.getPaymentMethods.bind(cfoController)));
router
   .route('/shipping-cost/:clientId')
   .get(asyncHandler(cfoController.getShippingCosts.bind(cfoController)));
router
   .route('/costs-by-order-id/:clientId')
   .get(asyncHandler(cfoController.getShippingCostsByOrderId.bind(cfoController)));
router
   .route('/shipping-profiles/:clientId')
   .get(asyncHandler(cfoController.getShippingProfiles.bind(cfoController)));
router
   .route('/fixed-expense/:clientId')
   .get(asyncHandler(cfoController.getFixedExpenses.bind(cfoController)));
router
   .route('/variable-expense/:clientId')
   .get(asyncHandler(cfoController.getVariableExpenses.bind(cfoController)));

router
   .route('/cogs')
   .post(asyncHandler(cfoController.upsertCogs.bind(cfoController)));
router
   .route('/payment-method')
   .post(asyncHandler(cfoController.upsertPaymentGateway.bind(cfoController)));
router
   .route('/shipping-cost')
   .post(asyncHandler(cfoController.upsertShippingCost.bind(cfoController)));
router
   .route('/costs-by-order-id')
   .post(asyncHandler(cfoController.upsertShippingCostByOrderId.bind(cfoController)));
router
   .route('/shipping-profile')
   .post(asyncHandler(cfoController.upsertShippingProfile.bind(cfoController)));
router
   .route('/fixed-expense')
   .post(asyncHandler(cfoController.upsertFixedExpense.bind(cfoController)));
router
   .route('/variable-expense')
   .post(asyncHandler(cfoController.upsertVariableExpense.bind(cfoController)));

router
   .route('/delete')
   .put(asyncHandler(cfoController.deleteRecord.bind(cfoController)));

export default router;
