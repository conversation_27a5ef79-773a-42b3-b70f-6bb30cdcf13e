import { AppDataSource } from '../../../../config/data-source';
import { GoolgeAdsController } from '../../../../controllers/GoogleAdsController'
import { GoogleAdsService } from '../../../../services/GoogleAdsService'
import { logger } from '../../../../config/logger'
import express from 'express'
import asyncHandler from '../../../../midddleware/async-handler'

const router = express.Router()

const entityManager = AppDataSource.manager
const googleAdsService = new GoogleAdsService(entityManager)
const goolgeAdsController = new GoolgeAdsController(
    googleAdsService,
    logger
)

router
    .route("/channel-types")
    .post(
        asyncHandler(
            goolgeAdsController.fetchChannelType.bind(
                goolgeAdsController,
            ),
        ),
    )
router
    .route('/kpi-names')
    .post(
        asyncHandler(
            goolgeAdsController.fetchKPIsNames.bind(
                goolgeAdsController,
            )
        )
    )
router
    .route('/campaigns')
    .post(
        asyncHandler(
            goolgeAdsController.fetchCampaignsDaywise.bind(
                goolgeAdsController,
            ),
        ),
    )
router
    .route('/campaigns-kpi-data')
    .post(
        asyncHandler(
            goolgeAdsController.fetchCampaignsKpiwise.bind(
                goolgeAdsController,
            ),
        ),
    )
router
    .route('/track-kpi')
    .put(
        asyncHandler(
            goolgeAdsController.updateTrackedKpis.bind(
                goolgeAdsController,
            ),
        ),
    )
router
    .route('/ad-groups')
    .post(
        asyncHandler(
            goolgeAdsController.fetchAdgroups.bind(
                goolgeAdsController,
            ),
        ),
    )
router
    .route('/campaign-chart-insights')
    .post(
        asyncHandler(
            goolgeAdsController.fetchCampaignChartSummary.bind(
                goolgeAdsController,
            ),
        ),
    )
router
    .route('/campaign-adgroup-insights')
    .post(
        asyncHandler(
            goolgeAdsController.fetchCampaignAdgroupSummary.bind(
                goolgeAdsController,
            ),
        ),
    )
router
    .route('/ad-group-keyword-insights')
    .post(
        asyncHandler(
            goolgeAdsController.fetchAdgroupKeywordSummary.bind(
                goolgeAdsController,
            ),
        ),
    )
router
    .route('/ad-group-graph-insights')
    .post(
        asyncHandler(
            goolgeAdsController.fetchAdgroupGraphSummary.bind(
                goolgeAdsController,
            ),
        ),
    )
router
    .route('/ad-group-insights')
    .post(
        asyncHandler(
            goolgeAdsController.fetchAdgroupSummary.bind(
                goolgeAdsController,
            ),
        ),
    )
router
    .route('/ads')
    .post(
        asyncHandler(
            goolgeAdsController.fetchAds.bind(
                goolgeAdsController,
            ),
        ),
    )
router.route('/keywords')
    .post(
        asyncHandler(
            goolgeAdsController.fetchKeywords.bind(
                goolgeAdsController,
            )
        )
    )
router
    .route('/tracked-kpis')
    .post(
        asyncHandler(
            goolgeAdsController.fetchTrackedkpis.bind(
                goolgeAdsController,
            ),
        ),
    )
router
    .route('/tracked-prev-kpis')
    .post(
        asyncHandler(
            goolgeAdsController.fetchTrackedPrevkpis.bind(
                goolgeAdsController,
            ),
        ),
    )

export { router as googleAdsRouter }