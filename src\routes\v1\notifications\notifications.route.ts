import express from 'express';
import { logger } from '../../../config/logger';
import { AppDataSource } from '../../../config/data-source';
import asyncHandler from '../../../midddleware/async-handler';
import { NotificationsModel } from '../../../models/notifications/notifications.model';
import { NotificationsService } from '../../../services/notifications/notifications.service';
import { NotificationsController } from '../../../controllers/notifications/notifications.controller';

const router = express.Router();

const notificationsModel = new NotificationsModel(AppDataSource.manager);
const notificationsService = new NotificationsService(notificationsModel);
const notificationsController = new NotificationsController(
   logger,
   notificationsService,
);

router
   .route('/:client_id/:user_id/notifications')
   .get(
      asyncHandler(
         notificationsController.fetchAllNotificationsByUserID.bind(
            notificationsController,
         ),
      ),
   )
   .post(
      asyncHandler(
         notificationsController.createNotification.bind(
            notificationsController,
         ),
      ),
   );

router
   .route('/:client_id/:user_id/notifications/:id/read')
   .put(
      asyncHandler(
         notificationsController.markNotificationAsRead.bind(
            notificationsController,
         ),
      ),
   );

export { router as notificationsRouter };
