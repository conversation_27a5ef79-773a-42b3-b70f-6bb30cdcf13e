/** MISCELLANEOUS **/
export interface AlertCampaign {
   id: string;
   name: string;
}

export interface AlertingAgentChat {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
   user_query: string;
   response_status: 'success' | 'error' | 'pending';
   final_response: string;
   session_summary: string;
   response_time: number;
   created_at: string;
   updated_at: string;
}

export interface AlertingAgentAlert {
   alert_id: number;
   client_id: string;
   user_id: string;
   chat_id: string;
   session_id: string;
   alert_name: string;
   alert_description: string;
   recipients: string;
   channel: string;
   campaigns: AlertCampaign[];
   kpi: string;
   trend: string;
   value: string;
   value_type: string;
   comparison: string;
   comparison_type: string;
   alert_status: string;
   alert_time: string;
   user_timezone: string;
   emails_triggered: number;
   timestamps_when_triggered: string;
   alert_instruction: string;
   alert_timeframe: string;
   created_at: string;
   updated_at: string;
}

/** PAYLOADS **/

export interface FetchAllSessionsByUserIDPayload {
   client_id: string;
   user_id: string;
   page: number;
}

export interface FetchHistoryBySessionIDPayload {
   client_id: string;
   user_id: string;
   session_id: string;
}

export interface AddChatToSessionHistoryPayload {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
   user_query: string;
   response_status: 'success' | 'error' | 'pending';
   final_response: string;
   session_summary: string;
   response_time: number;
}

export interface FetchChatByChatIdPayload {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
}

export interface FetchAlertsBySessionIDPayload {
   client_id: string;
   user_id: string;
   session_id: string;
}

export interface FetchAllAlertsPayload {
   client_id: string;
   user_id: string;
}

export interface CreateAlertPayload {
   client_id: string;
   user_id: string;
   chat_id: string;
   session_id: string;
   alert_name: string;
   alert_description: string;
   recipients: string;
   channel: string;
   campaigns: AlertCampaign[];
   kpi: string;
   trend: string;
   value: string;
   value_type: string;
   comparison: string;
   comparison_type: string;
   alert_status: string;
   alert_time: string;
   user_timezone: string;
   emails_triggered: number;
   timestamps_when_triggered: string;
   alert_instruction: string;
   alert_timeframe: string;
}

export interface DeleteMultipleAlertsPayload {
   client_id: string;
   user_id: string;
   alert_ids: number[];
}

export interface FetchAlertByIdPayload {
   client_id: string;
   user_id: string;
   alert_id: string;
}

export interface UpdateAlertPayload {
   client_id: string;
   user_id: string;
   chat_id: string;
   session_id: string;
   alert_id: number;
   alert_name: string;
   alert_description: string;
   recipients: string;
   channel: string;
   campaigns: AlertCampaign[];
   kpi: string;
   trend: string;
   value: string;
   value_type: string;
   comparison: string;
   comparison_type: string;
   alert_status: string;
   alert_time: string;
   user_timezone: string;
   emails_triggered: number;
   timestamps_when_triggered: string;
   alert_instruction: string;
   alert_timeframe: string;
}

export interface DeleteAlertPayload {
   client_id: string;
   user_id: string;
   alert_id: string;
}

export interface PauseUnpauseAlertPayload {
   client_id: string;
   user_id: string;
   alert_id: string;
}

export interface UpdateEmailRecipientsPayload {
   client_id: string;
   user_id: string;
   alert_id: string;
   recipients: string[];
}
