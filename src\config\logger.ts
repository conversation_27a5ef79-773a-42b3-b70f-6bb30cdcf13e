import winston from 'winston';
import { Config } from '.';

export const logger = winston.createLogger({
   level: Config.LOG_LEVEL,
   format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json(),
   ),
   defaultMeta: { service: 'flable-backend-service' },
   transports: [
      new winston.transports.File({
         dirname: 'logs',
         filename: 'combined.log',
         level: 'info',
         silent: Config.ENVIRONMENT === 'test',
      }),
      new winston.transports.File({
         dirname: 'logs',
         filename: 'error.log',
         level: 'error',
         silent: Config.ENVIRONMENT === 'test',
      }),
      new winston.transports.Console({
         silent: Config.ENVIRONMENT === 'production',
         format: winston.format.combine(
            winston.format.colorize(),
            winston.format.printf(({ level, message, timestamp, ...meta }) => {
               return `${timestamp} [${level}]: ${message} ${Object.keys(meta).length ? JSON.stringify(meta) : ''}`;
            })
         ),
      }),
   ],
});
