export interface WorkspaceDto {
   name: string;
}

export interface SourceCreationDto {
   name: string;
   workspaceId: string;
   sourceType: string;
   secretId?: string;
   dateRange?: string;
}

export interface DestinationCreationDto {
   name: string;
   workspaceId: string;
   schema: string;
}

export interface ConnectionCreationDto {
   name: string;
   sourceId: string;
   destinationId: string;
   prefix?: string;
}
export interface TriggerSyncDto {
   connectionId: string;
}
