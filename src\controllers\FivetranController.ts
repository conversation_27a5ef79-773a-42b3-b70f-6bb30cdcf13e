import { Request, Response } from 'express';
import { Logger } from 'winston';
import { FivetranService } from '../services/FivetranService';

export class FivetranController {
   constructor(
      private fivetranService: FivetranService,
      private logger: Logger,
   ) {}
   async handleSourceCreation(req: Request, res: Response) {
      const { clientId, sourceType, metaData } = req.body as {
         clientId: string;
         sourceType: string;
         metaData?:{isVendor?:boolean}
      };
      const sourceDetails = await this.fivetranService.createSource(
         clientId,
         sourceType,
         metaData
      );
      res.json({ sourceDetails });
   }

   async handleCreateConnectCard(req: Request, res: Response) {
      const { sourceId, redirectPathName } = req.body as {
         sourceId: string;
         redirectPathName: string;
      };

      const uri = await this.fivetranService.createConnectCard(
         sourceId,
         redirectPathName,
      );

      res.json({ uri });
   }

   async handleGetAllConnectors(req: Request, res: Response) {
      const connectors = await this.fivetranService.getAllConnectors();
      res.json({ connectors });
   }

   async handleDeleteConnector(req: Request, res: Response) {
      const { sourceId } = req.body as { sourceId: string };
      await this.fivetranService.deleteConnector(sourceId);
      res.json({ ok: true });
   }
}
