import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import { Logger } from 'winston';
import { Config } from '../config';
import { EntityManager } from 'typeorm';
import {
   GetAllUsersQueryResult,
   GetAllUsersPayload,
   CreateUpdateUserPayload,
   GetAllUsersResponse,
   CreateUpdateUserResponse,
   DeleteUserPayload,
   DeleteUserResponse,
} from '../types';
import { EmailService } from './EmailService';
import { confirmUserRoleEmail } from '../constants/email-templates';

export class UsersService {
   constructor(
      private logger: Logger,
      private entityManager: EntityManager,
      private emailService: EmailService,
   ) {}

   private toQueryParams(payload: { [key: string]: string }) {
      return Object.keys(payload)
         .map(
            (key) =>
               encodeURIComponent(key) + '=' + encodeURIComponent(payload[key]),
         )
         .join('&');
   }

   async getAllProfiles(
      payload: GetAllUsersPayload,
   ): Promise<GetAllUsersResponse> {
      const { email_address } = payload;

      try {
         const query: string = `SELECT ${Config.DB_Postgres_CONFIG_SCHEMA}.fn_user_login_get_new($1)`;
         const result: GetAllUsersQueryResult[] =
            await this.entityManager.query(query, [email_address]);
         return {
            response: {
               status: 'Success',
               message: 'Users fetched successfully',
               details: result[0].fn_user_login_get_new,
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'getAllProfiles',
         });
         throw new Error(error.message);
      }
   }

   async getAllUsers(
      payload: GetAllUsersPayload,
   ): Promise<GetAllUsersResponse> {
      const { company_url } = payload;

      try {
         const query: string = `SELECT 
                                    ur.email_address,
                                    ur.company_url,
                                    ur.user_role,
                                    ur.last_change_date,
                                    ur.client_id,
                                    ur.is_active AS role_is_active,
                                    ul.full_name,
                                    ul.user_active AS login_user_active,
                                    ul.cb_product_updates,
                                    ul.create_date,
                                    ul.country,
                                    ul.language
                                 FROM 
                                    ${Config.DB_Postgres_CONFIG_SCHEMA}.user_role ur
                                 JOIN 
                                    ${Config.DB_Postgres_CONFIG_SCHEMA}.user_login ul
                                 ON 
                                    ur.email_address = ul.email_address
                                 WHERE 
                                    ur.company_url = $1;`;

         const result = await this.entityManager.query(query, [company_url]);

         return {
            response: {
               status: 'Success',
               message: 'Users fetched successfully',
               details: result,
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'getAllUsers',
         });
         throw new Error(error.message);
      }
   }

   async createUser(
      payload: CreateUpdateUserPayload,
   ): Promise<CreateUpdateUserResponse> {
      try {
         const email_address = payload.email_address.trim();
         if (payload.user_confirmed === 'N') {
            const userExistsQuery = `SELECT * FROM ${Config.DB_Postgres_CONFIG_SCHEMA}.user_login WHERE email_address = $1`;
            const userExists = await this.entityManager.query(userExistsQuery, [
               email_address,
            ]);

            const queryParams = this.toQueryParams(
               Object.entries(payload).reduce(
                  (acc, [key, value]) => {
                     acc[key] = value;
                     return acc;
                  },
                  {} as { [key: string]: string },
               ),
            );

            const queryParamsString = `${queryParams}&user_exists=${userExists.length > 0 ? 'Y' : 'N'}`;
            const regUrl = `${Config.FRONTEND_DOMAIN?.split(',')[0]}/auth/set-password?${queryParamsString}`;

            const emailInfo = {
               to: email_address,
               subject: 'User Registration Confirmation',
               text: '',
               html: confirmUserRoleEmail(
                  payload.full_name,
                  payload.user_role,
                  payload.company_url,
                  regUrl,
               ),
            };
            await this.emailService.sendEmail(emailInfo);
            this.logger.info('Registeration email sent successfully');

            return {
               response: {
                  status: 'Success',
                  message: 'Registeration email sent successfully',
               },
            };
         } else {
            if (payload.password) {
               const hashedPassword = await bcrypt.hash(payload.password, 10);
               payload['password'] = hashedPassword;
            }

            const query: string = `CALL ${Config.DB_Postgres_CONFIG_SCHEMA}.q_user_login_role_put($1::json)`;
            await this.entityManager.query(query, [JSON.stringify([payload])]);

            return {
               response: {
                  status: 'Success',
                  message: 'User created successfully',
               },
            };
         }
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'createUser',
         });
         throw new Error(error.message);
      }
   }

   async updateUser(
      payload: CreateUpdateUserPayload,
   ): Promise<CreateUpdateUserResponse> {
      try {
         const query: string = `CALL ${Config.DB_Postgres_CONFIG_SCHEMA}.q_user_login_role_put($1::json)`;
         await this.entityManager.query(query, [JSON.stringify([payload])]);

         return {
            response: {
               status: 'Success',
               message: 'User updated successfully',
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'updateUser',
         });
         throw new Error(error.message);
      }
   }

   async deleteUser(payload: DeleteUserPayload): Promise<DeleteUserResponse> {
      const { email_address, company_url } = payload;
      try {
         const query: string = `CALL ${Config.DB_Postgres_CONFIG_SCHEMA}.q_user_role_deactivate_put($1, $2)`;
         await this.entityManager.query(query, [email_address, company_url]);

         return {
            response: {
               status: 'Success',
               message: 'User deleted successfully',
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'deleteUser',
         });
         throw new Error(error.message);
      }
   }
}
