/** MISCELLANEOUS **/
export interface ChunkData {
   status: 'in_progress' | 'completed';
   agent_name: string;
   step_id: string | Record<string, string>;
   type: 'tool_call' | 'tool_input' | 'transition' | 'thought' | 'final_result';
   content: string;
   is_final_chunk: boolean;
   timestamp: number;
   metadata: Record<string, string | number | Record<string, string>>;
   error_count: number;
}

export interface AnalyticsAgentChat {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
   user_query: string;
   response_status: 'success' | 'error' | 'pending';
   final_response: string;
   agent_name: string;
   prev_agent_response: string;
   prev_agent_name: string;
   channel: string;
   context_snapshot: Record<string, string>;
   response_like_dislike: string;
   response_feedback_category: string;
   rewrite_response: boolean;
   copy_response: boolean;
   response_time: number;
   user_feedback_comments: string;
   question_mode:
      | 'data-analyst'
      | 'cmo'
      | 'alerting-agent'
      | 'diagnostic-agent';
   diagnostics_prompt_meta_data: Record<string, string>;
   created_at: string;
   updated_at: string;
}

export interface FeatureUsage {
   client_id: string;
   user_id: string;
   feature_name: string;
   feature_type: string;
   is_enabled: boolean;
   no_of_calls: number;
   free_limit_expired: boolean;
   last_call_made_at: string;
}

/** PAYLOADS **/
export interface FetchAllSessionsByUserIDPayload {
   client_id: string;
   user_id: string;
   page: number;
}

export interface FetchSessionHistoryByIDPayload {
   session_id: string;
   client_id: string;
   user_id: string;
}

export interface AddChatToSessionHistoryPayload {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
   user_query: string;
   response_status: 'success' | 'error' | 'pending';
   final_response: string;
   agent_name: string;
   prev_agent_response: string;
   prev_agent_name: string;
   channel: string;
   context_snapshot: Record<string, string>;
   response_like_dislike: string;
   response_feedback_category: string;
   rewrite_response: boolean;
   copy_response: boolean;
   response_time: number;
   user_feedback_comments: string;
   question_mode:
      | 'data-analyst'
      | 'cmo'
      | 'alerting-agent'
      | 'diagnostic-agent';
   diagnostics_prompt_meta_data: Record<string, string>;
   session_name: string;
}

export interface FetchFeatureUsagePayload {
   client_id: string;
   user_id: string;
   feature_name: string;
   feature_type: string;
}

export interface TrackFeatureUsagePayload {
   client_id: string;
   user_id: string;
   feature_name: string;
   feature_type: string;
}

export interface FetchChatInsightsByIDPayload {
   client_id: string;
   user_id: string;
   session_id: string;
}

export interface AddInsightsToSessionHistoryPayload {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
   chat_flow_context: ChunkData[];
}

export interface LikeDislikeChatPayload {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
   action: 'liked' | 'disliked';
}

export interface UpdateChatRewrittenPayload {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
}

export interface UpdateChatCopiedPayload {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
}

export interface UpdateChatFeedbackPayload {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
   type: 'custom' | 'existing';
   feedback: string;
}
