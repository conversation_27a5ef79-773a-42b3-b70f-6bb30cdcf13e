import { CFOService } from '../services/CFOService';
import { ClientPayload, DeleteRecord, FixedExpense, PaymentMethod, ShippingCost, ShippingCostByOrderId, ShippingProfile, UpdateCOGSPayload, VariableExpense } from '../types/cfo';
import { Request, Response } from 'express';
import { Logger } from 'winston';

export class CFOController {
   constructor(
      private cfoService: CFOService,
      private logger: Logger,
   ) {}

   async upsertCogs(req: Request, res: Response){
      const payload = req.body as UpdateCOGSPayload;
      const result = await this.cfoService.upsertCogs(payload);

      res.status(200).send(result);
   }
   async upsertPaymentGateway(req: Request, res: Response){
      const payload = req.body as PaymentMethod;
      const result = await this.cfoService.upsertPaymentGateway(payload);

      res.status(200).send(result);
   }
   async upsertShippingCost(req: Request, res: Response){
      const payload = req.body as ShippingCost;
      const result = await this.cfoService.upsertShippingCost(payload);

      res.status(200).send(result);
   }
   async upsertShippingCostByOrderId(req: Request, res: Response){
      const payload = req.body as ShippingCostByOrderId[];
      const result = await this.cfoService.upsertShippingCostByOrderId(payload);

      res.status(200).send(result);
   }
   async upsertShippingProfile(req: Request, res: Response){
      const payload = req.body as ShippingProfile;
      const result = await this.cfoService.upsertShippingProfile(payload);

      res.status(200).send(result);
   }
   async upsertVariableExpense(req: Request, res: Response){
      const payload = req.body as VariableExpense;
      const result = await this.cfoService.upsertVariableExpense(payload);

      res.status(200).send(result);
   }
   async upsertFixedExpense(req: Request, res: Response){
      const payload = req.body as FixedExpense;
      const result = await this.cfoService.upsertFixedExpense(payload);

      res.status(200).send(result);
   }
   async deleteRecord(req: Request, res: Response){
      const payload = req.body as DeleteRecord;
      const result = await this.cfoService.deleteRecord(payload);

      res.status(200).send(result);
   }
   async getCogs(req: Request, res: Response) {
    const payload = { clientId: req.params.clientId } as ClientPayload;
      const result = await this.cfoService.getCogs(payload);
      res.status(200).send(result)
   }
   async getShippingCosts(req: Request, res: Response) {
    const payload = { clientId: req.params.clientId } as ClientPayload;
      const result = await this.cfoService.getShippingCosts(payload);
      res.status(200).send(result)
   }
   async getShippingCostsByOrderId(req: Request, res: Response) {
    const payload = { clientId: req.params.clientId } as ClientPayload;
      const result = await this.cfoService.getShippingCostsByOrderId(payload);
      res.status(200).send(result)
   }
   async getFixedExpenses(req: Request, res: Response) {
    const payload = { clientId: req.params.clientId } as ClientPayload;
      const result = await this.cfoService.getFixedExpenses(payload);
      res.status(200).send(result)
   }
   async getShippingProfiles(req: Request, res: Response) {
    const payload = { clientId: req.params.clientId } as ClientPayload;
      const result = await this.cfoService.getShippingProfiles(payload);
      res.status(200).send(result)
   }
   async getVariableExpenses(req: Request, res: Response) {
    const payload = { clientId: req.params.clientId } as ClientPayload;
      const result = await this.cfoService.getVariableExpenses(payload);
      res.status(200).send(result)
   }
   async getPaymentMethods(req: Request, res: Response) {
    const payload = { clientId: req.params.clientId } as ClientPayload;
      const result = await this.cfoService.getPaymentGateway(payload);
      res.status(200).send(result)
   }
}
