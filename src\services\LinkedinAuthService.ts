import axios from 'axios';

import { Logger } from 'winston';
import { Config } from '../config';

const fetchAccessToken = async (
   code: string,
   logger: Logger,
   redirectPathName: string,
) => {
   try {
      const uri = 'https://www.linkedin.com/oauth/v2/accessToken';
      const {
         data: { access_token, refresh_token },
      } = await axios.post<{
         access_token: string;
         refresh_token: string;
      }>(
         uri,
         {
            grant_type: 'authorization_code',
            code: code,
            client_id: Config.LINKEDIN_CLIENT_ID,
            client_secret: Config.LINKEDIN_CLIENT_SECRET,
            redirect_uri: `${Config.LINKEDIN_REDIRECT_URI!}?redirect_path_name=${redirectPathName}`,
         },
         {
            headers: {
               'Content-Type': 'application/x-www-form-urlencoded',
            },
         },
      );
      return { access_token, refresh_token };
   } catch (err) {
      logger.error(String(err));
      return null;
   }
};

export class LinkedinAuthService {
   constructor(private logger: Logger) {}

   authorize(clientId: string, redirectURI: string) {
      return encodeURI(
         `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${Config.LINKEDIN_CLIENT_ID}&redirect_uri=${redirectURI}&state=${clientId}&scope=${Config.LINKEDIN_SCOPE}`,
      );
   }

   async fetchTokenData(code: string, state: string, redirectPathName: string) {
      const tokenData = await fetchAccessToken(
         code,
         this.logger,
         redirectPathName,
      );

      if (!tokenData) throw new Error('Failed to fetch access token');

      //   await LinkedinAuth.findOneAndUpdate(
      //      { clientID: state },
      //      {
      //         $set: {
      //            accessToken: access_token,
      //            refreshToken: refresh_token,
      //         },
      //      },
      //      { new: true, upsert: true, setDefaultsOnInsert: true },
      //   );
      return tokenData;
   }
}
