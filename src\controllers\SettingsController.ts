import { SettingsService } from '../services/SettingsService';
import { Request, Response } from 'express';
import { Logger } from 'winston';
import {
   AddCompetitorRequest,
   SettingsRequest,
   FetchCompetitorsRequest,
   FetchIndustryRequest,
   FetchLanguageRequest,
   FetchTimezoneRequest,
   MetricReport,
   GetReportPayload,
   UpdateReportPayload,
   pauseReportPayload,
   DeleteReportPayload,
   GeneralSettingsPayload,
   LanguageTimezonePayload,
   AccountDetailsPayload,
   ProfileImagePayload,
   FetchFeatureUsagePayload,
   TrackFeatureUsagePayload,
} from '../types';
import { MAX_TRACKED_COMPETITORS } from '../constants';
import { hasDuplicate } from '../utils/ArrayUtils';
import { DatabaseError } from 'pg';

export class SettingsController {
   constructor(
      private settingsService: SettingsService,
      private logger: Logger,
   ) {}

   async handleFetchGeneralSettings(req: Request, res: Response) {
      const { client_id, email_address } = req.query as GeneralSettingsPayload;
      const result = await this.settingsService.getGeneralSettings({
         client_id,
         email_address,
      });
      res.json(result);
   }

   async handleUpdateLanguageTimezone(req: Request, res: Response) {
      const result = await this.settingsService.updateLanguageTimezone(
         req.body as LanguageTimezonePayload,
      );
      res.json(result);
   }

   async handleUpdateAccountDetails(req: Request, res: Response) {
      const result = await this.settingsService.updateAccountDetails(
         req.body as AccountDetailsPayload,
      );
      res.json(result);
   }

   async handleUpdateProfileImage(req: Request, res: Response) {
      const result = await this.settingsService.updateProfileImage(
         req.body as ProfileImagePayload,
      );
      res.json(result);
   }

   async handleFetchLanguage(req: Request, res: Response) {
      const result = await this.settingsService.getLanguage(
         req.query as FetchLanguageRequest,
      );
      res.json(result);
   }

   async handleFetchTimezone(req: Request, res: Response) {
      const result = await this.settingsService.getTimezone(
         req.query as FetchTimezoneRequest,
      );
      res.json(result);
   }

   async handleFetchIndustry(req: Request, res: Response) {
      const result = await this.settingsService.getIndustry(
         req.query as FetchIndustryRequest,
      );
      res.json(result);
   }

   async handleFetchIndustryOptions(req: Request, res: Response) {
      const result = await this.settingsService.getIndustryOptions();
      res.json(result);
   }

   async verify(req: Request, res: Response) {
      const result = await this.settingsService.verifySocialHandle(
         req.query as { handle: string },
      );
      res.json({ verified: result });
   }

   async handleUpdateLanguage(req: Request, res: Response) {
      const requestBody = req.body as SettingsRequest;
      await this.settingsService.updateLanguage(requestBody);

      res.status(200).send({
         response: 'Updated language!',
         language: requestBody.language,
      });
   }

   async handleUpdateTimezone(req: Request, res: Response) {
      const requestBody = req.body as SettingsRequest;
      await this.settingsService.updateTimezone(requestBody);

      res.status(200).send({
         response: 'Updated timezone!',
         timezone: requestBody.timezone,
      });
   }

   async handleUpdateIndustry(req: Request, res: Response) {
      const requestBody = req.body as SettingsRequest;
      await this.settingsService.updateIndustry(requestBody);

      res.status(200).send({
         response: 'Updated industry!',
         Industry: requestBody.industry,
         category: requestBody.category,
      });
   }

   async handleFetchCompetitor(req: Request, res: Response) {
      const result = await this.settingsService.getCompetitors(
         req.query as FetchCompetitorsRequest,
      );
      res.json(result);
   }
   async getWeeklyAutoReportFromXi(req: Request, res: Response) {
      const { clientId, userId, activeCategories } = req.body;
      const result = await this.settingsService.getWeeklyAutoReportFromXi({
         clientId,
         userId,
         activeCategories,
      });

      res.json(result);
   }
   async handleReportsMail(req: Request, res: Response) {
      const result = await this.settingsService.sendMail(
         req.body as MetricReport,
      );
      res.json(result);
   }
   async createReport(req: Request, res: Response) {
      const result = await this.settingsService.createReport(
         req.body as MetricReport,
      );
      res.json(result);
   }
   async getReports(req: Request, res: Response) {
      const payload = {
         clientId: req.params.clientId,
         userId: req.params.userId,
      } as GetReportPayload;
      const result = await this.settingsService.getAllReports(payload);
      res.json(result);
   }
   async getMetaDataForAutoReport(req: Request, res: Response) {
      const result = await this.settingsService.getMetaDataForAutoReport(
         req.params.clientId,
      );
      res.json(result);
   }
   async updateReport(req: Request, res: Response) {
      const result = await this.settingsService.updateReport(
         req.body as UpdateReportPayload,
      );
      res.json(result);
   }
   async pauseAutoReport(req: Request, res: Response) {
      const payload = {
         clientId: req.params.clientId,
         isSubscribed: req.params.is_subscribed === 'true',
         reportId: req.params.reportId,
      } as pauseReportPayload;
      const result = await this.settingsService.pauseAutoReport(payload);
      res.json(result);
   }
   async deleteReport(req: Request, res: Response) {
      const payload = {
         clientId: req.params.clientId,
         reportId: req.params.reportId,
      } as DeleteReportPayload;
      const result = await this.settingsService.deleteReport(payload);
      res.json(result);
   }

   async handleCompetitorPost(req: Request, res: Response) {
      const { competitor_handles } = req.body as AddCompetitorRequest;
      if (competitor_handles.length > MAX_TRACKED_COMPETITORS) {
         res.status(400).send({
            message: `Max ${MAX_TRACKED_COMPETITORS} handles allowed!`,
         });
         return;
      }
      if (hasDuplicate(competitor_handles, 'handle')) {
         res.status(400).send({ message: 'No Duplicate handles allowed!' });
         return;
      }

      await this.settingsService.addCompetitors(
         req.body as AddCompetitorRequest,
      );

      res.status(200).send({ response: 'Updated competitors!' });
   }

   async handleSaveMetaAdsCredentials(req: Request, res: Response) {
      try {
         const { client_id, meta_access_token, page_id, ad_account_id } =
            req.body;
         await this.settingsService.saveMetaAdsCredentials({
            client_id,
            meta_access_token,
            page_id,
            ad_account_id,
         });
         res.status(200).json({
            status: 'Success',
            message: 'Meta Ads credentials saved successfully',
         });
      } catch (err) {
         const error = err as Error;
         res.status(500).json({
            status: 'Error',
            message: error.message,
         });
      }
   }

   async handleGetMetaAdsCredentials(req: Request, res: Response) {
      try {
         const { client_id } = req.params;
         const credentials =
            await this.settingsService.getMetaAdsCredentials(client_id);
         if (!credentials) {
            res.status(404).json({
               status: 'Error',
               message: 'No Meta Ads credentials found for this client',
            });
            return;
         }
         res.status(200).json({
            status: 'Success',
            data: credentials,
         });
      } catch (err) {
         const error = err as Error;
         res.status(500).json({
            status: 'Error',
            message: error.message,
         });
      }
   }

   /**
    * Fetches feature usage statistics for a specific user.
    *
    * @route GET /:client_id/:user_id/feature-usage
    * @param req.params {client_id, user_id} - Identifiers for client and user.
    * @returns 200 with feature usage data, or 400 on error.
    */
   async fetchFeatureUsage(req: Request, res: Response) {
      try {
         const result = await this.settingsService.fetchFeatureUsage({
            ...req.params,
            ...req.query,
         } as unknown as FetchFeatureUsagePayload);

         res.status(200).send(result);
      } catch (err) {
         const error = err as Error | DatabaseError;
         this.logger.error(`Error fetching feature usage: ${error.message}`);
         res.status(400).send(error.message);
      }
   }

   /**
    * Tracks feature usage within a specific session.
    *
    * @route POST /:client_id/:user_id/session/:session_id/track-usage
    * @param req.params {client_id, session_id, user_id} - Identifiers for locating insights.
    * @param req.body - Contains feature usage data.
    * @returns 204 No Content on success, or 400 on failure.
    */
   async trackFeatureUsage(req: Request, res: Response) {
      try {
         await this.settingsService.trackFeatureUsage({
            ...req.body,
            ...req.params,
         } as unknown as TrackFeatureUsagePayload);

         res.status(200).send({
            message: 'Feature usage tracked successfully',
         });
      } catch (err) {
         const error = err as Error | DatabaseError;
         this.logger.error(`Error tracking feature usage: ${error.message}`);
         res.status(400).send(error.message);
      }
   }
}
