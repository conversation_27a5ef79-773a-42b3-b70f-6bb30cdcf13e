import { SocialMediaType } from '../types';
import mongoose, { Schema, Document } from 'mongoose';

export interface Media {
   media_id: string;
   image_link: string;
}

export interface IContentCalendar extends Document {
   time: string;
   date: string;
   social_media_type: SocialMediaType;
   media: Media[];
   post_data?: string;
   client_id: string;
   is_schedule?: boolean;
   uuid: string;
}

const mediaSchema: Schema = new Schema({
   media_id: { type: String, required: true },
   image_link: { type: String, required: true },
});

const ContentCalendarSchema: Schema = new Schema(
   {
      time: { type: String, required: true },
      date: { type: String, required: true },
      social_media_type: {
         type: String,
         required: true,
         enum: Object.values(SocialMediaType),
      },
      post_data: { type: String },
      media: { type: [mediaSchema], default: [] },
      is_schedule: { type: Boolean, default: false },
      client_id: { type: String, required: true },
      uuid: { type: String, required: true },
   },
   {
      timestamps: true,
   },
);

export default mongoose.model<IContentCalendar>(
   'twitters',
   ContentCalendarSchema,
);
