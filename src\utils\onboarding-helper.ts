import { Config } from '../config';

export const JS_SCRIPT = (_newClient_id: string): string => {
   return `<script>
    (function(f, l, a, b, t, i, c) {
      f[a] = f[a] || function() {
        (f[a].q = f[a].q || []).push(arguments)
      };
      i = l.createElement(b);
      i.async = 1;
      i.src ="${Config.SCRIPT_SRC}"+t;
      c = l.getElementsByTagName(b)[0];
      c.parentNode.insertBefore(i, c);
    })(window, document, "flable", "script", "${_newClient_id}");
  </script>`;
};
