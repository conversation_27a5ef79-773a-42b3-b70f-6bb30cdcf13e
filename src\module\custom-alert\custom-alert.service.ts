import { CustomAlertModel } from './custom.alert.model';
import * as types from './custom-alert-types';
import { Config } from '../../config';
import { EntityManager } from 'typeorm';
import { EmailService } from '../../services/EmailService';
import { WhatsappService } from '../../services/WhatsappService';
import {
   alertEmailTemplate,
   evaluateKpiRules,
   returnValidValues,
} from './custom-alert-helpers';

export class CustomAlertService {
   constructor(
      private customAlertModel: CustomAlertModel,
      private emailService: EmailService,
      private whatsappService: WhatsappService,
      private entityManager: EntityManager,
   ) {}

   async getAllAlerts(
      clientId: string,
      userId: string,
   ): Promise<types.CustomAlert[]> {
      try {
         if (!clientId || !userId) {
            throw new Error('Client ID and User ID are required');
         }

         const alerts = await this.customAlertModel.getAllAlerts(
            clientId,
            userId,
         );

         return alerts;
      } catch (error) {
         if (error instanceof Error) {
            throw new Error(`Error fetching all alerts: ${error.message}`);
         }
         throw error;
      }
   }

   async getAlertById(alertId: string) {
      try {
         if (!alertId) {
            throw new Error('Alert ID is required for fetching');
         }

         const alert = await this.customAlertModel.getAlertById(alertId);

         if (!alert) {
            throw new Error('Alert not found');
         }

         return alert;
      } catch (error) {
         if (error instanceof Error) {
            throw new Error(`Error fetching alert by ID: ${error.message}`);
         }
         throw error;
      }
   }

   async createOrUpdateAlert(data: types.CustomAlert) {
      try {
         const alert = this.customAlertModel.createOrUpdateAlert(data);

         if (!alert) {
            throw new Error('Failed to create or update alert');
         }

         return {
            status: 'success',
            message: 'Alert created or updated successfully',
         };
      } catch (error) {
         if (error instanceof Error) {
            throw new Error(`Error creating alert: ${error.message}`);
         }
         throw error;
      }
   }

   async deleteAlert(alertId: string) {
      try {
         if (!alertId) {
            throw new Error('Alert ID is required for deletion');
         }

         await this.customAlertModel.deleteAlert(alertId);
      } catch (error) {
         if (error instanceof Error) {
            throw new Error(`Error deleting alert: ${error.message}`);
         }
         throw error;
      }
   }

   async deleteMultipleAlerts(alertIds: number[]) {
      try {
         if (!alertIds || alertIds.length === 0) {
            throw new Error('Alert IDs are required for deletion');
         }

         await this.customAlertModel.deleteMultipleAlerts(alertIds);
      } catch (error) {
         if (error instanceof Error) {
            throw new Error(`Error deleting multiple alerts: ${error.message}`);
         }
         throw error;
      }
   }

   async fetchOptions(clientId: string) {
      try {
         if (!clientId) {
            throw new Error('Client ID is required to fetch options');
         }

         const options = this.customAlertModel.fetchOptions(clientId);
         return options;
      } catch (error) {
         if (error instanceof Error) {
            throw new Error(`Error fetching options: ${error.message}`);
         }
         throw error;
      }
   }

   private async sendAlertNotification(alert: types.CustomAlert) {
      try {
         const { email_recipients } = alert;

         if (!email_recipients || email_recipients.length === 0) {
            throw new Error(
               'No email recipients specified for alert notification',
            );
         }

         email_recipients.forEach(async (recipient) => {
            const {
               channel,
               campaigns,
               multipleCampaigns,
               metrics,
               multipleMetrics,
               targetPeriodDays,
               referencePeriodDays,
               kpiRules,
               questionText,
            } = returnValidValues(alert);

            const emailInfo = {
               to: recipient,
               subject: 'Flable AI Alert',
               text: 'Flable AI Alert',
               html: alertEmailTemplate(
                  recipient,
                  channel,
                  campaigns,
                  multipleCampaigns,
                  metrics,
                  multipleMetrics,
                  targetPeriodDays,
                  referencePeriodDays,
                  kpiRules,
                  questionText,
               ),
            };

            await this.emailService.sendEmail(emailInfo);
         });
      } catch (error) {
         if (error instanceof Error) {
            throw new Error(
               `Error sending alert notification: ${error.message}`,
            );
         }
         throw error;
      }
   }

   async checkAlertConditions(alerts: types.CustomAlert[]) {
      try {
         if (!alerts || alerts.length === 0) {
            throw new Error('Alert data is required to check conditions');
         }

         alerts.forEach(async (alert) => {
            const {
               client_id,
               user_id,
               alert_name,
               alert_conditions,
               email_recipients,
            } = alert;

            const channel = alert_conditions.channel;
            const isCampaignLevel = alert_conditions.campaigns.length > 0;

            if (!isCampaignLevel) {
               const targetPeriodAvg =
                  await this.customAlertModel.fetchDashboardMetrics({
                     clientId: client_id,
                     channel: alert_conditions.channel,
                     metrics: alert_conditions.metrics,
                     period: alert_conditions.target_period.value,
                  });

               const referencePeriodAvg =
                  await this.customAlertModel.fetchDashboardMetrics({
                     clientId: client_id,
                     channel: alert_conditions.channel,
                     metrics: alert_conditions.metrics,
                     period: alert_conditions.reference_period.value,
                  });

               if (!targetPeriodAvg || !referencePeriodAvg) {
                  throw new Error(`Failed to fetch dashboard metrics`);
               }

               const conditionMatch = evaluateKpiRules(
                  targetPeriodAvg,
                  referencePeriodAvg,
                  alert_conditions.kpi_rules,
               );

               if (conditionMatch) {
                  await this.sendAlertNotification(alert);
               }
            } else {
               let campaigns: boolean[] = [];

               if (channel === 'facebookads') {
                  campaigns = await Promise.all(
                     alert_conditions.campaigns.map(async (campaign) => {
                        const targetPeriodAvg =
                           await this.customAlertModel.fetchMetaCampaignMetrics(
                              {
                                 clientId: client_id,
                                 campaign: campaign.id,
                                 metrics: alert_conditions.metrics,
                                 period: alert_conditions.target_period.value,
                              },
                           );

                        const referencePeriodAvg =
                           await this.customAlertModel.fetchMetaCampaignMetrics(
                              {
                                 clientId: client_id,
                                 campaign: campaign.id,
                                 metrics: alert_conditions.metrics,
                                 period:
                                    alert_conditions.reference_period.value,
                              },
                           );

                        if (!targetPeriodAvg || !referencePeriodAvg) {
                           throw new Error(`Failed to fetch dashboard metrics`);
                        }

                        const conditionMatch = evaluateKpiRules(
                           targetPeriodAvg,
                           referencePeriodAvg,
                           alert_conditions.kpi_rules,
                        );

                        return !!conditionMatch;
                     }),
                  );
               }

               if (channel === 'googleads') {
                  campaigns = await Promise.all(
                     alert_conditions.campaigns.map(async (campaign) => {
                        const targetPeriodAvg =
                           await this.customAlertModel.fetchGoogleCampaignMetrics(
                              {
                                 clientId: client_id,
                                 campaign: campaign.id,
                                 metrics: alert_conditions.metrics,
                                 period: alert_conditions.target_period.value,
                              },
                           );

                        const referencePeriodAvg =
                           await this.customAlertModel.fetchGoogleCampaignMetrics(
                              {
                                 clientId: client_id,
                                 campaign: campaign.id,
                                 metrics: alert_conditions.metrics,
                                 period:
                                    alert_conditions.reference_period.value,
                              },
                           );

                        if (!targetPeriodAvg || !referencePeriodAvg) {
                           throw new Error(`Failed to fetch dashboard metrics`);
                        }

                        const conditionMatch = evaluateKpiRules(
                           targetPeriodAvg,
                           referencePeriodAvg,
                           alert_conditions.kpi_rules,
                        );
                        return !!conditionMatch;
                     }),
                  );
               }

               if (campaigns.length > 0 && campaigns.every((c) => c)) {
                  await this.sendAlertNotification(alert);
               }
            }
         });

         return {
            status: 'success',
            message: 'All alert conditions checked successfully',
         };
      } catch (error) {
         if (error instanceof Error) {
            throw new Error(
               `Error checking alert conditions: ${error.message}`,
            );
         }

         return null;
      }
   }
}
