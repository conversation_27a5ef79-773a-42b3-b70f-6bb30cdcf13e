import { DatabaseError } from 'pg';
import { EntityManager } from 'typeorm';
import { Config } from '../../../config';
import * as types from '../../../types/agents/alerting-agent/alerting-agent.types';
import { CampaignKPIs, GoogleAdsK<PERSON>Keys, KPIKeys } from '@/types';
import { KPI_CALCULATE } from '../../../utils/performanceInsightsHelper';
import { GOOGLE_KPI_CALCULATE } from '../../../utils/googleAdsKpisCalculator';

export class AlertingAgentModel {
   constructor(private entityManager: EntityManager) {}

   async fetchAllSessionsByUserID(
      payload: types.FetchAllSessionsByUserIDPayload,
   ): Promise<types.AlertingAgentChat[]> {
      try {
         const { client_id, user_id, page } = payload;

         const LIMIT = 10;
         const OFFSET = (page - 1) * LIMIT;

         const query = `
                SELECT DISTINCT ON (session_id) *
                FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.alerting_agent_chat
                WHERE client_id = $1
                AND user_id = $2
                ORDER BY session_id DESC
                LIMIT $3 OFFSET $4;
             `;

         const result: types.AlertingAgentChat[] =
            await this.entityManager.query(query, [
               client_id,
               user_id,
               LIMIT,
               OFFSET,
            ]);

         return result;
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async fetchHistoryBySessionID(
      payload: types.FetchHistoryBySessionIDPayload,
   ): Promise<types.AlertingAgentChat[]> {
      try {
         const { client_id, user_id, session_id } = payload;

         const query = `
               SELECT * FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.alerting_agent_chat
               WHERE client_id = $1
               AND user_id = $2
               AND session_id = $3
               ORDER BY created_at ASC;
            `;

         const result: types.AlertingAgentChat[] =
            await this.entityManager.query(query, [
               client_id,
               user_id,
               session_id,
            ]);

         return result;
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async addChatToSessionHistory(
      payload: types.AddChatToSessionHistoryPayload,
   ): Promise<void> {
      try {
         const {
            client_id,
            user_id,
            session_id,
            chat_id,
            user_query,
            response_status,
            final_response,
            session_summary,
            response_time,
         } = payload;

         const query = `
            INSERT INTO ${Config.DB_Postgres_AGENTS_SCHEMA}.alerting_agent_chat
            (client_id, user_id, session_id, chat_id, user_query, response_status,
             final_response, session_summary, response_time)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            ON CONFLICT (chat_id) DO UPDATE SET
            response_status = EXCLUDED.response_status,
            final_response = EXCLUDED.final_response,
            session_summary = EXCLUDED.session_summary,
            response_time = EXCLUDED.response_time,
            updated_at = NOW();
         `;

         await this.entityManager.query(query, [
            client_id,
            user_id,
            session_id,
            chat_id,
            user_query,
            response_status,
            final_response,
            session_summary,
            response_time,
         ]);
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async fetchChatByChatId(
      payload: types.FetchChatByChatIdPayload,
   ): Promise<types.AlertingAgentChat> {
      try {
         const { client_id, user_id, session_id, chat_id } = payload;

         const query = `
                SELECT * FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.alerting_agent_chat
                WHERE client_id = $1
                AND user_id = $2
                AND session_id = $3
                AND chat_id = $4;
             `;

         const result: types.AlertingAgentChat[] =
            await this.entityManager.query(query, [
               client_id,
               user_id,
               session_id,
               chat_id,
            ]);

         if (result.length === 0) {
            throw new Error(`Chat with ID ${chat_id} not found`);
         }

         return result[0];
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async fetchAlertsBySessionID(
      payload: types.FetchAlertsBySessionIDPayload,
   ): Promise<types.AlertingAgentAlert[]> {
      try {
         const { client_id, user_id, session_id } = payload;

         const query = `
                SELECT * FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.alerting_agent_alerts
                WHERE client_id = $1
                AND user_id = $2
                AND session_id = $3;
             `;

         const result: types.AlertingAgentAlert[] =
            await this.entityManager.query(query, [
               client_id,
               user_id,
               session_id,
            ]);

         return result;
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async fetchAllAlerts(
      payload: types.FetchAllAlertsPayload,
   ): Promise<types.AlertingAgentAlert[]> {
      try {
         const { client_id, user_id } = payload;

         const query = `
                SELECT * FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.alerting_agent_alerts
                WHERE client_id = $1
                AND user_id = $2
                ORDER BY alert_time DESC;
             `;

         const result: types.AlertingAgentAlert[] =
            await this.entityManager.query(query, [client_id, user_id]);

         return result;
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async fetchAlertByID(
      payload: types.FetchAlertByIdPayload,
   ): Promise<types.AlertingAgentAlert> {
      try {
         const { alert_id } = payload;

         const query = `
                SELECT * FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.alerting_agent_alerts
                WHERE alert_id = $1;
             `;

         const result: types.AlertingAgentAlert[] =
            await this.entityManager.query(query, [alert_id]);

         if (result.length === 0) {
            throw new Error(`Alert with ID ${alert_id} not found`);
         }

         return result[0];
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async createAlert(
      payload: types.CreateAlertPayload,
   ): Promise<types.AlertingAgentAlert> {
      try {
         const {
            client_id,
            user_id,
            chat_id,
            session_id,
            alert_name,
            alert_description,
            recipients,
            channel,
            campaigns,
            kpi,
            trend,
            value,
            value_type,
            comparison,
            comparison_type,
            alert_status,
            alert_time,
            user_timezone,
            emails_triggered,
            timestamps_when_triggered,
            alert_instruction,
            alert_timeframe,
         } = payload;

         const query = `
            INSERT INTO ${Config.DB_Postgres_AGENTS_SCHEMA}.alerting_agent_alerts
            (client_id, user_id, chat_id, session_id, alert_name, alert_description,
             recipients, channel, campaigns, kpi, trend, value,
             value_type, comparison, comparison_type, alert_status, alert_time,
             user_timezone, emails_triggered, timestamps_when_triggered,
             alert_instruction, alert_timeframe) VALUES
            ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13,
             $14, $15, $16, $17, $18, $19, $20, $21, $22)
            RETURNING *;
         `;

         const result = await this.entityManager.query(query, [
            client_id,
            user_id,
            chat_id,
            session_id,
            alert_name,
            alert_description,
            recipients,
            channel,
            JSON.stringify(campaigns),
            kpi,
            trend,
            value,
            value_type,
            comparison,
            comparison_type,
            alert_status,
            alert_time,
            user_timezone,
            emails_triggered,
            timestamps_when_triggered,
            alert_instruction,
            alert_timeframe,
         ]);

         return result[0];
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async updateAlert(
      payload: types.UpdateAlertPayload,
   ): Promise<types.AlertingAgentAlert> {
      try {
         const {
            alert_id,
            client_id,
            user_id,
            chat_id,
            session_id,
            alert_name,
            alert_description,
            recipients,
            channel,
            campaigns,
            kpi,
            trend,
            value,
            value_type,
            comparison,
            comparison_type,
            alert_status,
            alert_time,
            user_timezone,
            emails_triggered,
            timestamps_when_triggered,
            alert_instruction,
            alert_timeframe,
         } = payload;

         const query = `
               UPDATE ${Config.DB_Postgres_AGENTS_SCHEMA}.alerting_agent_alerts
               SET client_id = $1, user_id = $2, chat_id = $3, session_id = $4, 
                   alert_name = $5, alert_description = $6, recipients = $7, 
                   channel = $8, campaigns = $9, kpi = $10, trend = $11, value = $12, 
                   value_type = $13, comparison = $14, comparison_type = $15, 
                   alert_status = $16, alert_time = $17, user_timezone = $18, 
                   emails_triggered = $19, timestamps_when_triggered = $20, 
                   alert_instruction = $21, alert_timeframe = $22
               WHERE alert_id = $23
               RETURNING *;
         `;

         return await this.entityManager.query(query, [
            client_id,
            user_id,
            chat_id,
            session_id,
            alert_name,
            alert_description,
            recipients,
            channel,
            JSON.stringify(campaigns),
            kpi,
            trend,
            value,
            value_type,
            comparison,
            comparison_type,
            alert_status,
            alert_time,
            user_timezone,
            emails_triggered,
            timestamps_when_triggered,
            alert_instruction,
            alert_timeframe,
            alert_id,
         ]);
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async deleteAlert(payload: types.DeleteAlertPayload) {
      try {
         const { alert_id } = payload;

         const query = `DELETE FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.alerting_agent_alerts WHERE alert_id = $1;`;

         await this.entityManager.query(query, [alert_id]);

         return {
            response: {
               status: 'Success',
               message: 'Alert deleted successfully',
            },
         };
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async deleteMultipleAlerts(payload: types.DeleteMultipleAlertsPayload) {
      try {
         const { alert_ids } = payload;

         const query = `DELETE FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.alerting_agent_alerts WHERE alert_id = ANY($1);`;

         await this.entityManager.query(query, [alert_ids.map(Number)]);

         return {
            response: {
               status: 'Success',
               message: 'Alerts deleted successfully',
            },
         };
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async pauseUnpauseAlert(payload: types.PauseUnpauseAlertPayload) {
      try {
         const query = `UPDATE ${Config.DB_Postgres_AGENTS_SCHEMA}.alerting_agent_alerts 
            SET alert_status = 
                CASE 
                    WHEN alert_status = 'PAUSED' THEN 'ACTIVE' 
                    ELSE 'PAUSED' 
                END 
            WHERE alert_id = $1`;

         await this.entityManager.query(query, [payload.alert_id]);

         return {
            response: {
               status: 'Success',
               message: 'Alert status updated successfully',
            },
         };
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async updateEmailRecipients(payload: types.UpdateEmailRecipientsPayload) {
      try {
         const { alert_id, recipients } = payload;

         const query = `UPDATE ${Config.DB_Postgres_AGENTS_SCHEMA}.alerting_agent_alerts
            SET recipients = $1
            WHERE alert_id = $2;`;

         await this.entityManager.query(query, [recipients, alert_id]);

         return {
            response: {
               status: 'Success',
               message: 'Email recipients updated successfully',
            },
         };
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async fetchTodaysKPIValue(
      table: string,
      keys: string[],
      isCampaignLevel: boolean,
      dateColumn: string,
   ): Promise<number | null> {
      if (!isCampaignLevel) {
         const query = `SELECT * FROM ${table} WHERE client_id = $1 AND category = $2 AND kpi_names = $3 AND date = CURRENT_DATE - INTERVAL '1 day';`;
         const result = await this.entityManager.query(query, keys);

         return result[0]?.kpi_value ?? null;
      }

      const campaignIds = keys[1].split(',').map((id) => String(id.trim()));
      const placeholders = campaignIds
         .map((_, idx) => `$${idx + 2}`)
         .join(', ');
      const query = `SELECT * FROM ${table} WHERE client_id = $1 AND campaign_id IN (${placeholders}) AND kpi_name = $${
         campaignIds.length + 2
      } AND ${dateColumn} = CURRENT_DATE - INTERVAL '1 day';`;
      const result = await this.entityManager.query(query, [
         keys[0],
         ...campaignIds,
         keys[2],
      ]);

      if (result.length === 0) {
         return null;
      }

      let kpiAgg = 0;
      const kpiName = keys[2];

      const isCalculatedKPI = table.includes('googleads')
         ? GOOGLE_KPI_CALCULATE[kpiName as GoogleAdsKpiKeys]
         : KPI_CALCULATE[kpiName as KPIKeys];

      if (result.length > 0) {
         if (!!isCalculatedKPI) {
            kpiAgg =
               result.reduce((sum: number, row: CampaignKPIs) => {
                  return typeof row.kpi_value === 'number'
                     ? sum + row.kpi_value
                     : sum;
               }, 0) / result.length;
         } else {
            kpiAgg = result.reduce((sum: number, row: CampaignKPIs) => {
               return typeof row.kpi_value === 'number'
                  ? sum + row.kpi_value
                  : sum;
            }, 0);
         }
      }

      return kpiAgg ?? null;
   }

   async fetchAverageKPIValue(
      table: string,
      keys: string[],
      timeframe: string,
      isCampaignLevel: boolean,
      dateColumn: string,
   ): Promise<number | null> {
      if (!isCampaignLevel) {
         const query = `
               SELECT AVG(kpi_value) AS avg_kpi
               FROM ${table}
               WHERE client_id = $1
               AND category = $2
               AND kpi_names = $3
               AND DATE(date) BETWEEN CURRENT_DATE - INTERVAL '${timeframe} days' AND CURRENT_DATE - INTERVAL '1 day';
            `;

         const result = await this.entityManager.query(query, keys);
         return result[0]?.avg_kpi ?? null;
      }

      const campaignIds = keys[1].split(',').map((id) => String(id.trim()));
      const placeholders = campaignIds
         .map((_, idx) => `$${idx + 2}`)
         .join(', ');

      const query = `
           SELECT AVG(kpi_value) AS avg_kpi
           FROM ${table}
           WHERE client_id = $1
           AND campaign_id IN (${placeholders})
           AND kpi_name = $${campaignIds.length + 2}
           AND DATE(${dateColumn}) BETWEEN CURRENT_DATE - INTERVAL '${timeframe} days' AND CURRENT_DATE - INTERVAL '1 day';
         `;

      const result = await this.entityManager.query(query, [
         keys[0],
         ...campaignIds,
         keys[2],
      ]);

      if (result.length === 0) {
         return null;
      }

      return parseFloat(result[0]?.avg_kpi ?? 'NaN');
   }
}
