import { Repository } from 'typeorm';
import bcrypt from 'bcryptjs';
import { Login } from '../entity/Login';
import {
   LoginDto,
   User,
   UserLoginStatus,
   UserLoginStatusResponse,
   UserToken,
   UserTokenResponse,
} from '../types';

export class LoginService {
   constructor(private loginRepository: Repository<Login>) {}

   async login(payload: LoginDto) {
      const { email, password } = payload;

      const user = await this.findUserByEmail(email);

      if (!user) throw new Error('No user found');

      const isPasswordVerified = await bcrypt.compare(password, user.password);

      if (!isPasswordVerified) throw new Error('Invalid credentials');

      const loginStatus = await this.validateUserLogin(email, user.password);

      if (!loginStatus) throw new Error('Unauthenticated');

      const tokenDetails = await this.getTokenDetails(
         loginStatus.client_id,
         payload.email,
      );
      return { fullName: user.full_name, tokenDetails };
   }

   private async findUserByEmail(email: string): Promise<User | null> {
      const result = (await this.loginRepository.query(
         `SELECT * FROM config.user_login WHERE email_address = $1`,
         [email],
      )) as User[];

      return result.length ? result[0] : null;
   }

   private async validateUserLogin(
      email: string,
      password: string,
   ): Promise<UserLoginStatus | null> {
      const result = (await this.loginRepository.query(
         `SELECT * FROM config.fn_user_login_validate('[{"email_address":"${email}","password":"${password}"}]')`,
      )) as UserLoginStatusResponse[];
      return result.length ? result[0].user_login_status[0] : null;
   }

   private async getTokenDetails(
      clientId: string,
      email: string,
   ): Promise<UserToken[] | null> {
      const result = (await this.loginRepository.query(
         `SELECT config.fn_user_token_get($1, $2)`,
         [clientId, email],
      )) as UserTokenResponse[];
      return result.length ? result[0].fn_user_token_get : null;
   }
}
