import express from 'express';
import asyncHandler from '../../../midddleware/async-handler';

import { OnboardingService } from '../../../services/OnboardingService';
import { OnboardingController } from '../../../controllers/OnboardingController';
import { AppDataSource } from '../../../config/data-source';
import { logger } from '../../../config/logger';
import { EmailService } from '../../../services/EmailService';
import { AuthService } from '../../../services/AuthService';

const router = express.Router();

const entityManager = AppDataSource.manager;
const emailService = new EmailService();
const authService = new AuthService(logger, entityManager, emailService);
const onboardingService = new OnboardingService(
   logger,
   entityManager,
   emailService,
   authService,
);
const onboardingController = new OnboardingController(
   onboardingService,
   logger,
);

router
   .route('/master_list')
   .get(
      asyncHandler(
         onboardingController.getUserMasterList.bind(onboardingController),
      ),
   );

router
   .route('/snippet')
   .get(
      asyncHandler(onboardingController.getSnippet.bind(onboardingController)),
   )
   .post(
      asyncHandler(
         onboardingController.sendSnippetEmail.bind(onboardingController),
      ),
   );

router
   .route('/snippet/test_connection')
   .post(
      asyncHandler(
         onboardingController.testConnection.bind(onboardingController),
      ),
   );

router
   .route('/user')
   .get(
      asyncHandler(
         onboardingController.handleGetUserDetails.bind(onboardingController),
      ),
   )
   .post(
      asyncHandler(
         onboardingController.handleCreateUser.bind(onboardingController),
      ),
   )
   .put(
      asyncHandler(
         onboardingController.handleUpdateRegisterProgress.bind(
            onboardingController,
         ),
      ),
   );

router
   .route('/user/social')
   .get(
      asyncHandler(
         onboardingController.handleGetUserSocialDetails.bind(
            onboardingController,
         ),
      ),
   );

router
   .route('/user/company')
   .get(
      asyncHandler(
         onboardingController.handleGetUserCompanyDetails.bind(
            onboardingController,
         ),
      ),
   )
   .post(
      asyncHandler(
         onboardingController.handleAddNewCompany.bind(onboardingController),
      ),
   );

export { router as onboardingRouter };
