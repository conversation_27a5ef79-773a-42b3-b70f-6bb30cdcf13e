import express from 'express';
import asyncHandler from '../../../midddleware/async-handler';

import { logger } from '../../../config/logger';
import { MetaAdsController } from '../../../controllers/MetaAdsController';
import { AppDataSource } from '../../../config/data-source';
import { MetaAdsService } from '../../../services/MetaAdsService';
const router = express.Router();

const entityManager = AppDataSource.manager;
const metaAdsService = new MetaAdsService(entityManager, logger);
const metaAdsController = new MetaAdsController(metaAdsService);

router.get(
   '/callback',
   asyncHandler(metaAdsController.fetchRefreshToken.bind(metaAdsController)),
);

router.get(
   '/login',
   asyncHandler(
      metaAdsController.getGoogleAnalyticsAuthUrl.bind(metaAdsController),
   ),
);
router.post(
   '/accounts',
   async<PERSON>and<PERSON>(metaAdsController.listAdAccounts.bind(metaAdsController)),
);
export { router as metaAdsRouter };
