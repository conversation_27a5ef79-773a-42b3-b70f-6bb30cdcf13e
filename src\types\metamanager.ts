export interface CreateCampaignRequest {
   client_id: string;
   name: string;
   objective: string;
   status: 'ACTIVE' | 'PAUSED';
   special_ad_categories: string[];
}

export interface CreateAdSetRequest {
   client_id: string;
   campaign_id: string;
   name: string;
   daily_budget: number;
   billing_event: string;
   bid_strategy: string;
   optimization_goal: string;
   targeting: {
      age_min: number;
      age_max: number;
      geo_locations: {
         countries: string[];
      };
   };
   status: 'ACTIVE' | 'PAUSED';
}

export interface CreateAdCreativeRequest {
   client_id: string;
   name: string;
   object_story_spec: {
      page_id: string;
      link_data: {
         link: string;
         message: string;
         picture: string;
         call_to_action: {
            type: string;
            value: {
               link: string;
            };
         };
      };
   };
}

export interface CreateAdRequest {
   client_id: string;
   name: string;
   adset_id: string;
   creative: { creative_id: string };
   status: 'ACTIVE' | 'PAUSED';
}

export interface DeleteAdRequest {
   ad_id: string;
}

export interface AdResponse {
   success: boolean;
   id?: string;
   message?: string;
}

export interface GenerateImageRequest {
   imageUrl: string;
   title: string;
   body: string;
}

export interface ChatHistory {
   question: string;
   response: string;
}

export interface ChatHistoryPayload {
   chat_id: string;
   chat_name: string;
   chat_history: ChatHistory[];
   chat_level: string;
   campaign_details: string;
   adset_details: string;
   ad_creative_details: string;
   ad_details: string;
   created_time: string;
}
export interface CampaignDetails {
   name: string;
   campaign_id: string;
   objective: string;
   product_url: string;
   buying_type: string;
   campaign_budget_optimization: boolean;
   daily_budget: string | number;
}
export interface StreamingMessage {
   id: string;
   type:
      | 'campaign'
      | 'adset-analysis'
      | 'adset-creation'
      | 'ad-creative'
      | 'ad';
   title: string;
   content: string;
   isStreaming: boolean;
   timestamp: Date;
   data?: unknown;
   tableData?: TableSection[];
   imageUrl?: string;
   isComplete: boolean;
}
export type ChatLevels =
   | 'campaign'
   | 'adset-initial'
   | 'adset-final'
   | 'ad-creative'
   | 'confirm-creative'
   | 'ad'
   | 'completed';
export interface AutomationStep {
   step: number;
   name: string;
   status: 'pending' | 'in-progress' | 'completed' | 'error';
   chatLevel: ChatLevels;
}
export interface TableSection {
   sectionTitle: string;
   rows: { [key: string]: string }[];
}
export interface AdsetDetails
   extends Omit<ResponseAdSetData, 'targeting_analysis'> {
   targeting_analysis: targeting;
}
export interface Interest {
   id: string;
   name: string;
}

export interface GeoLocation {
   countries: string[];
}
export interface behavior {
   id: string;
   name: string;
}
export interface targeting {
   age_min: number;
   age_max: number;
   interests: Interest[];
   geo_locations: GeoLocation;
   behaviors: behavior[];
   publisher_platforms: string[];
   facebook_positions: string[];
   instagram_positions: string[];
}
export interface ResponseTargetingAnalysis {
   geo_locations: {
      countries: string[];
   };
   age: { [key: string]: string }[];
   audience_interests: { [key: string]: string }[];
   Placement: { [key: string]: string }[];
   region: { [key: string]: string }[];
   audience_behaviors: { [key: string]: string }[];
}
interface promptObj {
   pixel_id: string;
   custom_event_type: string;
}
export interface ResponseAdSetData {
   adset_id: string;
   adset_name: string;
   daily_budget?: number;
   bid_amount: number;
   targeting_analysis: ResponseTargetingAnalysis;
   options: object;
   chat_response: object;
   bid_strategy: string;
   billing_event: string;
   optimization_goal: string;
   promoted_object?: promptObj;
}
export interface AdsetData {
   adset_name: string;
   daily_budget?: number;
   bid_amount: number;
   targeting_analysis?: {
      geo_locations: {
         countries: string[];
      };
      age: { [key: string]: string }[];
      audience_interests: { [key: string]: string }[];
      Placement: { [key: string]: string }[];
      audience_behaviors: { [key: string]: string }[];
      region: { [key: string]: string }[];
   };
   bid_strategy: string;
   billing_event: string;
   optimization_goal: string;
   promoted_object?: {
      pixel_id: string;
      custom_event_type: string;
   };
}
export interface MetaAutoAgentHistoryPayload {
   client_id: string;
   user_id: string;
   session_id: string;
   chat_id: string;
   user_query: string;
   final_response: string;
   campaign_details?: CampaignDetails;
   adset_details?: AdsetDetails;
   adset_data?: AdsetData;
   ad_creative_id?: string;
   ad_id?: string;
   summary_content?: string;
   streaming_messages?: StreamingMessage[];
   step_statuses?: AutomationStep[];
}
export interface MetaAdsAutoAgentChat {
   id: number;
   chat_id: string;
   session_id: string;
   client_id: string;
   user_id: string;
   user_query: string;
   final_response: string;
   campaign_details: CampaignDetails | null;
   adset_details: AdsetDetails | null;
   adset_data: AdsetData | null;
   ad_creative_id: string | null;
   ad_id: string | null;
   summary_content: string | null;
   streaming_messages: StreamingMessage[] | null;
   step_statuses: AutomationStep[] | null;
   created_at?: string;
   updated_at?: string;
}
export interface ChatHistoryResponse {
   status: string;
   message: string;
   data?: any;
}

export interface scrapeResponse {
   name: string;
   image: string;
}
export interface creativeImagePayload {
   caption: string;
   description: string;
}
export interface creativeImageResponse {
   imageUrl: string;
   promptUsed: string;
}
