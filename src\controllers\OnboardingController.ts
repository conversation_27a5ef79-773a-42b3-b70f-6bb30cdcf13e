import { Request, Response } from 'express';
import { OnboardingService } from '../services/OnboardingService';
import { Logger } from 'winston';
import {
   FetchSnippetPayload,
   FetchUserPayload,
   SendSnippetPayload,
   CreateUserPayload,
   UpdateRegisterProgressPayload,
   GetUserSocialDetailsPayload,
   GetUserCompanyDetailsPayload,
   TestConnectionPayload,
} from '../types';

export class OnboardingController {
   constructor(
      private onboardingService: OnboardingService,
      private logger: Logger,
   ) {}

   async getUserMasterList(req: Request, res: Response) {
      const result = await this.onboardingService.fetchUserMasterList();
      this.logger.info('Fetched user master list successfully');
      res.status(200).send(result.response);
   }

   async handleGetUserDetails(req: Request, res: Response) {
      const result = await this.onboardingService.getUserDetails(
         req.query as FetchUserPayload,
      );
      this.logger.info('Fetched user details successfully');
      res.status(200).send(result.response);
   }

   async handleCreateUser(req: Request, res: Response) {
      const result = await this.onboardingService.createUser(
         req.body as CreateUserPayload,
      );
      this.logger.info('Created user successfully');
      res.status(200).send(result.response);
   }

   async handleUpdateRegisterProgress(req: Request, res: Response) {
      const result = await this.onboardingService.updateRegisterProgress(
         req.body as UpdateRegisterProgressPayload,
      );
      this.logger.info('Updated user details successfully');
      res.status(200).send(result.response);
   }

   async getSnippet(req: Request, res: Response) {
      const result = await this.onboardingService.getSnippet(
         req.query as FetchSnippetPayload,
      );
      this.logger.info('Fetched snippet successfully');
      res.status(200).send(result.response);
   }

   async sendSnippetEmail(req: Request, res: Response) {
      const result = await this.onboardingService.sendSnippetEmail(
         req.body as SendSnippetPayload,
      );
      this.logger.info('Snippet sent successfully');
      res.status(200).send(result.response);
   }

   async handleGetUserSocialDetails(req: Request, res: Response) {
      const result = await this.onboardingService.getUserSocialDetails(
         req.query as GetUserSocialDetailsPayload,
      );
      this.logger.info('Fetched user social details successfully');
      res.status(200).send(result.response);
   }

   async handleGetUserCompanyDetails(req: Request, res: Response) {
      const result = await this.onboardingService.getUserCompanyDetails(
         req.query as GetUserCompanyDetailsPayload,
      );
      this.logger.info('Fetched user company details successfully');
      res.status(200).send(result.response);
   }

   async handleAddNewCompany(req: Request, res: Response) {
      const result = await this.onboardingService.addNewCompany(
         req.body as GetUserCompanyDetailsPayload,
      );
      this.logger.info('Added new company successfully');
      res.status(200).send(result.response);
   }

   async testConnection(req: Request, res: Response) {
      const result = await this.onboardingService.testConnection(
         req.body as TestConnectionPayload,
      );
      this.logger.info('Test connection successful');
      res.status(200).send(result.response);
   }
}
