import express from 'express';

import { ShipRocketController } from '../../../controllers/ShipRocketController';
import { ShipRocketService } from '../../../services/ShipRocketService';

import asyncHandler from '../../../midddleware/async-handler';

const router = express.Router();

const shipRocketService = new ShipRocketService();
const shipRocketController = new ShipRocketController(shipRocketService);

router.post(
   '/validate',
   asyncHandler(
      shipRocketController.validateCredentials.bind(shipRocketController),
   ),
);

router.post(
   '/encrypt',
   shipRocketController.encryptFields.bind(shipRocketController),
);
export { router as shipRocketRouter };
