import { ShipRocketService } from '../services/ShipRocketService';
import { Request, Response } from 'express';
export class ShipRocketController {
   constructor(private shipRocketService: ShipRocketService) {}

   async validateCredentials(req: Request, res: Response) {
      const { email, password } = req.body as {
         email: string;
         password: string;
      };
      if (!email || !password) {
         return res
            .status(400)
            .json({ message: 'Email and password are required' });
      }

      const result = await this.shipRocketService.validateCredentials(
         email,
         password,
      );

      if (!result.valid) {
         return res.status(401).json({ message: result.message, valid: false });
      }

      return res.status(200).json({
         message: 'Credentials are valid',
         valid: true,
      });
   }

   encryptFields(req: Request, res: Response) {
      const { password } = req.body as { password: string };
      if (!password) {
         return res
            .status(400)
            .json({ message: 'Email and password are required' });
      }
      const result = this.shipRocketService.encryptCreds(password);
      res.json(result);
   }
}
