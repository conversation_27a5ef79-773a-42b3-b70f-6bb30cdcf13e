import { <PERSON>ti<PERSON><PERSON><PERSON>ger } from 'typeorm';
import { Config } from '../config';
import { google } from 'googleapis';
import { Logger } from 'winston';
import { GAAccountWithProperties } from '../types/googleanalytics';
import { AxiosResponse } from 'axios';
export class GoogleAnalyticsService {
   //  private tokenCache = new Map<string, { token: string; expiresAt: number }>();

   constructor(
      private readonly entityManager: EntityManager,
      private readonly logger: Logger,
   ) {}

   private createOAuthClient() {
      return new google.auth.OAuth2(
         Config.GOOGLE_CLIENT_ID,
         Config.GOOGLE_CLIENT_SECRET,
         Config.GOOGLE_ANALYTICS_REDIRECT_URI,
      );
   }

   private async getRefreshTokenByClientId(
      clientId: string,
   ): Promise<string | null> {
      const query = `
         SELECT meta_data ->> 'refresh_token' AS refresh_token
         FROM ${Config.DB_Postgres_SCHEMA}.social_analytics
         WHERE client_id = $1 AND meta_data ->> 'refresh_token' IS NOT NULL
      `;
      const result = await this.entityManager.query(query, [clientId]);
      return result?.[0]?.refresh_token ?? null;
   }

   public getGoogleAnalyticsAuthUrl(): string {
      const oauth2Client = this.createOAuthClient();
      return oauth2Client.generateAuthUrl({
         access_type: 'offline',
         scope: ['https://www.googleapis.com/auth/analytics.readonly'],
         prompt: 'consent',
      });
   }

   public async getAccessTokenFromCode(code: string) {
      const oauth2Client = this.createOAuthClient();
      const { tokens } = await oauth2Client.getToken(code);
      return {
         access: tokens.access_token,
         refresh: tokens.refresh_token,
      };
   }

   private async updateMetaData(clientId: string, newData: object) {
      const updateQuery = `
         UPDATE ${Config.DB_Postgres_SCHEMA}.social_analytics
         SET meta_data = COALESCE(meta_data, '{}'::jsonb) || $1::jsonb
         WHERE client_id = $2
      `;
      await this.entityManager.query(updateQuery, [
         JSON.stringify(newData),
         clientId,
      ]);
   }
   private async getGoogleAccessToken(clientId: string): Promise<string> {
      /*const cached = this.tokenCache.get(clientId);
      if (cached && cached.expiresAt > Date.now()) {
         this.logger.info(`Reusing cached token for clientId: ${clientId}`);
         return cached.token;
      }*/

      const refreshToken = await this.getRefreshTokenByClientId(clientId);
      if (!refreshToken) throw new Error('Refresh token not found for client');

      const oauth2Client = this.createOAuthClient();
      oauth2Client.setCredentials({ refresh_token: refreshToken });

      const { credentials } = await oauth2Client.refreshAccessToken();

      const token = credentials.access_token!;
      /* const expiresAt = Date.now() + 55 * 60 * 1000;
      console.log(token);
      this.tokenCache.set(clientId, { token, expiresAt });*/
      return token;
   }

   public async getProperties(
      clientId: string,
   ): Promise<GAAccountWithProperties[]> {
      const accessToken = await this.getGoogleAccessToken(clientId);
      const authClient = this.createOAuthClient();
      authClient.setCredentials({ access_token: accessToken });

      const analyticsAdmin = google.analyticsadmin({
         version: 'v1beta',
         auth: authClient,
      });

      // Get all accounts
      const accountsResponse = await analyticsAdmin.accounts.list();
      const accounts = accountsResponse.data.accounts || [];

      const allAccountsWithProperties = [];

      for (const account of accounts) {
         const accountId = account.name;

         const propertiesResponse = await analyticsAdmin.properties.list({
            filter: `parent:${accountId}`,
         });

         const properties = propertiesResponse.data.properties || [];

         allAccountsWithProperties.push({
            accountId: account.name?.replace('accounts/', '') ?? '',
            regionCode: account.regionCode ?? '',
            displayName: account.displayName ?? '',
            createTime: account.createTime ?? '',
            updateTime: account.updateTime ?? '',
            properties: properties.map((prop) => ({
               propertyId: prop.name?.replace('properties/', '') ?? '',
               parent: prop.parent ?? '',
               createTime: prop.createTime ?? '',
               updateTime: prop.updateTime ?? '',
               displayName: prop.displayName ?? '',
               currencyCode: prop.currencyCode ?? '',
               timeZone: prop.timeZone ?? '',
               account: prop.account ?? '',
               serviceLevel: prop.serviceLevel ?? '',
               propertyType: prop.propertyType ?? '',
            })),
         });
      }

      await this.updateMetaData(clientId, {
         accounts: allAccountsWithProperties,
      });
      return allAccountsWithProperties;
   }

   public async saveProperties(
      clinetId: string,
      selectedProperties: {
         ccount_id: string;
         property_ids: string[];
      },
   ): Promise<{ message: string }> {
      await this.updateMetaData(clinetId, {
         selectedGAprops: selectedProperties,
      });
      return {
         message: 'Properties saved successfully',
      };
   }
}
