export const getFrontendRedirectUrl = (domain: string, path: string) => {
   domain = domain.split(',')[0];
   if (!domain) return '';
   return domain.endsWith('/') ? `${domain}${path}/` : `${domain}/${path}/`;
};

export function throwCustomError(msg: string) {
   throw new Error(msg);
}

export function getLastYearDate() {
   const date = new Date();
   date.setFullYear(date.getFullYear() - 1);
   return date.toISOString().split('.')[0] + 'Z';
}

export function getLastMonthDate(): string {
   const date = new Date();
   date.setMonth(date.getMonth() - 1);
   return date.toISOString().split('T')[0] + 'T00:00:00Z';
}

export function getStartDate(range: string): string {
   const now = new Date();
   switch (range) {
      case '15d':
         now.setDate(now.getDate() - 15);
         break;
      case '1m':
         now.setMonth(now.getMonth() - 1);
         break;
      case '3m':
         now.setMonth(now.getMonth() - 3);
         break;
      case '6m':
         now.setMonth(now.getMonth() - 6);
         break;
      case '1y':
         now.setFullYear(now.getFullYear() - 1);
         break;
   }

   return now.toISOString().split('T')[0] + 'T00:00:00Z';
}
