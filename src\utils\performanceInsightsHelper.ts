import { DateAgg, KPIAggregate, KPIKeys } from '@/types/performanceinsights';
import {
   CostPerLead,
   CPC,
   CPM,
   CPP,
   CTR,
   ROAS,
   FREQUENCY,
   KPIFormula,
   KPIFormulaExtended,
   PurchaseRate,
   LeadConversionRate,
} from '@/types/kpi';

export const KPI_CALCULATE = {
   roas: (dateAgg: DateAgg): number => {
      const roasSums = Object.values(dateAgg).reduce(
         (roas: ROAS, kpis: KPIAggregate) => {
            const weightedRoas =
               Number(kpis.spend || 0) * Number(kpis.roas || 0);
            return {
               weightedRoas: roas.weightedRoas + weightedRoas,
               totalSpent: Number(roas.totalSpent) + Number(kpis.spend || 0),
            };
         },
         { weightedRoas: 0, totalSpent: 0 },
      );
      return roasSums.weightedRoas / roasSums.totalSpent;
   },
   cpc: (dateAgg: DateAgg): number => {
      const cpcSums = Object.values(dateAgg).reduce(
         (cpc: CPC, kpis: KPIAggregate) => {
            return {
               totalSpent: Number(cpc.totalSpent) + Number(kpis.spend || 0),
               totalClicks: Number(cpc.totalClicks) + Number(kpis.clicks || 0),
            };
         },
         {
            totalSpent: 0,
            totalClicks: 0,
         },
      );
      return cpcSums.totalSpent / cpcSums.totalClicks;
   },
   cpp: (dateAgg: DateAgg): number => {
      const cppSums = Object.values(dateAgg).reduce(
         (cpp: CPP, kpis: KPIAggregate) => {
            return {
               totalSpent: Number(cpp.totalSpent) + Number(kpis.spend || 0),
               totalPurchase:
                  Number(cpp.totalPurchase) + Number(kpis.purchase || 0),
            };
         },
         {
            totalSpent: 0,
            totalPurchase: 0,
         },
      );
      return cppSums.totalSpent / cppSums.totalPurchase;
   },
   cpm: (dateAgg: DateAgg): number => {
      const cpmSums = Object.values(dateAgg).reduce(
         (cpm: CPM, kpis: KPIAggregate) => {
            return {
               totalSpent: Number(cpm.totalSpent) + Number(kpis.spend || 0),
               totalImpressions:
                  Number(cpm.totalImpressions) + Number(kpis.impressions || 0),
            };
         },
         {
            totalSpent: 0,
            totalImpressions: 0,
         },
      );
      return (cpmSums.totalSpent / cpmSums.totalImpressions) * 1000;
   },
   cpl: (dateAgg: DateAgg): number => {
      const cplSums = Object.values(dateAgg).reduce(
         (cpl: CostPerLead, kpis: KPIAggregate) => {
            return {
               totalSpent: Number(cpl.totalSpent) + Number(kpis.spend || 0),
               totalLeads: Number(cpl.totalLeads) + Number(kpis.leads || 0),
            };
         },
         {
            totalSpent: 0,
            totalLeads: 0,
         },
      );
      return cplSums.totalSpent / cplSums.totalLeads;
   },
   ctr: (dateAgg: DateAgg): number => {
      const ctrSums = Object.values(dateAgg).reduce(
         (ctr: CTR, kpis: KPIAggregate) => {
            return {
               totalClicks: Number(ctr.totalClicks) + Number(kpis.clicks || 0),
               totalImpressions:
                  Number(ctr.totalImpressions) + Number(kpis.impressions || 0),
            };
         },
         {
            totalClicks: 0,
            totalImpressions: 0,
         },
      );
      return (ctrSums.totalClicks / ctrSums.totalImpressions) * 100;
   },
   frequency: (dateAgg: DateAgg): number => {
      const frequencySums = Object.values(dateAgg).reduce(
         (frequency: FREQUENCY, kpis: KPIAggregate) => {
            return {
               totalImpressions:
                  Number(frequency.totalImpressions) +
                  Number(kpis.impressions || 0),
               totalReach:
                  Number(frequency.totalReach) + Number(kpis.reach || 0),
            };
         },
         {
            totalImpressions: 0,
            totalReach: 0,
         },
      );
      return frequencySums.totalImpressions / frequencySums.totalReach;
   },
   purchase_rate: (dateAgg: DateAgg): number => {
      const purchaseRateFinal = Object.values(dateAgg).reduce(
         (purchaseRate: PurchaseRate, kpis: KPIAggregate) => {
            return {
               totalClicks:
                  Number(purchaseRate.totalClicks) + Number(kpis.clicks || 0),
               totalPurchase:
                  Number(purchaseRate.totalPurchase) +
                  Number(kpis.purchase || 0),
            };
         },
         {
            totalPurchase: 0,
            totalClicks: 0,
         },
      );
      return (
         (purchaseRateFinal.totalPurchase / purchaseRateFinal.totalClicks) * 100
      );
   },
   leads_conversion_rate: (dateAgg: DateAgg): number => {
      const leadsConversionRateFinal = Object.values(dateAgg).reduce(
         (leadsConversionRate: LeadConversionRate, kpis: KPIAggregate) => {
            return {
               totalClicks:
                  Number(leadsConversionRate.totalClicks) +
                  Number(kpis.clicks || 0),
               totalLeads:
                  Number(leadsConversionRate.totalLeads) +
                  Number(kpis.leads || 0),
            };
         },
         {
            totalClicks: 0,
            totalLeads: 0,
         },
      );
      return (
         (leadsConversionRateFinal.totalLeads /
            leadsConversionRateFinal.totalClicks) *
         100
      );
   },
};

export const KPI_CALCULATION_DAYWISE: Record<KPIKeys, KPIFormula> = {
   roas: (day, daywiseAgg) =>
      calculateKPI(
         daywiseAgg,
         { numerator: 'roas', denominator: 'spend' },
         day,
      ),
   cpc: (day, daywiseAgg) =>
      calculateKPI(
         daywiseAgg,
         { numerator: 'spend', denominator: 'clicks' },
         day,
      ),
   cpp: (day, daywiseAgg) =>
      calculateKPI(
         daywiseAgg,
         { numerator: 'spend', denominator: 'purchase' },
         day,
      ),
   cpm: (day, daywiseAgg) =>
      calculateKPI(
         daywiseAgg,
         { numerator: 'spend', denominator: 'impressions' },
         day,
      ) * 1000,
   cpl: (day, daywiseAgg) =>
      calculateKPI(
         daywiseAgg,
         { numerator: 'spend', denominator: 'leads' },
         day,
      ),
   ctr: (day, daywiseAgg) =>
      calculateKPI(
         daywiseAgg,
         { numerator: 'clicks', denominator: 'impressions' },
         day,
      ) * 100,
   frequency: (day, daywiseAgg) =>
      calculateKPI(
         daywiseAgg,
         { numerator: 'impressions', denominator: 'reach' },
         day,
      ) * 100,
};

const calculateKPI: KPIFormulaExtended = (daywiseAgg, keys, day) => {
   const numerator = daywiseAgg.get(keys.numerator)?.[day] || 0;
   const denominator = daywiseAgg.get(keys.denominator)?.[day] || 0;
   return denominator > 0 ? numerator / denominator : NaN;
};

export const TargetingInsightsKPIS = {
   age: {
      OUTCOME_SALES: ['spend', 'purchase', 'roas', 'ctr'],
      LINK_CLICKS: ['spend', 'clicks', 'cpc', 'ctr'],
      OUTCOME_LEADS: ['leads', 'cpl', 'ctr'],
   },
   gender: {
      OUTCOME_SALES: ['spend', 'purchase', 'roas', 'ctr'],
      LINK_CLICKS: ['spend', 'clicks', 'cpc', 'ctr'],
      OUTCOME_LEADS: ['leads', 'cpl', 'ctr'],
   },
   placement: {
      OUTCOME_SALES: ['spend', 'purchase', 'roas', 'ctr'],
      LINK_CLICKS: ['spend', 'clicks', 'cpc', 'ctr'],
      OUTCOME_LEADS: ['leads', 'cpl', 'ctr'],
   },
   region: {
      OUTCOME_SALES: ['spend', 'cpc', 'ctr', 'impressions'],
      LINK_CLICKS: ['spend', 'cpc', 'ctr', 'impressions'],
      OUTCOME_LEADS: ['spend', 'cpc', 'ctr', 'impressions'],
   },
   country: {
      OUTCOME_SALES: ['spend', 'purchase', 'roas', 'ctr'],
      LINK_CLICKS: ['spend', 'clicks', 'cpc', 'ctr'],
      OUTCOME_LEADS: ['leads', 'cpl', 'ctr'],
   },
};
