import express from 'express';
import asyncHandler from '../../../midddleware/async-handler';

import { logger } from '../../../config/logger';
import { TwitterAuthService } from '../../../services/TwitterAuthService';
import { <PERSON><PERSON>uthController } from '../../../controllers/TwitterAuthController';

const router = express.Router();

const twitterAuthService = new TwitterAuthService(logger);
const twitterAuthController = new TwitterAuthController(
   twitterAuthService,
   logger,
);

router
   .route('/request-token')
   .get(
      asyncHandler(
         twitterAuthController.handleRequestToken.bind(twitterAuthController),
      ),
   );

router
   .route('/access-token')
   .post(
      asyncHandler(
         twitterAuthController.handleAccessToken.bind(twitterAuthController),
      ),
   );

export { router as twitterAuthRouter };
