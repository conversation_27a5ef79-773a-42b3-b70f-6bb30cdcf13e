import { EntityManager } from 'typeorm';

interface ConnectionDetails {
   actual_account_name: string;
   image_url: string;
   is_active: boolean;
   store: string;
   channel_id: string;
   meta_data: Record<string, string | number>;
}

interface DbResult {
   result: ConnectionDetails[];
}

export const getConnectionDetails = async (
   entityManager: EntityManager,
   clientId: string,
   channelName: string,
): Promise<ConnectionDetails | null> => {
   try {
      // Construct the query
      const query = `
      SELECT dscasd.fn_social_analytics_get($1, $2, $3) AS result
    `;
      const parameters = [clientId, channelName, 'private'];

      const response = (await entityManager.query(
         query,
         parameters,
      ));

      if (!response || response.length === 0) {
         return null;
      }

      const result = response[0].result || [];
      const firstResult = result.length > 0 ? result[0] : null;

      if (firstResult) {
         const details: ConnectionDetails = {
            actual_account_name: firstResult.actual_account_name,
            image_url: firstResult.image_url,
            is_active: firstResult.is_active,
            store: firstResult.store,
            channel_id: firstResult.channel_id,
            meta_data: firstResult.meta_data,
         };
         return details;
      }

      return null;
   } catch (err) {
      console.error('Error in getConnectionDetails:', err);
      throw new Error('Internal Server Error');
   }
};
