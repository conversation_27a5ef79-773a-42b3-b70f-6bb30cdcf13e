import { getFrontendRedirectUrl } from '../utils/common';
import { getAuthUrl, getAccessToken } from '../services/GscService';
import { Request, Response } from 'express';
import { Logger } from 'winston';
import { Config } from '../config';

export class GscController {
   constructor(private logger: Logger) {}

   async getGoogleAuthUrl(req: Request, res: Response) {
      res.json({ url: getAuthUrl() });
   }

   async fetchRefreshToken(req: Request, res: Response) {
      const { code } = req.query;
      if (!code)
         return res.status(400).json({ error: 'Authorization code required' });

      const {access, refresh} = await getAccessToken(code as string);
      const redirectUrl = getFrontendRedirectUrl(
         Config.FRONTEND_DOMAIN?.split(',')[0] || '',
         'integrations',
      );

      res.redirect(`${redirectUrl}?gsc=true&t=${access}&r=${refresh}`);
   }
}
