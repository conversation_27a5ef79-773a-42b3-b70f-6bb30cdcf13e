// DEFAULT RESPONSE
export interface DefaultResponse {
   response: {
      status: 'Success' | 'Failure';
      message: string;
   };
}

/** PAYLOADS **/
export type FetchUserPayload = {
   email_address: string;
};

export interface CreateUserPayload {
   email_address: string;
   register_progress: string;
   organization_type: 'individual-business' | 'marketing-agency';
   agency_name: string | null;
   agency_url: string | null;
   company_country: string;
   company_business_type: string;
   company_platform: string;
   company_url: string;
   company_traffic: string;
   company_annual_revenue: string;
   company_currency: string;
   company_name: string;
   last_update_date: string;
   role_in_organization: string;
}

export interface UpdateRegisterProgressPayload {
   client_id: string;
   register_progress: string;
   email_address: string;
   action?: string;
}

export interface FetchSnippetPayload {
   [key: string]: string;
   client_id: string;
}

export interface SendSnippetPayload {
   client_id: string;
   email_address_sender: string;
   email_address_receiver: string;
}

export interface GetUserSocialDetailsPayload {
   [key: string]: string;
   client_id: string;
}

export interface GetUserCompanyDetailsPayload {
   [key: string]: string;
   email_address: string;
}

export interface TestConnectionPayload {
   [key: string]: string;
   email_address: string;
   client_id: string;
}

/** DETAILS **/
export interface CountryCurrencyOptions {
   country_name: string;
   currency: string | null;
}

export interface UserMasterList {}

export interface UserDetails {
   email_address: string;
   password: string;
   full_name: string;
   user_active: 'Y' | 'N';
   register_progress: string;
   cb_product_updates: 'Y' | 'N';
   country: string | null;
   language: string | null;
}

export interface ClientRoleDetails {
   email_address: string;
   company_url: string;
   user_role: string;
   client_id: string;
}

export interface ClientAccountDetails {
   [key: string]: string | number | null;
}

export interface UserSocialDetails {
   [key: string]: string | null;
}

/** RESPONSES **/
export interface FetchCountryCurrencyOptionsResponse {
   [key: string]: CountryCurrencyOptions;
}

export interface FetchUserMasterListResponse {
   [key: string]: UserMasterList;
}

export interface CreateUserResponse {
   response: {
      status: 'Success' | 'Failure';
      message: string;
      details?: ClientAccountDetails[] | null;
   };
}

export interface UpdateRegisterProgressResponse {
   response: {
      status: 'Success' | 'Failure';
      message: string;
      details?: {
         [key: string]: string;
      };
      action?: string;
   };
}

export interface ClientDetailsResponse {
   accountDetails: ClientAccountDetails[];
}
