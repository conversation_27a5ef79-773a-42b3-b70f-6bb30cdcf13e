import express from 'express';
import asyncHandler from '../../midddleware/async-handler';

import { logger } from '../../config/logger';
import { FivetranController } from '../../controllers/FivetranController';
import { FivetranService } from '../../services/FivetranService';

const router = express.Router();
const fivetranService = new FivetranService(logger);
const fivetranController = new FivetranController(fivetranService, logger);

router
   .route('/source')
   .post(
      asyncHandler(
         fivetranController.handleSourceCreation.bind(fivetranController),
      ),
   );
router
   .route('/connect-card')
   .post(
      asyncHandler(
         fivetranController.handleCreateConnectCard.bind(fivetranController),
      ),
   );

router.get(
   '/source/all',
   asyncHandler(
      fivetranController.handleGetAllConnectors.bind(fivetranController),
   ),
);

router
   .route('/remove')
   .post(
      asyncHandler(
         fivetranController.handleDeleteConnector.bind(fivetranController),
      ),
   );

export { router as fivetranRouter };
