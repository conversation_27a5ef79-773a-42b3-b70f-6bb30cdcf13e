import express from 'express';
import asyncHandler from '../../../midddleware/async-handler';

import { logger } from '../../../config/logger';
import { LinkedinAuthController } from '../../../controllers/LinkedinAuthController';
import { LinkedinAuthService } from '../../../services/LinkedinAuthService';

const router = express.Router();

const linkedinAuthService = new LinkedinAuthService(logger);
const linkedinAuthController = new LinkedinAuthController(
   linkedinAuthService,
   logger,
);
router
   .route('/authorize')
   .get(
      asyncHandler(
         linkedinAuthController.handleAuthorize.bind(linkedinAuthController),
      ),
   );

router
   .route('/redirect')
   .get(
      asyncHandler(
         linkedinAuthController.handleRedirect.bind(linkedinAuthController),
      ),
   );
export { router as linkedinAuthRouter };
