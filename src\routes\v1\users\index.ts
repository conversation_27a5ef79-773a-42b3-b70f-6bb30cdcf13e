import express from 'express';
import asyncHandler from '../../../midddleware/async-handler';

import { UsersService } from '../../../services/UsersService';
import { UsersController } from '../../../controllers/UsersController';
import { AppDataSource } from '../../../config/data-source';
import { logger } from '../../../config/logger';
import { EmailService } from '../../../services/EmailService';

const router = express.Router();

const entityManager = AppDataSource.manager;
const emailService = new EmailService();

const usersService = new UsersService(logger, entityManager, emailService);
const usersController = new UsersController(usersService, logger);

router
   .route('/profiles')
   .get(asyncHandler(usersController.getAllProfiles.bind(usersController)));

router
   .route('/all')
   .get(asyncHandler(usersController.getAllUsers.bind(usersController)));

router
   .route('/')
   .post(asyncHandler(usersController.createUser.bind(usersController)))
   .put(asyncHandler(usersController.updateUser.bind(usersController)))
   .delete(asyncHandler(usersController.deleteUser.bind(usersController)));

export { router as usersRouter };
