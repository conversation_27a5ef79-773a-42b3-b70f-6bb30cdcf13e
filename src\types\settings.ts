import { KPIMeta } from './kpi';

export interface Competitors {
   handle: string;
   metadata?: object;
}

export interface CompetitorBaseModel {
   client_id: string;
   channel: string;
   competitor_handles: Array<Competitors>;
}

export type FetchCompetitorsRequest = Omit<
   CompetitorBaseModel,
   'competitor_handles'
>;

export type FetchCompetitorsResponse = Pick<
   CompetitorBaseModel,
   'competitor_handles'
>;

export interface GetReportPayload {
   clientId: string;
   userId: string;
}
export interface DeleteReportPayload {
   clientId: string;
   reportId: string;
}
export interface pauseReportPayload {
   clientId: string;
   isSubscribed: boolean;
   reportId: string;
}
export interface UpdateReportPayload {
   clientId: string;
   user_id?: string;
   reportId: string;
   title: string;
   time: string;
   frequency: string;
   interval: string;
   start_date: string;
   end_date: string;
   date: string;
   emails: string;
   kpis: KPIMeta[];
   username: string;
}
export interface WeeklyAutoReportFromXiPayload {
   client_id: string;
   context_variables: Record<string, any>;
   message_history: any[];
   session_id: string;
   text: string;
   user_id: string;
   mode: 'cmo' | string;
}

export interface MetricReport {
   clientId: string;
   user_id?: string;
   title: string;
   time: string;
   frequency: string;
   interval: string;
   start_date: string;
   end_date: string;
   date: string;
   emails: string;
   kpis: KPIMeta[];
   username: string;
   is_auto_report?: boolean;
   is_subscribed?: boolean;
}

export interface AddCompetitorRequest extends CompetitorBaseModel {}

export interface SettingsBaseModel {
   client_id: string;
   language: string;
   timezone: string;
   industry: string;
   category: string;
}

export type IndustryBaseModel = Omit<
   SettingsBaseModel,
   'client_id' | 'language | timezone'
>;

export type IndustryOptionsBaseModel = {
   [industry: string]: string[];
};

export type FetchLanguageRequest = Pick<SettingsBaseModel, 'client_id'>;

export type FetchLanguageResponse = Omit<
   SettingsBaseModel,
   'client_id' | 'timezone | industry | category'
>;

export type FetchTimezoneRequest = Pick<SettingsBaseModel, 'client_id'>;

export type FetchTimezoneResponse = Omit<
   SettingsBaseModel,
   'client_id' | 'language | industry | category'
>;

export type FetchIndustryRequest = Pick<SettingsBaseModel, 'client_id'>;

export type FetchIndustryResponse = {
   [fn: string]: IndustryBaseModel;
};

export type FetchIndustryOptionsResponse = {
   [fn: string]: IndustryOptionsBaseModel;
};

export interface SettingsRequest extends SettingsBaseModel {}

export interface FeatureUsage {
   client_id: string;
   user_id: string;
   feature_name: string;
   feature_type: string;
   is_enabled: boolean;
   no_of_calls: number;
   free_limit_expired: boolean;
   last_call_made_at: string;
   mode: string;
}

/** PAYLOAD **/
export interface GeneralSettingsPayload {
   [key: string]: string;
   client_id: string;
   email_address: string;
}

export interface LanguageTimezonePayload {
   [key: string]: string;
   client_id: string;
   language: string;
   timezone: string;
}

export interface AccountDetailsPayload {
   client_id: string;
   email_address: string;
   annual_revenue: string;
   industry: string;
   currency: string;
}

export interface ProfileImagePayload {
   client_id: string;
   email_address: string;
   profile_image: string;
}

export interface FetchFeatureUsagePayload {
   client_id: string;
   user_id: string;
   feature_name: string;
   feature_type: string;
}

export interface TrackFeatureUsagePayload {
   client_id: string;
   user_id: string;
   feature_name: string;
   feature_type: string;
   mode: string;
}

/** RESPONSE **/
export interface GeneralSettingsResponse {
   client_id: string;
   language: string;
   timezone: string;
   industry: string;
   currency: string;
   annual_revenue: string;
   profile_image: string | null;
   timezone_name: string;
}

export interface LanguageTimezoneResponse {
   client_id: string;
   language: string;
   timezone: string;
   timezone_name: string;
}

export interface AccountDetailsResponse {
   client_id: string;
   company_annual_revenue: string;
   company_business_type: string;
   company_currency: string;
}

export interface LoginDetailsResponse {
   email_address: string;
   profile_image: string | null;
}
