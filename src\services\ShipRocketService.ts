import axios from 'axios';
import crypto from 'crypto';
import { Config } from '../config';
export class ShipRocketService {
   private baseUrl = 'https://apiv2.shiprocket.in/v1/external';

   private encryptPass(password: string) {
      const ALGORITHM = 'aes-256-gcm';
      const ENCRYPTION_KEY = crypto
         .createHash('sha256')
         .update(String(Config.ENCRYPTION_KEY))
         .digest() as unknown as crypto.CipherKey;
      const IV_LENGTH = 16;

      const iv = new Uint8Array(crypto.randomBytes(IV_LENGTH));
      const cipher = crypto.createCipheriv(ALGORITHM, ENCRYPTION_KEY, iv);
      let encrypted = cipher.update(password, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      const authTag = cipher.getAuthTag().toString('hex');
      const encrypted_pass =
         Buffer.from(iv).toString('hex') + ':' + authTag + ':' + encrypted;

      return encrypted_pass;
   }
   public async validateCredentials(email: string, password: string) {
      try {
         const response = await axios.post(`${this.baseUrl}/auth/login`, {
            email,
            password,
         });

         if (response.data?.token) {
            return {
               valid: true,
            };
         }
         return { valid: false, message: 'Invalid credentials' };
      } catch (error: any) {
         const message =
            error.response?.data?.message || 'Shiprocket validation failed';
         throw new Error(message);
      }
   }

   public encryptCreds(password: string) {
      const encryptedPass = this.encryptPass(password);

      return encryptedPass;
   }
}
