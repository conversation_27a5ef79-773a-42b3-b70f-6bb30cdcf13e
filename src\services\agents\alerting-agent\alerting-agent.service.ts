import { EmailService } from '../../EmailService';
import { AlertingAgentModel } from '@/models/agents/alerting-agent/alerting-agent.model';
import { returnAlertEmail } from '../../../constants/email-templates';
import * as types from '../../../types/agents/alerting-agent/alerting-agent.types';
import { EntityManager } from 'typeorm';
import { DatabaseError } from 'pg';
import {
   compareValues,
   createDescriptionText,
   createDescriptionTextWhatsApp,
   createQuestionText,
   getTableName,
   mapChannel,
   parseRecipients,
} from '../../../utils/alerting-agent-helper';
import { WhatsappService } from '../../WhatsappService';
import { Config } from '../../../config/index';

export class AlertingAgentService {
   constructor(
      private alertingAgentModel: AlertingAgentModel,
      private emailService: EmailService,
      private whatsappService: WhatsappService,
      private entityManager: EntityManager,
   ) {}

   async fetchAllSessionsByUserID(
      payload: types.FetchAllSessionsByUserIDPayload,
   ): Promise<types.AlertingAgentChat[] | null> {
      try {
         if (!payload.client_id || !payload.user_id) {
            return null;
         }

         return await this.alertingAgentModel.fetchAllSessionsByUserID(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async fetchHistoryBySessionID(
      payload: types.FetchHistoryBySessionIDPayload,
   ): Promise<types.AlertingAgentChat[] | null> {
      try {
         if (!payload.client_id || !payload.user_id || !payload.session_id) {
            return null;
         }

         return await this.alertingAgentModel.fetchHistoryBySessionID(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async addChatToSessionHistory(
      payload: types.AddChatToSessionHistoryPayload,
   ): Promise<void | null> {
      try {
         if (!payload.client_id || !payload.user_id || !payload.session_id) {
            return null;
         }

         return await this.alertingAgentModel.addChatToSessionHistory(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async fetchChatByChatId(
      payload: types.FetchChatByChatIdPayload,
   ): Promise<types.AlertingAgentChat | null> {
      try {
         if (!payload.client_id || !payload.user_id || !payload.session_id) {
            return null;
         }

         return await this.alertingAgentModel.fetchChatByChatId(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async fetchAlertsBySessionID(
      payload: types.FetchAlertsBySessionIDPayload,
   ): Promise<types.AlertingAgentAlert[] | null> {
      try {
         if (!payload.client_id || !payload.user_id || !payload.session_id) {
            return null;
         }

         return await this.alertingAgentModel.fetchAlertsBySessionID(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async fetchAllAlerts(
      payload: types.FetchAllAlertsPayload,
   ): Promise<types.AlertingAgentAlert[] | null> {
      try {
         if (!payload.client_id || !payload.user_id) {
            return null;
         }

         return await this.alertingAgentModel.fetchAllAlerts(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async fetchAlertByID(
      payload: types.FetchAlertByIdPayload,
   ): Promise<types.AlertingAgentAlert | null> {
      try {
         if (!payload.client_id || !payload.user_id) {
            return null;
         }

         return await this.alertingAgentModel.fetchAlertByID(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async createAlert(
      payload: types.CreateAlertPayload,
   ): Promise<types.AlertingAgentAlert | null> {
      try {
         if (!payload.client_id || !payload.user_id) {
            return null;
         }

         return await this.alertingAgentModel.createAlert(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async updateAlert(
      payload: types.UpdateAlertPayload,
   ): Promise<types.AlertingAgentAlert | null> {
      try {
         if (!payload.client_id || !payload.user_id) {
            return null;
         }

         return await this.alertingAgentModel.updateAlert(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async deleteAlert(payload: types.DeleteAlertPayload): Promise<void | null> {
      try {
         if (!payload.client_id || !payload.user_id) {
            return null;
         }

         await this.alertingAgentModel.deleteAlert(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async deleteMultipleAlerts(
      payload: types.DeleteMultipleAlertsPayload,
   ): Promise<void | null> {
      try {
         if (!payload.client_id || !payload.user_id) {
            return null;
         }

         await this.alertingAgentModel.deleteMultipleAlerts(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async pauseUnpauseAlert(
      payload: types.PauseUnpauseAlertPayload,
   ): Promise<void | null> {
      try {
         if (!payload.client_id || !payload.user_id) {
            return null;
         }

         await this.alertingAgentModel.pauseUnpauseAlert(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async updateEmailRecipients(
      payload: types.UpdateEmailRecipientsPayload,
   ): Promise<void | null> {
      try {
         if (!payload.client_id || !payload.user_id) {
            return null;
         }

         await this.alertingAgentModel.updateEmailRecipients(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   private async sendAlertEmail(
      recipient: string,
      alert_name: string,
      trend: string,
      value: string,
      value_type: string,
      comparison: string,
      comparison_type: string,
      channel: string,
      recipients: string,
      campaign_name: string,
      kpi: string,
      fixedComparison: string,
      user_id: string,
      alert_id: string,
      client_id: string,
   ) {
      try {
         const descriptionText = createDescriptionText(
            channel,
            trend,
            value,
            value_type,
            comparison,
            campaign_name,
            kpi,
         );
         const descriptionTextWa = createDescriptionTextWhatsApp(
            channel,
            trend,
            value,
            value_type,
            comparison,
            campaign_name,
            kpi,
         );

         const questionText = createQuestionText(
            channel,
            trend,
            value,
            value_type,
            comparison,
            campaign_name,
            kpi,
         );

         const emailInfo = {
            to: recipient,
            subject: 'Flable AI Alert',
            text: 'Flable AI Alert',
            html: returnAlertEmail(
               recipient,
               alert_name,
               trend,
               value,
               value_type,
               fixedComparison,
               comparison_type,
               channel,
               recipients,
               descriptionText,
               questionText,
               user_id,
               alert_id,
               client_id,
            ),
         };
         await this.emailService.sendEmail(emailInfo);

         const query = `
  SELECT meta_data
  FROM ${Config.DB_Postgres_SCHEMA}.social_analytics
  WHERE client_id = $1 AND channel_name = $2 AND is_active = true
`;
         const result = await this.entityManager.query(query, [
            client_id,
            'whatsapp',
         ]);

         if (result.length > 0) {
            const whatsappPayload = {
               apiKey: Config.AISENSY_API_KEY || '',
               campaignName: 'Flable Alert',
               destination: result[0]?.meta_data?.phone_number,
               userName: 'Flable AI',
               templateParams: [
                  descriptionTextWa,
                  channel,
                  comparison_type === 'time'
                     ? `${comparison || '.'}`
                     : `compared with ${comparison || '.'}`,
                  value_type === 'percentage'
                     ? `${trend} by ${value}%`
                     : `${trend} ${value}`,
                  new Date()
                     .toISOString()
                     .split('T')
                     .map((item, index) =>
                        index === 0 ? item : item.split('.')[0],
                     )
                     .join(' '),
                  `${Config.FRONTEND_DOMAIN?.split(',')[0]}/marco/analytics-agent?query=${questionText}`,
                  `${Config.FRONTEND_DOMAIN?.split(',')[0]}/dashboard`,
               ],
               source: 'new-landing-page form',
               media: {},
               buttons: [],
               carouselCards: [],
               location: {},
               attributes: {},
               paramsFallbackValue: {
                  FirstName: 'User',
               },
            };
            await this.whatsappService.sendWhatsapp(whatsappPayload);
         }
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(
            `Failed to send email to ${recipient}: ${error.message}`,
         );
      }
   }

   private async notifyRecipients(
      recipients: string[],
      alert: types.AlertingAgentAlert,
      fixedChannel: string,
      fixedComparison: string,
      user_id: string,
      alert_id: string,
      client_id: string,
   ): Promise<void> {
      try {
         await Promise.all(
            recipients.map((recipient) =>
               this.sendAlertEmail(
                  recipient,
                  alert.alert_name,
                  alert.trend.split('_').join(' '),
                  alert.value,
                  alert.value_type,
                  alert.comparison,
                  alert.comparison_type,
                  fixedChannel,
                  recipients.join(','),
                  alert.campaigns.map((c) => c.name).join(', ') || '',
                  alert.kpi,
                  fixedComparison,
                  user_id,
                  alert_id,
                  client_id,
               ),
            ),
         );
      } catch (error) {
         const errorMessage =
            error instanceof Error ? error.message : 'Unknown error';
         throw new Error(`Failed to notify recipients: ${errorMessage}`);
      }
   }

   async checkAlertCriteria(payload: { alerts: types.AlertingAgentAlert[] }) {
      try {
         for (const alert of payload.alerts) {
            const {
               client_id,
               user_id,
               alert_id,
               campaigns,
               kpi,
               value,
               value_type,
               trend,
               comparison_type,
               comparison,
               channel,
               recipients,
               alert_timeframe,
            } = alert;

            const isCampaignLevel = campaigns?.length > 0;
            const dateColumn = channel === 'facebookads' ? 'date_time' : 'date';
            const fixedRecipients = parseRecipients(recipients);
            const fixedChannel = mapChannel(channel);
            const table = getTableName(channel, isCampaignLevel);
            const fixedComparison = comparison
               .split('_')
               .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
               .join(' ');

            const keys = isCampaignLevel
               ? [client_id, campaigns.map((c) => c.id).join(','), kpi]
               : [client_id, channel, kpi];

            const todaysValue =
               await this.alertingAgentModel.fetchTodaysKPIValue(
                  table,
                  keys,
                  isCampaignLevel,
                  dateColumn,
               );

            if (!todaysValue || Number.isNaN(todaysValue)) {
               return;
            }

            if (value_type === 'number') {
               switch (trend) {
                  case 'More_than':
                     if (todaysValue > Number(value)) {
                        await this.notifyRecipients(
                           fixedRecipients,
                           alert,
                           fixedChannel,
                           fixedComparison,
                           user_id,
                           String(alert_id),
                           String(client_id),
                        );

                        return;
                     }
                     break;
                  case 'Less_than':
                     if (todaysValue < Number(value)) {
                        await this.notifyRecipients(
                           fixedRecipients,
                           alert,
                           fixedChannel,
                           fixedComparison,
                           user_id,
                           String(alert_id),
                           String(client_id),
                        );

                        return;
                     }
                     break;
               }

               return;
            }

            let baseValue: number | null;

            if (comparison_type === 'kpi') {
               const keys = isCampaignLevel
                  ? [
                       client_id,
                       campaigns.map((c) => c.id).join(','),
                       comparison,
                    ]
                  : [client_id, channel, comparison];
               baseValue = await this.alertingAgentModel.fetchTodaysKPIValue(
                  table,
                  keys,
                  isCampaignLevel,
                  dateColumn,
               );
            } else {
               baseValue = await this.alertingAgentModel.fetchAverageKPIValue(
                  table,
                  keys,
                  alert_timeframe,
                  isCampaignLevel,
                  dateColumn,
               );
            }

            if (!baseValue || Number.isNaN(baseValue)) {
               return;
            }

            const threshold = parseFloat(value);

            const shouldAlert = compareValues(
               todaysValue,
               baseValue,
               trend,
               threshold,
            );

            if (shouldAlert) {
               await this.notifyRecipients(
                  fixedRecipients,
                  alert,
                  fixedChannel,
                  fixedComparison,
                  user_id,
                  String(alert_id),
                  String(client_id),
               );
            }
         }
      } catch (err) {
         const error = err as Error;
         throw new Error(error.message);
      }
   }
}
