import { Request, Response } from 'express';
import { MetaAdsService } from '../services/MetamanagerService';
import { Logger } from 'winston';
import * as types from '../types/metamanager';
import { Console } from 'console';

export class MetaAdsController {
   constructor(
      private metaAdsService: MetaAdsService,
      private logger: Logger,
   ) {}

   async createCampaign(req: Request, res: Response) {
      const campaignData = req.body as types.CreateCampaignRequest;

      try {
         const campaign =
            await this.metaAdsService.createCampaign(campaignData);
         res.status(201).json(campaign);
      } catch (err) {
         const error = err as Error;
         this.logger.error('Error creating campaign', error.message);
         res.status(500).json({
            error:
               error.message ||
               'An unexpected error occurred while creating the campaign',
         });
      }
   }

   async fetchCampaigns(req: Request, res: Response) {
      try {
         const campaigns = await this.metaAdsService.getCampaigns();
         res.status(200).json(campaigns);
      } catch (error: any) {
         this.logger.error('Error fetching campaigns', error);
         res.status(500).json({
            error:
               error.response?.data ||
               error.message ||
               'An unexpected error occurred while fetching the camapign',
         });
      }
   }

   async createAdSet(req: Request, res: Response) {
      const adSetData = req.body as types.CreateAdSetRequest;

      try {
         const adSet = await this.metaAdsService.createAdSet(adSetData);
         res.status(201).json(adSet);
      } catch (err) {
         const error = err as Error;
         this.logger.error('Error creating ad-set', error.message);
         res.status(500).json({
            error:
               error.message ||
               'An unexpected error occurred while creating the ad-set',
         });
      }
   }

   async fetchAdSets(req: Request, res: Response) {
      const { campaign_id } = req.query as { campaign_id: string };

      if (!campaign_id) {
         return res.status(400).json({ error: 'campaign_id is required' });
      }

      try {
         const adSets = await this.metaAdsService.getAdSets(campaign_id);
         res.status(200).json(adSets);
      } catch (error: any) {
         this.logger.error('Error fetching ad sets', error);
         res.status(500).json({
            error:
               error.response?.data || error.message || 'Something went wrong',
         });
      }
   }

   async createAdCreative(req: Request, res: Response) {
      const creativeData = req.body as types.CreateAdCreativeRequest;

      try {
         const adCreative =
            await this.metaAdsService.createAdCreative(creativeData);
         res.status(201).json(adCreative);
      } catch (err) {
         const error = err as Error;
         this.logger.error('Error creating ad-creative', error.message);
         res.status(500).json({
            error:
               error.message ||
               'An unexpected error occurred while creating the ad-creative',
         });
      }
   }

   async createAd(req: Request, res: Response) {
      const adData = req.body as types.CreateAdRequest;

      try {
         const ad = await this.metaAdsService.createAd(adData);
         res.status(201).json(ad);
      } catch (error: any) {
         this.logger.error('Error creating ad', error);
         res.status(500).json({
            error:
               error.response?.data || error.message || 'Something went wrong',
         });
      }
   }

   async fetchAdStatus(req: Request, res: Response) {
      const { ad_id } = req.params;

      if (!ad_id) {
         return res.status(400).json({ error: 'ad_id is required' });
      }

      try {
         const adStatus = await this.metaAdsService.getAdStatus(ad_id);

         if (!adStatus) {
            return res.status(404).json({ error: 'Ad not found' });
         }

         res.status(200).json(adStatus);
      } catch (error) {
         this.logger.error('Error fetching ad status', error);
         res.status(500).json({ error: 'Internal server error' });
      }
   }

   async deleteAd(req: Request, res: Response) {
      const { ad_id } = req.body as { ad_id: string };

      if (!ad_id) {
         return res.status(400).json({ error: 'ad_id is required' });
      }

      try {
         const deletedAd = await this.metaAdsService.deleteAd(ad_id);
         res.status(200).json(deletedAd);
      } catch (error: any) {
         this.logger.error('Error deleting ad', error);
         res.status(500).json({
            error:
               error.response?.data || error.message || 'Something went wrong',
         });
      }
   }

   async UpdatetChatHistory(req: Request, res: Response) {
      try {
         const result = await this.metaAdsService.updateChatHistory(req.body);
         this.logger.info('Chat history upserted successfully');
         res.status(200).send(result.response);
      } catch (error: any) {
         this.logger.error('Error in upserting chat history:', error);
         res.status(500).json({
            error: error.message || 'Internal Server Error',
         });
      }
   }
   async summarization(req: Request, res: Response) {
      try {
         const summary = await this.metaAdsService.summarizeCampaign(req.body);
         this.logger.info('Campaign summarized successfully');
         res.status(200).json({ summary });
      } catch (error: any) {
         this.logger.error('Error summarizing campaign:', error);
         res.status(500).json({
            error: error.message || 'Internal Server Error',
         });
      }
   }

   async getAllChatHistory(req: Request, res: Response) {
      try {
         const result = await this.metaAdsService.fetchAllChatHistory();
         this.logger.info('Fetched all chat history successfully');
         res.status(200).send(result.response);
      } catch (error: any) {
         this.logger.error('Error in fetching chat history:', error);
         res.status(500).json({
            error: error.message || 'Internal Server Error',
         });
      }
   }

   async saveAutoAgentHistory(req: Request, res: Response) {
      try {
         const result = await this.metaAdsService.saveAutoAgentHistory(
            req.body,
         );
         this.logger.info('Chat history inserted successfully');
         res.status(200).send(result.response);
      } catch (error: any) {
         this.logger.error('Error in upserting chat history:', error);
         res.status(500).json({
            error: error.message || 'Internal Server Error',
         });
      }
   }
   async fetchAllAutoAgentHistory(req: Request, res: Response) {
      try {
         const { client_id, user_id } = req.query;
         if (!client_id || !user_id) {
            return res
               .status(400)
               .json({ error: 'client_id and user_id are required' });
         }
         const result = await this.metaAdsService.fetchAllAutoAgentHistory(
            String(client_id),
            String(user_id),
         );
         this.logger.info('Fetched all auto agent chat history successfully');
         res.status(200).json(result);
      } catch (error: any) {
         this.logger.error(
            'Error fetching all auto agent chat history:',
            error,
         );
         res.status(500).json({
            error: error.message || 'Internal Server Error',
         });
      }
   }
   async fetchAutoAgentHistoryBySession(req: Request, res: Response) {
      try {
         const { client_id, user_id, session_id } = req.query;
         if (!client_id || !user_id || !session_id) {
            return res.status(400).json({
               error: 'client_id, user_id, and session_id are required',
            });
         }
         const result =
            await this.metaAdsService.fetchAutoAgentHistoryBySession(
               String(client_id),
               String(user_id),
               String(session_id),
            );
         this.logger.info(
            'Fetched auto agent chat history for session successfully',
         );
         res.status(200).json(result);
      } catch (error: any) {
         this.logger.error(
            'Error fetching auto agent chat history by session:',
            error,
         );
         res.status(500).json({
            error: error.message || 'Internal Server Error',
         });
      }
   }

   /*async uploadChatGptImageToAzure(req: Request, res: Response) {
      const { image_url } = req.body;
      if (!image_url) {
         return res.status(400).json({ error: 'imageUrl is required' });
      }
      try {
         const uri =
            await this.metaAdsService.uploadChatGptImageToAzure(image_url);
         this.logger.info('ChatGPT image uploaded to Azure Blob successfully');
         res.status(200).json({ uri });
      } catch (error: any) {
         this.logger.error(
            'Error uploading ChatGPT image to Azure Blob:',
            error,
         );
         res.status(500).json({
            error: error.message || 'Internal Server Error',
         });
      }
   }*/
   async generateCreativeImage(req: Request, res: Response) {
      const { caption, description } = req.query;

      if (!caption || !description) {
         return res.status(400).json({
            error: 'Caption and description are required for image creation',
         });
      }

      try {
         const creativePayload = {
            caption: String(caption),
            description: String(description),
         };

         const { imageUrl, promptUsed } =
            await this.metaAdsService.generateCreativeImage(creativePayload);

         this.logger.info('Creative image generated successfully!');

         return res.status(200).json({
            imageUrl,
            promptUsed,
         });
      } catch (error: any) {
         this.logger.error('Error generating creative image:', error);

         return res.status(500).json({
            error: error.message || 'Internal Server Error',
         });
      }
   }
}
