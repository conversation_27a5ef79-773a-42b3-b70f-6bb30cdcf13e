import { DataSource } from 'typeorm';
import { User } from '../entity/User';
import { Config } from '.';

const {
   DB_Postgres_HOST,
   DB_Postgres_USER,
   DB_Postgres_PASSWORD,
   DB_Postgres_DATABASE,
   DB_Postgres_PORT,
} = Config;

export const AppDataSource = new DataSource({
   type: 'postgres',
   host: DB_Postgres_HOST,
   port: Number(DB_Postgres_PORT),
   username: DB_Postgres_USER,
   password: DB_Postgres_PASSWORD,
   database: DB_Postgres_DATABASE,
   synchronize: false,
   logging: false,
   entities: [User],
   migrations: [],
   subscribers: [],
});
