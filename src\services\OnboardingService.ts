import axios from 'axios';
import { Logger } from 'winston';
import { Config } from '../config';
import { EntityManager } from 'typeorm';
import {
   FetchUserPayload,
   SendSnippetPayload,
   UserDetails,
   CreateUserPayload,
   FetchUserMasterListResponse,
   FetchSnippetPayload,
   ClientAccountDetails,
   ClientRoleDetails,
   UserSocialDetails,
   CreateUserResponse,
   ClientDetailsResponse,
   UpdateRegisterProgressPayload,
   UpdateRegisterProgressResponse,
   GetUserSocialDetailsPayload,
   GetUserCompanyDetailsPayload,
   TestConnectionPayload,
} from '../types';
import { AuthService } from './AuthService';
import { EmailService } from './EmailService';
import { JS_SCRIPT } from '../constants';
import { returnSnippetEmailHTML } from '../constants/email-templates';

export class OnboardingService {
   constructor(
      private logger: Logger,
      private entityManager: EntityManager,
      private emailService: EmailService,
      private authService: AuthService,
   ) {}

   private async fetchAccountDetails(
      email_address: string,
   ): Promise<ClientDetailsResponse | null> {
      try {
         const clientIdQuery = `SELECT * FROM ${Config.DB_Postgres_CONFIG_SCHEMA}.user_role WHERE email_address = $1`;
         const result: ClientRoleDetails[] = await this.entityManager.query(
            clientIdQuery,
            [email_address],
         );

         if (result.length === 0) return null;

         // This can later be changed to a function in db, but due to time constraints this is done here
         const accountDetails = [];

         for (let i = 0; i < result.length; i++) {
            const clientId: string = result[i].client_id;
            const clientDetailsQuery = `SELECT * FROM ${Config.DB_Postgres_CONFIG_SCHEMA}.user_account WHERE client_id = $1;`;
            const clientDetails: ClientAccountDetails[] =
               await this.entityManager.query(clientDetailsQuery, [clientId]);

            accountDetails.push(...clientDetails);
         }

         return {
            accountDetails: accountDetails,
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'fetchAccountDetails',
         });
         throw new Error(error.message);
      }
   }

   private async checkClientAlreadyExists(
      company_url: string,
   ): Promise<string> {
      try {
         const query = `SELECT * FROM ${Config.DB_Postgres_CONFIG_SCHEMA}.fn_onboarding_client_validate($1);`;
         const result = await this.entityManager.query(query, [company_url]);
         return result[0].company_url_verification_status[0].validation_message;
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'checkClientAlreadyExists',
         });
         throw new Error(error.message);
      }
   }

   async fetchUserMasterList() {
      try {
         const query = `SELECT * FROM ${Config.DB_Postgres_CONFIG_SCHEMA}.fn_user_master_list()`;
         const result: FetchUserMasterListResponse[] =
            await this.entityManager.query(query);

         return {
            response: {
               status: 'Success',
               message: 'User master list fetched successfully',
               list: result[0].user_master_list,
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'fetchUserMasterList',
         });
         throw new Error(error.message);
      }
   }

   async getUserDetails(payload: FetchUserPayload) {
      const { email_address } = payload;

      try {
         // Fetch user details from user_login table using email_address
         const userDetailsQuery = `SELECT * FROM ${Config.DB_Postgres_CONFIG_SCHEMA}.user_login WHERE email_address = $1`;
         const user: UserDetails[] = await this.entityManager.query(
            userDetailsQuery,
            [email_address],
         );

         // If there's no user, throw an error
         if (user.length === 0) {
            this.logger.error('User not found');
            throw new Error('User not found');
         }

         const userDetails = {
            full_name: user[0].full_name,
            email_address: user[0].email_address,
            user_active: user[0].user_active,
            cb_product_updates: user[0].cb_product_updates,
            country: user[0].country,
            language: user[0].language,
            register_progress: user[0].register_progress,
         };

         const accountDetails = await this.fetchAccountDetails(email_address);

         // Return the user if successful
         return {
            response: {
               status: 'Success',
               message: 'User details fetched successfully',
               details: {
                  userDetails,
                  accountDetails: accountDetails?.accountDetails || null,
               },
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'getUserDetails',
         });
         throw new Error(error.message);
      }
   }

   async createUser(payload: CreateUserPayload): Promise<CreateUserResponse> {
      try {
         const {
            email_address,
            register_progress,
            organization_type,
            company_annual_revenue,
            company_currency,
            company_url,
            company_traffic,
            company_platform,
            company_business_type,
            company_country,
            agency_url,
            agency_name,
            company_name,
            last_update_date,
            role_in_organization,
         } = payload;

         const clientExists = await this.checkClientAlreadyExists(company_url);

         if (clientExists === 'Client/Company Already Registered with Flable') {
            return {
               response: {
                  status: 'Failure',
                  message: clientExists,
               },
            };
         }

         const updatedAccountData = {
            email_address,
            register_progress,
            organization_type,
            company_annual_revenue,
            company_currency,
            company_url,
            company_traffic,
            company_platform,
            company_business_type,
            company_country,
            agency_url,
            agency_name,
            company_name,
            last_update_date,
            role_in_organization,
         };

         const query = `CALL ${Config.DB_Postgres_CONFIG_SCHEMA}.q_user_account_put($1::json)`;
         await this.entityManager.query(query, [
            JSON.stringify([updatedAccountData]),
         ]);

         const clientDetails = await this.fetchAccountDetails(email_address);

         return {
            response: {
               status: 'Success',
               message: 'User created successfully',
               details: clientDetails as ClientAccountDetails[] | null,
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'createUser',
         });
         throw new Error(error.message);
      }
   }

   async updateRegisterProgress(
      payload: UpdateRegisterProgressPayload,
   ): Promise<UpdateRegisterProgressResponse> {
      const { email_address, register_progress, client_id, action } = payload;

      try {
         const accountUpdateQuery = `UPDATE ${Config.DB_Postgres_CONFIG_SCHEMA}.user_account SET register_progress = $1 WHERE client_id = $2`;
         await this.entityManager.query(accountUpdateQuery, [
            register_progress,
            client_id,
         ]);

         let result;

         if (action !== 'company-added') {
            const loginUpdateQuery = `UPDATE ${Config.DB_Postgres_CONFIG_SCHEMA}.user_login SET register_progress = $1 WHERE email_address = $2`;
            await this.entityManager.query(loginUpdateQuery, [
               register_progress,
               email_address,
            ]);
         }

         if (action === 'register-complete') {
            result = await this.authService.setRefreshToken({
               email_address,
               client_id,
            });
         }

         if (result) {
            return {
               response: {
                  status: 'Success',
                  message: 'User details updated successfully',
                  details: result.response,
                  action,
               },
            };
         }

         return {
            response: {
               status: 'Success',
               message: 'User details updated successfully',
               action,
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'updateRegisterProgress',
         });
         throw new Error(error.message);
      }
   }

   async getSnippet(payload: FetchSnippetPayload) {
      const { client_id } = payload;

      return {
         response: {
            status: 'Success',
            message: 'Snippet fetched successfully',
            snippet: JS_SCRIPT(client_id),
         },
      };
   }

   async sendSnippetEmail(payload: SendSnippetPayload) {
      const { client_id, email_address_receiver } = payload;

      try {
         const emailInfo = {
            to: email_address_receiver,
            subject: 'Installation guidance',
            text: 'Ljc Script',
            html: returnSnippetEmailHTML(client_id),
         };
         await this.emailService.sendEmail(emailInfo);
         this.logger.info('Snippet sent successfully');

         return {
            response: {
               status: 'Success',
               message: 'Snippet sent successfully',
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'sendSnippetEmail',
         });
         throw new Error(error.message);
      }
   }

   async getUserSocialDetails(payload: GetUserSocialDetailsPayload) {
      const { client_id } = payload;

      try {
         const query = `
                        SELECT 
                           sa.client_id AS sa_client_id,
                           sa.channel_name,
                           sa.is_active,
                           cp.client_id AS cp_client_id,
                           cp.channel,
                           cp.competitor_handles
                        FROM 
                           ${Config.DB_Postgres_SCHEMA}.social_analytics sa
                        FULL OUTER JOIN 
                           ${Config.DB_Postgres_SCHEMA}.competitors cp
                        ON 
                           sa.client_id = cp.client_id
                        WHERE 
                           (sa.is_active = true OR sa.is_active IS NULL)
                           AND (sa.client_id = $1 OR cp.client_id = $2);
                     `;

         const result: UserSocialDetails[] = await this.entityManager.query(
            query,
            [client_id, client_id],
         );

         return {
            response: {
               status: 'Success',
               message: 'User social details fetched successfully',
               details: result,
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'getUserSocialDetails',
         });
         throw new Error(error.message);
      }
   }

   async getUserCompanyDetails(payload: GetUserCompanyDetailsPayload) {
      const { email_address } = payload;

      try {
         const query = `
                        SELECT 
                           ud.email_address,
                           ud.company_url,
                           ud.user_role,
                           ud.client_id,
                           ua.register_progress
                        FROM 
                           ${Config.DB_Postgres_CONFIG_SCHEMA}.user_role AS ud
                        JOIN 
                           ${Config.DB_Postgres_CONFIG_SCHEMA}.user_account AS ua
                        ON 
                           ud.client_id = ua.client_id
                        WHERE 
                           ud.email_address = $1;
                     `;

         const result: ClientRoleDetails[] = await this.entityManager.query(
            query,
            [email_address],
         );

         return {
            response: {
               status: 'Success',
               message: 'User company details fetched successfully',
               details: result,
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'getUserCompanyDetails',
         });
         throw new Error(error.message);
      }
   }

   async addNewCompany(payload: GetUserCompanyDetailsPayload) {
      const { email_address } = payload;

      try {
         const query = `UPDATE ${Config.DB_Postgres_CONFIG_SCHEMA}.user_login SET register_progress = 'Step 2' WHERE email_address = $1;`;
         await this.entityManager.query(query, [email_address]);

         return {
            response: {
               status: 'Success',
               message: 'Register progress updated successfully',
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'addNewCompany',
         });
         throw new Error(error.message);
      }
   }

   async testConnection(payload: TestConnectionPayload) {
      const { email_address, client_id } = payload;

      const clientDetails = await this.fetchAccountDetails(email_address);

      if (!clientDetails) {
         throw new Error('Client not found');
      }

      const form = {
         url: clientDetails.accountDetails[0].company_url as string,
         client_id: clientDetails.accountDetails[0].client_id as string,
      };

      try {
         const response = await axios.post(
            Config.TESTCONNECTION_VALIDATE_URL as string,
            form,
         );

         if (response.data.status === 'Error') {
            throw new Error(response.data.message);
         }

         const query = `INSERT INTO ${Config.DB_Postgres_SCHEMA}.social_analytics(client_id, channel_name, channel_integration_category, is_active) VALUES($1, 'flable_pixel', 'private', true);`;
         await this.entityManager.query(query, [client_id]);

         return {
            response: {
               status: 'Success',
               message: 'Test connection successful',
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'testConnection',
         });
         throw new Error(error.message);
      }
   }
}
