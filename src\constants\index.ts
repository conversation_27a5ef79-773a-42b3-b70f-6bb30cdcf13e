import { Config } from '../config';
import { DaysIn, GPTData } from '@/types/kpi';

export const UserRoles = {
   CUSTOMER: 'customer',
   ADMIN: 'admin',
   MANAGER: 'manager',
} as const;

export const SaltRounds = 10;
export const KPICategory: {
   [key: string]: string;
} = {
   facebookads: 'Meta Ads',
   store: 'Shopify Store',
   web: 'Web Analytics',
   pinned: 'Pinned',
};
export const GPT = {
   KPISummary: (
      data: GPTData[],
      kpi: string,
      category: string,
      currency: string,
   ) =>
      `The below given data is total data of ${kpi} from ${KPICategory[category] || category} -> ${JSON.stringify(data)})}. Currency is in ${currency}.
	\n\n Now give me most impactful insight on trend or any fluctuations or anomaly in 2 lines (divided by next line) point wise "`,
   GPTVersion: 'gpt-3.5-turbo',
};

export const DEFAULT_KPI_AGG = {
   web: {},
};
export const DAYS_IN: DaysIn = {
   day: 1,
   week: 7,
   month: 30,
   quarter: 120,
};

export const AVG_KPI_BY_NON_ZERO = [
   'avg_session_duration',
   'average_order_value',
   'bounce_rate',
   'avg_pages_per_session',
   'conversion_rate',
   'google_conversion_rate',
   'cost_per_session',
   'google_cost_per_conversion',
   'amazon_average_order_value',
];

export const MAX_TRACKED_COMPETITORS = 5;

export const ALL_KPIS = {
   roas: 'roas',
   ctr: 'ctr',
   cpp: 'cpp',
   cpm: 'cpm',
   cpc: 'cpc',
   cpl: 'cpl',
   spend: 'spend',
   purchase: 'purchase',
   leads: 'leads',
   frequency: 'frequency',
   clicks: 'clicks',
   impressions: 'impressions',
   reach: 'reach',
   video_watch_100_percent: 'video_watch_100_percent',
   video_watch_95_percent: 'video_watch_95_percent',
   video_watch_50_percent: 'video_watch_50_percent',
   video_watch_75_percent: 'video_watch_75_percent',
};

export const JS_SCRIPT = (_newClient_id: string): string => {
   return `<script>
   (function(f, l, a, b, t, i, c) {
     f[a] = f[a] || function() {
       (f[a].q = f[a].q || []).push(arguments)
     };
     i = l.createElement(b);
     i.async = 1;
     i.src ="${Config.SCRIPT_SRC}/api/config/"+t;
     c = l.getElementsByTagName(b)[0];
     c.parentNode.insertBefore(i, c);
   })(window, document, "flable", "script", "${_newClient_id}");
 </script>`;
};

// const GOOGLE_ADS_KPIS = 