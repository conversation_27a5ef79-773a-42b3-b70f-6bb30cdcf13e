import { Request, Response } from 'express';
import { AlertingAgentService } from '../services/AlertingAgentService';
import { Logger } from 'winston';
import {
   AddToHistoryPayload,
   CheckAlertPayload,
   CreateAlertPayload,
   UpdateAlertPayload,
} from '../types/alerting-agent';

export class AlertingAgentController {
   constructor(
      private alertingService: AlertingAgentService,
      private logger: Logger,
   ) {}

   async getAllAlerts(req: Request, res: Response) {
      const result = await this.alertingService.getAllAlerts(
         req.query.client_id as string,
      );
      this.logger.info('Fetched all alerts successfully');
      res.status(200).send(result.response);
   }

   async createAlert(req: Request, res: Response) {
      const result = await this.alertingService.createAlert(
         req.body as CreateAlertPayload,
      );
      this.logger.info('Created alert successfully');
      res.status(200).send(result.response);
   }

   async getAlertById(req: Request, res: Response) {
      const result = await this.alertingService.getAlertById(
         req.params.alert_id,
      );
      this.logger.info('Fetched alert successfully');
      res.status(200).send(result.response);
   }

   async updateAlert(req: Request, res: Response) {
      const result = await this.alertingService.updateAlert(
         req.body as UpdateAlertPayload,
      );
      this.logger.info('Updated alert successfully');
      res.status(200).send(result.response);
   }

   async deleteAlert(req: Request, res: Response) {
      const result = await this.alertingService.deleteAlert(
         req.params.alert_id,
      );
      this.logger.info('Deleted alert successfully');
      res.status(200).send(result.response);
   }

   async deleteMultipleAlerts(req: Request, res: Response) {
      const result = await this.alertingService.deleteMultipleAlerts(
         req.body as { alert_ids: number[] },
      );
      this.logger.info('Deleted multiple alerts successfully');
      res.status(200).send(result.response);
   }

   async updateEmailRecipients(req: Request, res: Response) {
      const result = await this.alertingService.updateEmailRecipients(req.body);
      this.logger.info('Updated email recipient successfully');
      res.status(200).send(result.response);
   }

   async pauseUnpauseAlert(req: Request, res: Response) {
      const result = await this.alertingService.pauseUnpauseAlert(
         req.params.alert_id,
      );
      this.logger.info('Paused/Unpaused alert successfully');
      res.status(200).send(result.response);
   }

   async checkAlerts(req: Request, res: Response) {
      const result = await this.alertingService.checkAlerts(
         req.body as CheckAlertPayload,
      );
      this.logger.info('Checked alerts successfully');
      res.status(200).send(result.response);
   }

   async addToChatHistory(req: Request, res: Response) {
      const result = await this.alertingService.addToChatHistory(
         req.body as AddToHistoryPayload,
      );
      this.logger.info('Added to chat history successfully');
      res.status(200).send(result.response);
   }

   async updateChatHistory(req: Request, res: Response) {
      const result = await this.alertingService.updateChatHistory(req.body);
      this.logger.info('Updated chat history successfully');
      res.status(200).send(result.response);
   }

   async getChatHistoryById(req: Request, res: Response) {
      const result = await this.alertingService.getChatHistoryById(
         req.params.chat_id,
      );
      this.logger.info('Fetched chat history successfully');
      res.status(200).send(result.response);
   }

   async getChatHistory(req: Request, res: Response) {
      const client_id = req.query.client_id as string;

      const result = await this.alertingService.getChatHistory(client_id);
      this.logger.info('Fetched chat history successfully');
      res.status(200).send(result.response);
   }
}
