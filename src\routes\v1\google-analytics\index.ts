import express from 'express';
import asyncHandler from '../../../midddleware/async-handler';

import { logger } from '../../../config/logger';
import { GoogleAnalyticsController } from '../../../controllers/GoogleAnalyticsController';
import { AppDataSource } from '../../../config/data-source';
import { GoogleAnalyticsService } from '../../../services/GoogleAnalyticsService';
const router = express.Router();

const entityManager = AppDataSource.manager;
const googleAnalyticsService = new GoogleAnalyticsService(
   entityManager,
   logger,
);
const googleAnalyticsController = new GoogleAnalyticsController(
   googleAnalyticsService,
);

router.get(
   '/callback',
   asyncHandler(
      googleAnalyticsController.fetchRefreshToken.bind(
         googleAnalyticsController,
      ),
   ),
);

router.get(
   '/login',
   asyncHandler(
      googleAnalyticsController.getGoogleAnalyticsAuthUrl.bind(
         googleAnalyticsController,
      ),
   ),
);

router.post(
   '/all-properties',
   asyncHandler(
      googleAnalyticsController.getProperties.bind(googleAnalyticsController),
   ),
);
router.post(
   '/save/selected-properties',
   asyncHandler(
      googleAnalyticsController.saveProperties.bind(googleAnalyticsController),
   ),
);
export { router as googleAnalyticsRouter };
