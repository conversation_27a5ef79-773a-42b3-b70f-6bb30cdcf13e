import {
   ClientPayload,
   DeleteRecord,
   FixedExpense,
   PaymentMethod,
   ShippingCost,
   ShippingCostByOrderId,
   ShippingProfile,
   UpdateCOGSPayload,
   VariableExpense,
} from '../types/cfo';
import { Config } from '../config';
import { EntityManager } from 'typeorm';
import { Logger } from 'winston';

export class CFOService {
   constructor(
      private entityManager: EntityManager,
      private logger: Logger,
   ) {}

   async upsertCogs(payload: UpdateCOGSPayload): Promise<string> {
      const { clientId, fixedPercent, id } = payload;
      const query = `CALL ${Config.DB_Postgres_SCHEMA}.q_upsert_cost_of_goods($1)`;
      const result = await this.entityManager.query(query, [
         {
            client_id: clientId,
            fixed_percent: fixedPercent ? Number(fixedPercent) : null,
            id,
         },
      ]);

      return result;
   }
   async upsertPaymentGateway(payload: PaymentMethod): Promise<string> {
      const { clientId, method, cost, fee, id } = payload;
      const query = `CALL ${Config.DB_Postgres_SCHEMA}.q_upsert_payment_gateway($1)`;
      const result = await this.entityManager.query(query, [
         {
            client_id: clientId,
            cost: cost ? cost : null,
            fee: fee ? fee : null,
            gateway: method,
            id,
         },
      ]);

      return result;
   }
   async upsertShippingCost(payload: ShippingCost): Promise<string> {
      const { clientId, is_default, id } = payload;
      const query = `CALL ${Config.DB_Postgres_SCHEMA}.q_upsert_shipping_cost($1, $2)`;
      const result = await this.entityManager.query(query, [
         {
            client_id: clientId,
            is_default: is_default,
            id,
         },
         id,
      ]);

      return result;
   }
   async upsertShippingCostByOrderId(
      payload: ShippingCostByOrderId[],
   ): Promise<string> {
      const query = `CALL ${Config.DB_Postgres_SCHEMA}.q_upsert_shipping_costs_by_order_id($1, $2)`;
      const result = await this.entityManager.query(query, [
         JSON.stringify(
            payload.map((d) => {
               return {
                  order_id: d.Order_Id,
                  cost: d.Shipping_Cost,
                  client_id: d.clientId,
               };
            }),
         ),
         null,
      ]);

      return result;
   }
   async upsertFixedExpense (
      payload: FixedExpense,
   ): Promise<string> {
      const query = `CALL ${Config.DB_Postgres_SCHEMA}.q_upsert_fixed_expenses($1, $2)`;
      const result = await this.entityManager.query(query, [
         JSON.stringify(
            {
               id: payload.id,
               client_id: payload.clientId,
               title: payload.title,
               cost: payload.cost,
               source: payload.source,
               categories: JSON.stringify(payload.categories),
               sel_category: payload.selCategory,
               recurring_days: payload.recurringDays,
               start_date: payload.startDate,
               end_date: payload.endDate,
               ad_spend: payload.adSpend
            }),
         payload.id,
      ]);

      return result;
   }
   async upsertVariableExpense (
      payload: VariableExpense,
   ): Promise<string> {
      const query = `CALL ${Config.DB_Postgres_SCHEMA}.q_upsert_variable_expenses($1, $2)`;
      const result = await this.entityManager.query(query, [
         JSON.stringify(
            {
               id: payload.id,
               client_id: payload.clientId,
               name: payload.name,
               campaign: payload.campaign,
               categories: JSON.stringify(payload.categories),
               sel_category: payload.selCategory,
               metric: payload.metric,
               source: payload.source,
               percent: payload.percent,
               start_date: payload.startDate,
               end_date: payload.endDate,
               ad_spend: payload.adSpend
            }),
         payload.id,
      ]);

      return result;
   }
   
   async upsertShippingProfile(payload: ShippingProfile): Promise<string> {
      const query = `CALL ${Config.DB_Postgres_SCHEMA}.q_upsert_shipping_profile($1, $2)`;
      const { clientId, profileName, zones, fixedRate, weightBased } = payload;
      const queryPayload = [];
      if (weightBased.length === 0) {
         queryPayload.push({
            client_id: clientId,
            profile_name: profileName,
            zone: Array.isArray(zones) ? JSON.stringify(zones) : zones,
            is_fixed: true,
            rate: fixedRate,
            min_weight: null,
            max_weight: null,
            measure: null,
         });
      } else {
         weightBased.forEach((item) => {
            queryPayload.push({
               client_id: clientId,
               profile_name: profileName,
               zone: Array.isArray(zones) ? JSON.stringify(zones) : zones,
               is_fixed: false,
               rate: item.rate,
               min_weight: item.minWeight,
               max_weight: item.maxWeight,
               measure: item.measure,
            });
         });
      }
      const result = await this.entityManager.query(query, [
         JSON.stringify(queryPayload),
         null,
      ]);

      return result;
   }
   async getCogs(payload: ClientPayload) {
      const { clientId } = payload;
      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_get_cogs_by_client($1)`;
      const result = await this.entityManager.query(query, [clientId]);

      return result[0].fn_get_cogs_by_client as string;
   }
   async getPaymentGateway(payload: ClientPayload) {
      const { clientId } = payload;
      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_get_payment_methods($1)`;
      const result = await this.entityManager.query(query, [clientId]);

      return result[0].fn_get_payment_methods as string;
   }
   async getShippingCosts(payload: ClientPayload) {
      const { clientId } = payload;
      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_get_shipping_costs($1)`;
      const result = await this.entityManager.query(query, [clientId]);

      return result[0].fn_get_shipping_costs as string;
   }
   async getShippingCostsByOrderId(payload: ClientPayload) {
      const { clientId } = payload;
      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_get_shipping_costs_order_id($1)`;
      const result = await this.entityManager.query(query, [clientId]);

      return result[0].fn_get_shipping_costs_order_id as string;
   }
   async getShippingProfiles(payload: ClientPayload) {
      const { clientId } = payload;
      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_get_shipping_profiles($1)`;
      const result = await this.entityManager.query(query, [clientId]);

      return result[0].fn_get_shipping_profiles as string;
   }
   async getFixedExpenses(payload: ClientPayload) {
      const { clientId } = payload;
      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_get_fixed_expenses($1)`;
      const result = await this.entityManager.query(query, [clientId]);

      return result[0].fn_get_fixed_expenses as string;
   }
   async getVariableExpenses(payload: ClientPayload) {
      const { clientId } = payload;
      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_get_variable_expenses($1)`;
      const result = await this.entityManager.query(query, [clientId]);

      return result[0].fn_get_variable_expenses as string;
   }
   async deleteRecord(payload: DeleteRecord) {
      const { clientId, id, zone, type } = payload;
      const query = `CALL ${Config.DB_Postgres_SCHEMA}.q_delete_cfo_by_condtion($1)`;
      const result = await this.entityManager.query(query, [
         {
            client_id: clientId,
            id,
            zone,
            type,
         },
      ]);

      return result;
   }
}
