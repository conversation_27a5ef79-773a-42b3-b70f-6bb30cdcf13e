export interface FivetranResponse {
   code: string;
   message: string;
}

export interface CreateSource extends FivetranResponse {
   data: { id: string };
}
export interface ConnectCard extends FivetranResponse {
   data: {
      connector_id: string;
      connect_card: {
         uri: string;
         token: string;
      };
   };
}

interface Connector {
   id: string;
   group_id: string;
   service: string;
   schema: string;
}

export interface AllSources extends FivetranResponse {
   data: {
      items: Connector[];
   };
}
