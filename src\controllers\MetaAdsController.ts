import { getFrontendRedirectUrl } from '../utils/common';
import { MetaAdsService } from '../services/MetaAdsService';
import { Request, Response } from 'express';
import { Logger } from 'winston';
import { Config } from '../config';
import { EntityManager } from 'typeorm';

export class MetaAdsController {
   constructor(private metaAdsService: MetaAdsService) {}

   async getGoogleAnalyticsAuthUrl(req: Request, res: Response) {
      res.json({
         url: this.metaAdsService.getmetaAdsAuthUrl(),
      });
   }

   async fetchRefreshToken(req: Request, res: Response) {
      const { code } = req.query;

      if (!code) {
         return res
            .status(400)
            .json({ error: 'Authorization code is required' });
      }
      const longLivedAccessToken =
         await this.metaAdsService.getLongLivedTokenFromCode(code as string);

      const redirectUrl = getFrontendRedirectUrl(
         Config.FRONTEND_DOMAIN?.split(',')[0] || '',
         'integrations',
      );

      res.redirect(`${redirectUrl}?meta=true&t=${longLivedAccessToken}`);
   }
   async listAdAccounts(req: Request, res: Response) {
      const { userAccessToken } = req.body;
      const result =
         await this.metaAdsService.getAllAdAccounts(userAccessToken);

      return res.json(result);
   }
}
