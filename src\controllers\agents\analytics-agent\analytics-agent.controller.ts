import { Request, Response } from 'express';
import { <PERSON><PERSON> } from 'winston';
import { DatabaseError } from 'pg';
import { AnalyticsAgentService } from '../../../services/agents/analytics-agent/analytics-agent.service';
import * as types from '../../../types/agents/analytics-agent/analytics-agent.types';

export class AnalyticsAgentController {
   constructor(
      private logger: Logger,
      private analyticsAgentService: AnalyticsAgentService,
   ) {}

   /**
    * Fetches all chat sessions for a specific user.
    *
    * @route GET /:client_id/:user_id/session
    * @param req.params {client_id, user_id} - Identifiers for client and user.
    * @returns 200 with session data, or 400 on error.
    */
   async fetchAllSessionsByUserID(req: Request, res: Response) {
      try {
         const result =
            await this.analyticsAgentService.fetchAllSessionsByUserID({
               ...req.params,
               ...req.query,
            } as unknown as types.FetchAllSessionsByUserIDPayload);

         res.status(200).send(result);
      } catch (err) {
         const error = err as Error | DatabaseError;
         this.logger.error(`Error fetching all sessions: ${error.message}`);
         res.status(400).send(error.message);
      }
   }

   /**
    * Fetches the entire chat session history for a specific user session.
    *
    * @route GET /:client_id/:user_id/session/:session_id
    * @param req.params {client_id, session_id, user_id} - Identifiers for session context.
    * @returns 200 with session chat history data, or 400 on error.
    */
   async fetchHistoryBySessionID(req: Request, res: Response) {
      try {
         const result =
            await this.analyticsAgentService.fetchHistoryBySessionID(
               req.params as unknown as types.FetchSessionHistoryByIDPayload,
            );

         res.status(200).send(result);
      } catch (err) {
         const error = err as Error | DatabaseError;
         this.logger.error(`Error fetching session history: ${error.message}`);
         res.status(400).send(error.message);
      }
   }

   /**
    * Adds a new chat interaction to an ongoing session history.
    *
    * @route POST /:client_id/:user_id/session/:session_id
    * @param req.params {client_id, session_id, user_id} - Identifiers for locating insights.
    * @param req.body - Includes metadata like user_query, response_status, final_response, etc.
    * @returns 201 on successful insertion, or 400 on failure.
    */
   async addChatToSessionHistory(req: Request, res: Response) {
      try {
         const result =
            await this.analyticsAgentService.addChatToSessionHistory({
               ...req.body,
               ...req.params,
            } as unknown as types.AddChatToSessionHistoryPayload);

         res.status(201).send(result);
      } catch (err) {
         const error = err as Error | DatabaseError;
         this.logger.error(`Error adding to session history: ${error.message}`);
         res.status(400).send(error.message);
      }
   }

   /**
    * Fetches feature usage statistics for a specific user.
    *
    * @route GET /:client_id/:user_id/feature-usage
    * @param req.params {client_id, user_id} - Identifiers for client and user.
    * @returns 200 with feature usage data, or 400 on error.
    */
   async fetchFeatureUsage(req: Request, res: Response) {
      try {
         const result = await this.analyticsAgentService.fetchFeatureUsage({
            ...req.params,
            ...req.query,
         } as unknown as types.FetchFeatureUsagePayload);

         res.status(200).send(result);
      } catch (err) {
         const error = err as Error | DatabaseError;
         this.logger.error(`Error fetching feature usage: ${error.message}`);
         res.status(400).send(error.message);
      }
   }

   /**
    * Tracks feature usage within a specific session.
    *
    * @route POST /:client_id/:user_id/session/:session_id/track-usage
    * @param req.params {client_id, session_id, user_id} - Identifiers for locating insights.
    * @param req.body - Contains feature usage data.
    * @returns 204 No Content on success, or 400 on failure.
    */
   async trackFeatureUsage(req: Request, res: Response) {
      try {
         const result = await this.analyticsAgentService.trackFeatureUsage({
            ...req.body,
            ...req.params,
         } as unknown as types.TrackFeatureUsagePayload);

         res.status(200).send(result);
      } catch (err) {
         const error = err as Error | DatabaseError;
         this.logger.error(`Error tracking feature usage: ${error.message}`);
         res.status(400).send(error.message);
      }
   }

   /**
    * Retrieves all analytical insights generated for a given session.
    *
    * @route GET /:client_id/:user_id/session/:session_id/insights
    * @param req.params {client_id, session_id, user_id} - Identifiers for locating insights.
    * @returns 200 with insights, or 400 on error.
    */
   async fetchSessionInsightsByID(req: Request, res: Response) {
      try {
         const result =
            await this.analyticsAgentService.fetchSessionInsightsByID(
               req.params as unknown as types.FetchChatInsightsByIDPayload,
            );

         res.status(200).send(result);
      } catch (err) {
         const error = err as Error | DatabaseError;
         this.logger.error(`Error fetching chat insights: ${error.message}`);
         res.status(400).send(error.message);
      }
   }

   /**
    * Adds or updates insights for a specific chat within a session.
    *
    * @route POST /:client_id/:user_id/session/:session_id/insights
    * @param req.params {client_id, session_id, user_id} - Identifiers for locating insights.
    * @param req.body - {chat_id, chat_flow_context} - Contains necessary IDs for identifying the chat.
    * @returns 204 No Content on success, or 400 on failure.
    */
   async addInsightsToSessionHistory(req: Request, res: Response) {
      try {
         const result =
            await this.analyticsAgentService.addInsightsToSessionHistory({
               ...req.body,
               ...req.params,
            } as unknown as types.AddInsightsToSessionHistoryPayload);

         res.status(204).send(result);
      } catch (err) {
         const error = err as Error | DatabaseError;
         this.logger.error(`Error adding session insights: ${error.message}`);
         res.status(400).send(error.message);
      }
   }

   /**
    * Updates the like/dislike status for a chat entry.
    *
    * @route PUT /session/:session_id/chat/:chat_id/like-dislike
    * @param req.params - Contains session/chat identifiers and action type.
    * @returns 204 No Content on success, or 400 on error.
    */
   async likeDislikeChat(req: Request, res: Response) {
      try {
         const result = await this.analyticsAgentService.likeDislikeChat({
            ...req.params,
            ...req.body,
         } as unknown as types.LikeDislikeChatPayload);

         res.status(204).send(result);
      } catch (err) {
         const error = err as Error | DatabaseError;
         this.logger.error(`Error liking/disliking chat: ${error.message}`);
         res.status(400).send(error.message);
      }
   }

   /**
    * Flags a chat as rewritten (e.g., for clarifying or improving original AI response).
    *
    * @route PUT /session/:session_id/chat/:chat_id/rewrite
    * @param req.params - Includes session/chat identifiers.
    * @returns 204 on successful update, 400 on error.
    */
   async updateChatRewritten(req: Request, res: Response) {
      try {
         const result = await this.analyticsAgentService.updateChatRewritten(
            req.params as unknown as types.UpdateChatRewrittenPayload,
         );

         res.status(204).send(result);
      } catch (err) {
         const error = err as Error | DatabaseError;
         this.logger.error(`Error updating chat rewritten: ${error.message}`);
         res.status(400).send(error.message);
      }
   }

   /**
    * Flags a chat response as copied (e.g., user copied the AI’s response).
    *
    * @route PUT /session/:session_id/chat/:chat_id/copied
    * @param req.params - Contains identifiers to locate the specific chat entry.
    * @returns 204 on success, or 400 on failure.
    */
   async updateChatCopied(req: Request, res: Response) {
      try {
         const result = await this.analyticsAgentService.updateChatCopied(
            req.params as unknown as types.UpdateChatCopiedPayload,
         );

         res.status(204).send(result);
      } catch (err) {
         const error = err as Error | DatabaseError;
         this.logger.error(`Error updating chat copied: ${error.message}`);
         res.status(400).send(error.message);
      }
   }

   /**
    * Starts a streaming response for a user query.
    *
    * @route POST /:client_id/:user_id/session/:session_id/stream
    * @param req.params - Identifiers for client, user, and session.
    * @param req.body - Contains the user query to be processed.
    * @returns 200 with stream data, or 400 on error.
    */
   async updateChatFeedback(req: Request, res: Response) {
      try {
         const result = await this.analyticsAgentService.updateChatFeedback({
            ...req.body,
            ...req.params,
         } as unknown as types.UpdateChatFeedbackPayload);

         res.status(204).send(result);
      } catch (err) {
         const error = err as Error | DatabaseError;
         this.logger.error(`Error updating chat feedback: ${error.message}`);
         res.status(400).send(error.message);
      }
   }
}
