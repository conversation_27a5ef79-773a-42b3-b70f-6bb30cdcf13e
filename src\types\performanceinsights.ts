export interface GetKPIDto {
   client_id: string;
   objective: string;
   kpis: string[];
   start_date: string;
   end_date: string;
}
export interface GetKPIPayload {
   client_id: string;
   campaign_id: number;
   objective: string;
   start_date: string;
   end_date: string;
}
export interface GetKPISDto {
   client_id: string;
   objective: string;
   kpis: string;
   days: number;
}
export interface GetAdsetsto {
   client_id: string;
   campaign_id: string;
   kpis: string;
   days: number;
}
export interface GetAdsto {
   client_id: string;
   adset_id: string;
   kpis: string;
   days: number;
}

export interface TrackedKPIs {
   client_id: string;
   objective: string;
   campaign_id: string;
   kpi_name: string;
   tracked: boolean;
   channel: string;
}

export interface KPIData {
   kpi_date: string;
   kpi_name: string;
   kpi_value: number | string;
   kpi_previous: string | number;
   kpi_current: string | number;
   campaign_status: string;
   objective?: string;
   currency?: string;
   channel?: string;
   totalKpis?: number;
}
export interface daywiseKPIs {
   [key: string]: Record<string, number>;
}

export interface CampaignData {
   campaign_id: string;
   campaign_name: string;
   campaign_status: 'ACTIVE' | 'PAUSED' | null;
   kpis: KPIData[] | null;
   total_val: KPIAggregate | null;
   prev_total_val: KPIAggregate | null;
   trackedKPIs: TrackedKPIs[];
   day_wise_kpis?: daywiseKPIs | null;
   grouped_kpis?: GroupedKPIAggregate | null;
   kpi_start_date?: string;
   kpi_end_date?: string;
   kpi_prev_start_date?: string;
   kpi_prev_end_date?: string;
}
export type kPIData = KPIData;
export interface CampaignDaywiseDataResponse {
   [key: string]: CampaignData[];
}
export interface CampaignDaywiseKpiResponse {
   [key: string]: CampaignKPI;
}

export interface CampaignKPI {
   kpi_name: string;
   kpi_value: number;
}
export interface KPIsData {
   kpi: string;
   kpi_previous: string | number;
   kpi_current: string | number;
}
export interface CampaignDataKpis {
   campaign_id: string;
   campaign_name: string;
   campaign_status: string;
   kpis: KPIsData[];
}
export interface CampaignKpiswiseDataResponse {
   fn_campaign_with_kpi_details_get: CampaignDataKpis[];
}
export interface DateAgg {
   [key: string]: KPIAggregate;
}
export interface KPIAggregate {
   [key: string]: number | string;
}
export interface GroupedKPIAggregate {
   [key: string]: KPIAggregate;
}

export type KPIKeys =
   | 'roas'
   | 'cpc'
   | 'cpp'
   | 'cpm'
   | 'ctr'
   | 'cpl'
   | 'frequency';
export interface AdsetKpi {
   kpi: string;
   kpi_previous: string | number;
   kpi_current: string | number;
   age_targeting: string;
   age_current: string;
   placement_targeting: string;
   placement_current: string;
   region_current: string;
}

export interface Adset {
   adset_id: string;
   adset_name: string;
   adset_status: string;
   kpis: AdsetKpi[];
}

export interface AdsetWithKpiDataResponse {
   fn_adset_with_kpi_get: Adset[];
}

export interface AdDetails {
   client_id: string;
   ad_id: string;
   creative_id: string;
   caption: string;
   title: string;
   creative_status: string;
   ad_status: string;
   creative_type: string;
   call_to_action_type: string;
   instagram_permalink_url: string;
   video_thumbnail: string;
   redirect_link: string;
   image_hash: string;
   images_in_carousel: string;
   image_link: string;
   updated_time: string;
   created_time: string;
}
export interface AdData {
   ad_details: AdDetails[];
   ad_id: string;
   ad_name: string;
   ad_status: string;
   kpis: KPIsData[];
}
export interface AdDataResponse {
   fn_ad_with_kpi_get: AdData[];
}

export interface GetCampaignSummary {
   client_id: string;
   current_campaign_name: string;
   current_campaign_id: string;
   current_campaign_data: CampaignDataKpis;
   other_campaign_data: CampaignDataKpis[];
}


export interface GetAdsetSummary {
   client_id: string;
   current_adset_id: string;
   current_adset_name: string;
   adset_data: Adset[];
   objective: string;
}

// export type PerformanceInsightsDataResponse = CampaignData[];
export interface TrackedKpisResponseItem {
   [key: string]: CampaignData[];
}
export type TrackedKpisResponse = TrackedKpisResponseItem[];
export interface UpdateUserTrack {
   client_id: string;
   objective: string;
   kpi_name: string;
   campaign_id: string;
   tracked: boolean;
   channel: string;
}

export interface UpdateWSInsightTrack {
   client_id: string;
   tracked: boolean;
   load_date: string;
   insight_text: string;
}

export interface GetTrackedKpis {
   client_id: string;
   channel: string;
   start_date: string;
   end_date: string;
   groupBy: string;
}
export interface GetTrackedPrevKpis {
   client_id: string;
   channel: string;
   groupBy: string;
   start_date: string;
   end_date: string;
   prev_start_date: string;
   prev_end_date: string;
}
export interface KpiPrevData {
   kpi_start_date: string;
   kpi_end_date: string;
   kpi_prev_start_date: string;
   kpi_prev_end_date: string;
   kpi_name: string;
   kpi_value: number | string;
   kpi_prev_agg_value: number;
   objective: string;
}
export interface CampaignPrevData {
   campaign_id: string;
   campaign_name: string;
   kpis: KpiPrevData[];
}
export interface TrackedKpisPrevResponse {
   fn_get_performance_insights_tracked_kpis: CampaignPrevData[];
}
export interface getDynamicObjectives {
   client_id: string;
}

// ---------------------- GOOGLE ADS INTERFACES -----------------------------------

export interface getDynamicChannelTypes {
   client_id: string;
}

export interface getDynamicKpisName extends getDynamicChannelTypes {
   channel_type: string;
}

export interface getCampignDaywise extends getDynamicKpisName {
   kpis: string[];
   start_date: string;
   end_date: string;
   prev_start_date: string;
   prev_end_date: string;
}

export interface getCampaignKpiwise extends getCampignDaywise {
   campaign_id: number;
}

export interface getAds extends getCampaignKpiwise {
   ad_group_id: number;
}

export interface GoogleCampaignDaywiseDataResponse {
   fn_googleads_campaign_daywise_with_multi_kpi_value_get: GoogleCampaignData[];
}

export interface GoogleCampaignData {
   campaign_id: number;
   campaign_name: string;
   campaign_status: string;
   currency: string;
   budget: number;
   kpis: GoogleKPIData[] | null;
   day_wise_kpis?: daywiseKPIs | null;
   trackedKPIs: TrackedKPIs[];
   total_val: GoogleKpiAggregate | null;
   prev_total_val: GoogleKpiAggregate | null;
}

export interface GoogleKPIData {
   kpi_date: string;
   kpi_name: string;
   kpi_value: number;
   campaign_status: string;
   currency: string;
   budget: number;
   device: string | null;
   hour: string | null;
   day_of_week: string | null;
   // [key: string]: any
}

export interface GoogleDateAgg {
   [key: string]: GoogleKpiAggregate;
}
export interface GoogleKpiAggregate {
   [key: string]: number | string | null;
}

export interface GoogleAdsCampaignKpiwiseDataResponse {
   fn_googleads_campaign_kpi_wise_data_get: GogoleKpisData[];
}
export interface GogoleKpisData {
   campaign_id: number;
   campaign_name: string;
   kpis: GoogleKPIData[] | null;
   kpi_wise_data: KpiwiseAggregate;
   total_val: GoogleKpiAggregate | null;
}

export interface KpiwiseAggregate {
   [key: string]: KpiEntry[];
}

export interface KpiEntry {
   date_time: string;
   kpi_value: number;
   budget: number;
   currency: string;
}
export interface GoogleAdgroupsDataResponse {
   fn_googleads_adgroup_with_kpi_get: GoogleAdgroupsData[];
}

export interface GoogleAdgroupsData {
   ad_group_id: number;
   ad_group_name: string;
   ad_group_status: string;
   kpi_values: GoogleKPIData[];
   kpis: GoogleKpiAggregate[];
   device_wise: DimensionsAggregate;
   hour_wise: DimensionsAggregate;
   day_of_week_wise: DimensionsAggregate;
   prev_total_val: GoogleKpiAggregate | null;
   device: AggregatedResult;
   hour: AggregatedResult;
   day_of_week: AggregatedResult;
}

export interface DimensionsAggregate {
   [kpiName: string]: Array<{
      keyword_name: string;
      current_value: number | null;
      previous_value: number | null;
   }>;
}

export interface AggregatedResult {
   [kpiName: string]: Array<{
      keyword_name: string;
      aggregate_value: number;
   }>;
}

export interface GoogleAdsDataResponse {
   fn_googleads_ads_with_kpi_get: GoogleAdsData[];
}

export interface GoogleAdsData {
   ad_id: number;
   ad_name: string;
   ad_status: string;
   ad_url: string;
   ad_strength: string;
   headlines: string;
   kpi_values: GoogleKPIData[];
   kpis: GoogleKpiAggregate[];
   prev_total_val: GoogleKpiAggregate | null;
}

export interface AdgroupsKpiData {
   kpi_date: string;
   kpi_name: string;
   kpi_value: number;
}

export type KpiFormulas = {
   [key in string]: (
      aggregatedResults: Record<string, Map<string, number>>,
      keywordName: string,
   ) => number;
};

export type GoogleAdsKpiKeys =
   | 'roas'
   | 'cpa'
   | 'cpm'
   | 'cpc'
   | 'ctr'
   | 'conversion_rate';

export type DimensionKeys = 'device' | 'hour' | 'day_of_week';

export type keywordKPIData = {
   [metric: string]: number | null;
 };
 
 export type KeywordEntry = keywordKPIData | (keywordKPIData & { kpi: { keyword: keywordKPIData } });
 
 export type KeywordGoogleAdsData = {
   [matchType: string]: {
     [keyword: string]: KeywordEntry;
   };
 };
 
 export type GoogleAdsSearchTermDataResponse = {
   structured_result: KeywordGoogleAdsData;
 };
 
 export type GoogleAdsKeywordDataResponse = {
   structured_result: KeywordGoogleAdsData;
 };
 

export interface trackedkpisDataResponse {
   fn_get_googleads_campaign_kpis: trackedkpisData[];
}

export interface trackedkpisData {
   campaign_id: number;
   campaign_name: string;
   channel_type: string;
   kpi_name: string;
   kpis: GoogleKPIData[] | null;
   currency: string;
   campaign_status: string;
   total_val: Record<string, number | null> | null;
   grouped_kpis: trackedGroupKpis | null;
   kpi_start_date?: string;
   kpi_end_date?: string;
   kpi_prev_start_date?: string;
   kpi_prev_end_date?: string;
}

export interface trackedGroupKpis {
   [key: string]: Record<string, number | null>;
}

export interface trackedPrevKpisDataResponse {
   fn_get_googleads_campaign_kpis: trackedPrevkpisData[];
}
export interface trackedPrevkpisData extends trackedkpisData {
   kpi_start_date: string;
   kpi_end_date: string;
   kpi_prev_start_date: string;
   kpi_prev_end_date: string;
}
