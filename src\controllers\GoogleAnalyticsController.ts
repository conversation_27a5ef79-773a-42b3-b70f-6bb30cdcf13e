import { getFrontendRedirectUrl } from '../utils/common';
import { GoogleAnalyticsService } from '../services/GoogleAnalyticsService';
import { Request, Response } from 'express';
import { Logger } from 'winston';
import { Config } from '../config';
import { EntityManager } from 'typeorm';

export class GoogleAnalyticsController {
   constructor(private googleAnalyticsService: GoogleAnalyticsService) {}

   async getGoogleAnalyticsAuthUrl(req: Request, res: Response) {
      res.json({
         url: this.googleAnalyticsService.getGoogleAnalyticsAuthUrl(),
      });
   }

   async fetchRefreshToken(req: Request, res: Response) {
      const { code } = req.query;

      if (!code)
         return res.status(400).json({ error: 'Authorization code required' });

      const { access, refresh } =
         await this.googleAnalyticsService.getAccessTokenFromCode(
            code as string,
         );
      const redirectUrl = getFrontendRedirectUrl(
         Config.FRONTEND_DOMAIN?.split(',')[0] || '',
         'integrations',
      );

      res.redirect(`${redirectUrl}?ga=true&t=${access}&r=${refresh}`);
   }

   async getProperties(req: Request, res: Response) {
      const clientId = req.body.clientId;
      if (!clientId) {
         return res.status(400).json({ error: 'clientId is required' });
      }
      const properties =
         await this.googleAnalyticsService.getProperties(clientId);
      res.status(200).json(properties);
   }
   async saveProperties(req: Request, res: Response) {
      const clientId = req.body.clientId;
      const selectedProperties = req.body.selectedProperties;
      console.log('clientId', clientId);
      console.log('properties', selectedProperties);
      if (!clientId) {
         return res.status(400).json({ error: 'clientId is required' });
      }
      const savedProperties = await this.googleAnalyticsService.saveProperties(
         clientId,
         selectedProperties,
      );
      res.status(200).json(savedProperties);
   }
}
