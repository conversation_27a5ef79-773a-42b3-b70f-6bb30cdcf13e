import { Request, Response } from 'express';
import { AuthService } from '../services/AuthService';
import { Logger } from 'winston';
import {
   LogoutDetails,
   LoginDetails,
   SendOTPDetails,
   RegisterDetails,
   ResetPasswordPayload,
   VerifyEmailPayload,
   IsEmailVerifiedPayload,
   RefreshTokenDetails,
} from '../types';
import { Config } from '../config';

export class AuthController {
   constructor(
      private logger: Logger,
      private authService: AuthService,
   ) {}

   async handleRegister(req: Request, res: Response) {
      const { full_name, email_address, password, cb_product_updates } =
         req.body as RegisterDetails;
      const result = await this.authService.register({
         full_name,
         email_address,
         password,
         cb_product_updates,
      });
      this.logger.info('User has been registered successfully');
      res.status(200).send(result.response);
   }

   async handleLogin(req: Request, res: Response) {
      const { email_address, password } = req.body as LoginDetails;
      const result = await this.authService.login({ email_address, password });
      this.logger.info('User has been logged in successfully');
      result.refreshToken &&
         res.cookie('refresh_token', result.refreshToken, {
            httpOnly: true,
            maxAge: 24 * 60 * 60 * 1000,
         });
      res.status(200).send(result.response);
   }

   async handleLogout(req: Request, res: Response) {
      const { email_address } = req.body as LogoutDetails;
      const result = await this.authService.logout({ email_address });
      this.logger.info('User has been logged out successfully');
      res.status(200).send(result);
   }

   async handleRefreshToken(req: Request, res: Response) {
      const { email_address, client_id } = req.body as RefreshTokenDetails;
      const result = await this.authService.setRefreshToken({
         email_address,
         client_id,
      });
      this.logger.info('Refresh token has been set successfully');
      result.refreshToken &&
         res.cookie('refresh_token', result.refreshToken, {
            httpOnly: true,
            maxAge: eval(Config.MAX_AGE_REFRESH_TOKEN as string),
         });
      res.status(200).send(result.response);
   }

   async handleSendOtp(req: Request, res: Response) {
      const { email_address, action } = req.body as SendOTPDetails;
      const result = await this.authService.sendOtp(email_address, action);
      this.logger.info('OTP has been sent successfully');
      res.status(200).send(result.response);
   }

   async handleVerifyEmail(req: Request, res: Response) {
      const { email_address, email_otp, action } =
         req.body as VerifyEmailPayload;
      const result = await this.authService.verifyEmail({
         email_address,
         email_otp,
         action,
      });
      this.logger.info('User has been verified successfully');
      res.status(200).send(result.response);
   }

   async handleIsEmailVerified(req: Request, res: Response) {
      const result = await this.authService.isEmailVerified(
         req.query as IsEmailVerifiedPayload,
      );
      this.logger.info('Fetched user email verification details successfully');
      res.status(200).send(result.response);
   }

   async handleResetPassword(req: Request, res: Response) {
      const { email_address, password } = req.body as ResetPasswordPayload;

      const result = await this.authService.resetPassword({
         email_address,
         password,
      });

      this.logger.info('User has been reset password successfully');
      res.status(200).send(result.response);
   }
}
