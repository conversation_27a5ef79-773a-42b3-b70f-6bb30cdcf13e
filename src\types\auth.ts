export interface UserDto {
   firstName: string;
   lastName: string;
   email: string;
   password: string;
   role: string;
}

export interface LoginDto {
   email: string;
   password: string;
}

export interface UserLoginStatus {
   email_address: string;
   client_id: string;
   company_url: string;
   user_role: string;
   login_status: string;
   unsuccessful_login_attempts: number;
   register_progress: string;
   profile_image: string | null;
}

export interface UserLoginStatusResponse {
   [key: string]: UserLoginStatus[];
}

export interface UserToken {
   email_address: string;
   client_id: string;
   email_token: string;
   last_change_date: string;
}

export interface UserTokenResponse {
   fn_user_token_get: UserToken[];
}

export interface EmailDetails {
   email_address: string;
}

export interface LogoutDetails extends EmailDetails {}

export interface LoginDetails extends LogoutDetails {
   password: string;
}
export interface RegisterDetails extends LoginDetails {
   full_name: string;
   cb_product_updates: boolean;
}

export interface SendOTPDetails {
   email_address: string;
   action: 'email-verification' | 'password-reset';
}

export interface User extends RegisterDetails {
   user_active: string;
   register_progress: string;
}

export interface VerifyEmailPayload extends EmailDetails {
   email_otp: string;
   action: 'email-verification' | 'password-reset';
}

export interface RefreshTokenDetails extends EmailDetails {
   client_id: string;
}

export interface IsEmailVerifiedPayload extends EmailDetails {
   [key: string]: string;
}

export interface ResetPasswordPayload extends EmailDetails {
   [key: string]: string;
   password: string;
}

export interface EmailVerificationDetails {
   email_address: string;
   email_otp: string;
   is_email_verified: boolean;
   update_date: string;
   forget_password_otp: string;
   forget_password_update_date: string;
}
