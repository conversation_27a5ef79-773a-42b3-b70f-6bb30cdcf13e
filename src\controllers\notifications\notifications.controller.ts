import { Request, Response } from 'express';
import { <PERSON>gger } from 'winston';
import * as types from '../../types/notifications/notifications.types';
import { NotificationsService } from '../../services/notifications/notifications.service';

export class NotificationsController {
   constructor(
      private logger: Logger,
      private notificationsService: NotificationsService,
   ) {}

   async fetchAllNotificationsByUserID(
      req: Request,
      res: Response,
   ): Promise<void> {
      const { user_id, client_id } = req.params;

      try {
         const notifications =
            await this.notificationsService.fetchAllNotificationsByUserID({
               user_id,
               client_id,
            });

         res.status(200).json(notifications);
      } catch (error) {
         this.logger.error('Error fetching notifications:', error);
         res.status(500).json({
            success: false,
            message: 'Failed to fetch notifications',
         });
      }
   }

   async createNotification(req: Request, res: Response): Promise<void> {
      const payload: types.CreateNotificationPayload = req.body;

      try {
         await this.notificationsService.createNotification(payload);

         res.status(201).json({
            success: true,
            message: 'Notification created successfully',
         });
      } catch (error) {
         this.logger.error('Error creating notification:', error);
         res.status(500).json({
            success: false,
            message: 'Failed to create notification',
         });
      }
   }

   async markNotificationAsRead(req: Request, res: Response): Promise<void> {
      const { user_id, client_id, id } = req.params;

      try {
         await this.notificationsService.markNotificationAsRead({
            user_id,
            client_id,
            id,
         });

         res.status(200).json({
            success: true,
            message: 'Notification marked as read successfully',
         });
      } catch (error) {
         this.logger.error('Error marking notification as read:', error);
         res.status(500).json({
            success: false,
            message: 'Failed to mark notification as read',
         });
      }
   }
}
