import { DatabaseError } from 'pg';
import { AnalyticsAgentModel } from '../../../models/agents/analytics-agent/analytics-agent.model';
import * as types from '../../../types/agents/analytics-agent/analytics-agent.types';

export class AnalyticsAgentService {
   constructor(private analyticsAgentModels: AnalyticsAgentModel) {}

   async fetchAllSessionsByUserID(
      payload: types.FetchAllSessionsByUserIDPayload,
   ): Promise<types.AnalyticsAgentChat[]> {
      try {
         return await this.analyticsAgentModels.fetchAllSessionsByUserID(
            payload,
         );
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async fetchHistoryBySessionID(
      payload: types.FetchSessionHistoryByIDPayload,
   ): Promise<types.AnalyticsAgentChat[]> {
      try {
         return await this.analyticsAgentModels.fetchHistoryBySessionID(
            payload,
         );
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async addChatToSessionHistory(
      payload: types.AddChatToSessionHistoryPayload,
   ): Promise<types.AnalyticsAgentChat[]> {
      try {
         return await this.analyticsAgentModels.addChatToSessionHistory(
            payload,
         );
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async fetchFeatureUsage(
      payload: types.FetchFeatureUsagePayload,
   ): Promise<types.FeatureUsage> {
      try {
         return await this.analyticsAgentModels.fetchFeatureUsage(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async trackFeatureUsage(
      payload: types.TrackFeatureUsagePayload,
   ): Promise<void> {
      try {
         await this.analyticsAgentModels.trackFeatureUsage(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async fetchSessionInsightsByID(
      payload: types.FetchChatInsightsByIDPayload,
   ): Promise<types.AnalyticsAgentChat[]> {
      try {
         return await this.analyticsAgentModels.fetchSessionInsightsByID(
            payload,
         );
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async addInsightsToSessionHistory(
      payload: types.AddInsightsToSessionHistoryPayload,
   ) {
      try {
         await this.analyticsAgentModels.addInsightsToSessionHistory(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async likeDislikeChat(payload: types.LikeDislikeChatPayload) {
      try {
         await this.analyticsAgentModels.likeDislikeChat(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async updateChatRewritten(payload: types.UpdateChatRewrittenPayload) {
      try {
         await this.analyticsAgentModels.updateChatRewritten(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async updateChatCopied(payload: types.UpdateChatCopiedPayload) {
      try {
         await this.analyticsAgentModels.updateChatCopied(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }

   async updateChatFeedback(payload: types.UpdateChatFeedbackPayload) {
      try {
         await this.analyticsAgentModels.updateChatFeedback(payload);
      } catch (err) {
         const error = err as Error | DatabaseError;
         throw new Error(error.message);
      }
   }
}
