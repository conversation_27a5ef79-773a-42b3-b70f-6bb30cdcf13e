import { Logger } from 'winston';
import { Request, Response } from 'express';
import { DynamicInsightService } from '../services/DynamicInsightService';
import * as types from '../types';

export class DynamicInsightController {
   constructor(
      private dynamicInsightService: DynamicInsightService,
      private logger: Logger,
   ) {}

   async handleFetchRank(req: Request, res: Response) {
      const { client_id, days } = req.body as types.GetRankDto;

      const rankData = await this.dynamicInsightService.fetchRank(
         client_id,
         days,
      );
      this.logger.info('Dynamic Insights Rank details of ' + client_id);
      res.json({ rankData });
   }
   async handleFetchWebInsightTrackedIds(req: Request, res: Response) {
      const body = req.body as { clientId: string; days: string };
      const trackedInsights =
         await this.dynamicInsightService.fetchTrackedInsights(
            body.clientId,
            body.days,
         );

      res.json({ trackedInsights });
   }
}
