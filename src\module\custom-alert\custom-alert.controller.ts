import { Request, Response } from 'express';
import { Logger } from 'winston';
import * as types from './custom-alert-types';
import { CustomAlertService } from './custom-alert.service';

export class CustomAlertController {
   constructor(
      private logger: Logger,
      private customAlertService: CustomAlertService,
   ) {}

   async getAllAlerts(req: Request, res: Response) {
      try {
         const { client_id, user_id } = req.params;
         const alerts = await this.customAlertService.getAllAlerts(
            client_id,
            user_id,
         );
         res.status(200).json(alerts);
      } catch (error) {
         this.logger.error('Error fetching all alerts:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }

   async getAlertById(req: Request, res: Response) {
      try {
         const alertId = req.params.alertId;
         const alert = await this.customAlertService.getAlertById(alertId);

         if (!alert) {
            return res.status(404).json({ error: 'Alert not found' });
         }

         res.status(200).json(alert);
      } catch (error) {
         this.logger.error('Error fetching alert by ID:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }

   async createOrUpdateAlert(req: Request, res: Response) {
      try {
         const alertData: types.CustomAlert = req.body;
         const alert =
            await this.customAlertService.createOrUpdateAlert(alertData);

         if (!alert) {
            return res.status(400).json({
               status: 'error',
               message: 'Failed to create or update alert',
            });
         }

         res.status(201).json(alert);
      } catch (error) {
         this.logger.error('Error creating or updating alert:', error);
         res.status(500).json({
            status: 'error',
            message: 'Internal Server Error',
         });
      }
   }

   async deleteAlert(req: Request, res: Response) {
      try {
         const alertId = req.params.alertId;
         await this.customAlertService.deleteAlert(alertId);
         res.status(204).send();
      } catch (error) {
         this.logger.error('Error deleting alert:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }

   async deleteMultipleAlerts(req: Request, res: Response) {
      try {
         const { alertIds } = req.body;
         if (!Array.isArray(alertIds) || alertIds.length === 0) {
            return res.status(400).json({ error: 'Invalid alert IDs' });
         }

         await this.customAlertService.deleteMultipleAlerts(alertIds);
         res.status(204).send();
      } catch (error) {
         this.logger.error('Error deleting multiple alerts:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }

   async fetchOptions(req: Request, res: Response) {
      try {
         const clientId = req.params.client_id;
         const options = await this.customAlertService.fetchOptions(clientId);
         res.status(200).json(options);
      } catch (error) {
         this.logger.error('Error fetching options:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }

   async checkAlertConditions(req: Request, res: Response) {
      try {
         const { alerts } = req.body;
         const conditions =
            await this.customAlertService.checkAlertConditions(alerts);
         res.status(200).json(conditions);
      } catch (error) {
         this.logger.error('Error checking alert conditions:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }
}
