import { Config } from '../config/index';

const nodemailer = require('nodemailer');

interface EmailInfo {
   to: string;
   subject: string;
   text: string;
   html: string;
}

export class EmailService {
   async sendEmail(emailInfo: EmailInfo): Promise<string> {
      const { to, subject, text, html } = emailInfo;
      try {
         const transporter = nodemailer.createTransport({
            host: Config.MAIL_HOST,
            port: 587,
            secure: false, // true for 465, false for other ports
            auth: {
               user: Config.MAIL_USERNAME,
               pass: Config.MAIL_PASSWORD,
            },
            tls: {
               rejectUnauthorized: false,
            },
         });
         var info = await transporter.sendMail({
            from: Config.MAIL_USERNAME,
            to, // list of receivers
            subject, // Subject line
            text, // plain text body
            html, // html body
         });
      } catch (error) {
         console.log(error, 'email error');
      }
      return info;
   }
}
