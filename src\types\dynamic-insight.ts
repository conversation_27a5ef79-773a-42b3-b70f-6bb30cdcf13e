export interface GetRankDto {
   client_id: string;
   days: string;
}

export interface DynamicInsightRankData {
   business_category: string;
   insight_type: string;
   insight_text: string;
   insight_value: string;
}

export interface TrackedVisitCount {
   date: string;
   hour: string;
   visit_count: string;
}

export interface WebInsightTrackedData {
   insight_text: string;
   business_category: string;
   data: TrackedVisitCount[];
}

export interface DynamicInsightsRankDataResponse {
   fn_dynamic_insights_rank_get: DynamicInsightRankData[];
}

export interface WebInsightTrackedResponse {
   fn_get_dynamic_tracked_data: WebInsightTrackedData[];
}
