import { Config } from '../../config';
import { fetchAndProcessData } from './airbyte';
import { Logger } from 'winston';

const cronJobCycle: string = Config.CRON_JOB_CYCLE || '';

class Scheduler {
   private cron: typeof import('node-cron');
   private tasks: { [key: string]: (() => void)[] };

   constructor(
      cron: typeof import('node-cron'),
      private logger: Logger,
   ) {
      this.cron = cron;
      this.tasks = {
         [cronJobCycle]: [fetchAndProcessData],
      };
   }

   scheduleTasks(): void {
      for (const [interval, tasks] of Object.entries(this.tasks)) {
         for (const task of tasks) {
            try {
               if (!interval) {
                  this.logger.info('No interval provided for cron');
                  return;
               }
               this.logger.info('SCHEDULING started');
               this.logger.info(task.name);
               this.cron.schedule(interval, () => {
                  task();
               });
            } catch (err) {
               this.logger.error(err);
            }
         }
      }
   }
}

export default Scheduler;
