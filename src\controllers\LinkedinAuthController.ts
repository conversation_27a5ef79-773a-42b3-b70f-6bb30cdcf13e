import { Request, Response } from 'express';
import { <PERSON>gger } from 'winston';
import { LinkedinAuthService } from '../services/LinkedinAuthService';
import { Config } from '../config';
import { getFrontendRedirectUrl } from '../utils/common';

export class LinkedinAuthController {
   constructor(
      private linkedinAuthService: LinkedinAuthService,
      private logger: Logger,
   ) {}

   // eslint-disable-next-line @typescript-eslint/require-await
   async handleAuthorize(req: Request, res: Response) {
      const { client, redirectPathName } = req.query;
      res.redirect(
         this.linkedinAuthService.authorize(
            client as string,
            `${Config.LINKEDIN_REDIRECT_URI!}?redirect_path_name=${redirectPathName}`,
         ),
      );
   }

   async handleRedirect(req: Request, res: Response) {
      const { code, state, redirect_path_name } = req.query;

      const tokenData = await this.linkedinAuthService.fetchTokenData(
         code as string,
         state as string,
         redirect_path_name as string,
      );

      // TOOD: token data is an object which contains two keys,
      // TODO: WHatever url this method 'getFrontendRedirectUrl' returns
      // I wanna append the token data into that url
      const domainUrl = getFrontendRedirectUrl(
         Config.FRONTEND_DOMAIN?.split(',')[0] || '',
         redirect_path_name as string,
      );

      const urlWithQueryParams = new URL(domainUrl);
      Object.entries(tokenData).forEach(([key, value]) => {
         if (value !== undefined && value !== null) {
            urlWithQueryParams.searchParams.append(key, String(value));
         }
      });

      res.redirect(urlWithQueryParams.toString());
   }
}
