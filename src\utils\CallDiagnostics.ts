import { Config } from "../config/index";
import axios from "axios";

export async function callDiagnostics<T, P>(
    endpoint: string,
    payload: P,
 ): Promise<T | null> {
    try {
       const response = await axios.post<T>(
          `${Config.DIAGNOSTICS_URL}/${endpoint}`,
          payload,
          {
             headers: {
                'Content-Type': 'application/json',
             },
          },
       );

       return response?.data;
    } catch (error) {
       return null;
    }
 }