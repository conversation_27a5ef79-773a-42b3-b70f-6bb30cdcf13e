import { Config } from '../config';

export const confirmUserRoleEmail = (
   full_name: string,
   role: string,
   company_name: string,
   confirm_account_url: string,
) => {
   return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8" />
                <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                <meta http-equiv="X-UA-Compatible" content="ie=edge" />
                <title>User Role Confirmation</title>

                <link
                href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap"
                rel="stylesheet"
                />
            </head>
            <body
                style="
                margin: 0;
                font-family: 'Poppins', sans-serif;
                background: #ffffff;
                font-size: 14px;
                "
            >
                <div
                style="
                    max-width: 680px;
                    margin: 0 auto;
                    padding: 45px 30px 60px;
                    background: #f4f7ff;
                    background-image: url(https://archisketch-resources.s3.ap-northeast-2.amazonaws.com/vrstyler/1661497957196_595865/email-template-background-banner);
                    background-repeat: no-repeat;
                    background-size: 800px 452px;
                    background-position: top center;
                    font-size: 14px;
                    color: #434343;
                "
                >
                <header>
                    <table style="width: 100%">
                    <tbody>
                        <tr style="height: 0">
                        <td>
                            <img
                            alt=""
                            src="https://flable.ai/assets/images/logo.png"
                            height="40px"
                            />
                        </td>
                        <td style="text-align: right">
                            <span style="font-size: 14px; line-height: 30px; color: #ffffff"
                            >${new Date().toDateString()}</span
                            >
                        </td>
                        </tr>
                    </tbody>
                    </table>
                </header>

                <main>
                    <div
                    style="
                        margin: 0;
                        margin-top: 70px;
                        padding: 92px 30px 115px;
                        background: #ffffff;
                        border-radius: 30px;
                        text-align: center;
                    "
                    >
                    <div style="width: 100%; max-width: 489px margin: 0 auto">
                        <h1
                        style="
                            margin: 0;
                            font-size: 24px;
                            font-weight: 500;
                            color: #1f1f1f;
                        "
                        >
                        User Confirmation
                        </h1>
                        <p
                        style="
                            margin: 0;
                            margin-top: 17px;
                            font-weight: 500;
                            letter-spacing: 0.56px;
                            text-align: left;
                        "
                        >
                        Dear <strong>${full_name}</strong>,
                        </p>
                        <p
                        style="
                            margin: 0;
                            margin-top: 17px;
                            font-weight: 500;
                            letter-spacing: 0.56px;
                            text-align: left;
                        "
                        >
                        You are receiving this mail from ${Config.FRONTEND_DOMAIN?.split(',')[0]}
                        </p>
                        <p
                        style="
                            margin: 0;
                            margin-top: 17px;
                            font-weight: 500;
                            letter-spacing: 0.56px;
                            text-align: left;
                        "
                        >
                        We are excited to inform you that you have been added as an
                        <strong>${role}</strong> on the Flable platform for
                        ${company_name}. If this was expected, please confirm your account:
                        </p>
                        <button
                            style="
                                margin: 0;
                                margin-top: 17px;
                                font-weight: 500;
                                letter-spacing: 0.56px;
                                padding: 10px;
                                background-color: #4c76a8;
                                border-color: transparent;
                                border-radius: 5px;
                            "
                        >
                        <a
                            href="${confirm_account_url}"
                            style="
                            text-decoration: none;
                            color: black;
                            font-size: 14px;
                            font-weight: bold;
                            color: white;
                            "
                            class="button"
                            >Confirm Account</a
                        >
                        </button>
                        <p style="text-align: left; margin-top: 15px;">If the above button doesn't work as expected, please click on the link below:</p>
                         <p
                        style="
                            margin: 0;
                            font-size: 12px;
                            margin-top: 17px;
                            font-weight: 500;
                            letter-spacing: 0.56px;
                            text-align: left;
                            word-break: break-all;
                        "
                        >
                        ${confirm_account_url}
                        </p>
                        <p
                        style="
                            margin: 0;
                            margin-top: 7px;
                            font-weight: 500;
                            letter-spacing: 0.56px;
                        "
                        >
                        If you were not expecting this email, you can either report this
                        incident to our support team or simply ignore this message.
                        </p>
                        <p
                        style="
                            margin: 0;
                            margin-top: 7px;
                            font-weight: 500;
                            letter-spacing: 0.56px;
                        "
                        >
                        Regards, Flable Team
                        </p>
                    </div>
                    </div>

                    <p
                    style="
                        max-width: 400px;
                        margin: 0 auto;
                        margin-top: 90px;
                        text-align: center;
                        font-weight: 500;
                        color: #8c8c8c;
                    "
                    >
                    For any further assistance or inquiries, please don't hesitate to
                    reach out to our dedicated support team at
                    <a
                        href="mailto:<EMAIL>"
                        style="color: #499fb6; text-decoration: none"
                        ><EMAIL></a
                    >
                    or visit our
                    <a
                        href="https://flable.ai/contact.html"
                        target="_blank"
                        style="color: #499fb6; text-decoration: none"
                        >Help Center</a
                    >
                    </p>
                </main>

                <footer
                    style="
                    width: 100%;
                    max-width: 490px;
                    margin: 20px auto 0;
                    text-align: center;
                    border-top: 1px solid #e6ebf1;
                    "
                >
                    <p
                    style="
                        margin: 0;
                        margin-top: 40px;
                        font-size: 16px;
                        font-weight: 600;
                        color: #434343;
                    "
                    >
                    Flable.ai
                    </p>
                    <div style="margin: 0; margin-top: 16px">
                    <a
                        href="https://www.linkedin.com/company/flable/"
                        target="_blank"
                        style="display: inline-block"
                    >
                        <img
                        width="36px"
                        alt="LinkedIn"
                        src="https://cdn2.iconfinder.com/data/icons/social-media-2285/512/1_Linkedin_unofficial_colored_svg-256.png"
                        />
                    </a>

                    <a
                        href="https://twitter.com/Flableai"
                        target="_blank"
                        style="display: inline-block; margin-left: 8px"
                    >
                        <img
                        width="36px"
                        alt="Twitter"
                        src="https://cdn2.iconfinder.com/data/icons/threads-by-instagram/24/x-logo-twitter-new-brand-64.png"
                        />
                    </a>
                    <a
                        href="https://www.youtube.com/channel/UCfTiob-n1Lzjd9d1G4fDiLQ"
                        target="_blank"
                        style="display: inline-block; margin-left: 8px"
                    >
                        <img
                        width="36px"
                        alt="Youtube"
                        src="https://cdn2.iconfinder.com/data/icons/social-media-2285/512/1_Youtube_colored_svg-256.png"
                    /></a>
                    </div>
                    <p style="margin: 0; margin-top: 16px; color: #434343">
                    Copyright © 2025 Company. All rights reserved.
                    </p>
                    <table
                    style="display: inline-block; vertical-align: middle"
                    cellspacing="3px"
                    cellpadding="3px"
                    >
                    <tr>
                        <td style="text-align: center">
                        <a href="https://flable.ai/privacy-policy.html" target="_blank"
                            >Privacy policy</a
                        >
                        </td>
                        <td style="text-align: center">
                        <a href="https://flable.ai/Terms-sevice.html" target="_blank"
                            >Terms of service</a
                        >
                        </td>
                    </tr>
                    </table>
                </footer>
                </div>
            </body>
            </html>
`;
};

export const returnSnippetEmailHTML = (client_id: string) => {
   return `
   <!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Snippet Installation Guidance</title>

    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap"
      rel="stylesheet"
    />
  </head>
  <body
    style="
      margin: 0;
      font-family: 'Poppins', sans-serif;
      background: #ffffff;
      font-size: 14px;
    "
  >
    <div
      style="
        max-width: 680px;
        margin: 0 auto;
        padding: 45px 30px 60px;
        background: #f4f7ff;
        background-image: url(https://archisketch-resources.s3.ap-northeast-2.amazonaws.com/vrstyler/1661497957196_595865/email-template-background-banner);
        background-repeat: no-repeat;
        background-size: 800px 452px;
        background-position: top center;
        font-size: 14px;
        color: #434343;
      "
    >
      <header>
        <table style="width: 100%">
          <tbody>
            <tr style="height: 0">
              <td>
                <img
                  alt=""
                  src="https://flable.ai/assets/images/logo.png"
                  height="40px"
                />
              </td>
              <td style="text-align: right">
                <span style="font-size: 14px; line-height: 30px; color: #ffffff"
                  >${new Date().toDateString()}</span
                >
              </td>
            </tr>
          </tbody>
        </table>
      </header>

      <main>
        <div
          style="
            margin: 0;
            margin-top: 70px;
            padding: 92px 30px 115px;
            background: #ffffff;
            border-radius: 30px;
            text-align: center;
          "
        >
          <div style="width: 100%; max-width: 489px margin: 0 auto">
            <h1
              style="
                margin: 0;
                font-size: 24px;
                font-weight: 500;
                color: #1f1f1f;
              "
            >
              Installation Guidance
            </h1>
            <p
              style="
                margin: 0;
                margin-top: 17px;
                font-weight: 500;
                letter-spacing: 0.56px;
              "
            >
              You are receiving this mail from
              ${Config.FRONTEND_DOMAIN?.split(',')[0]}
            </p>
            <p
              style="
                margin: 0;
                margin-top: 17px;
                font-weight: 500;
                letter-spacing: 0.56px;
              "
            >
              Please copy and paste the below code snippet into the header
              section of the website
            </p>

            <div
              style="
                margin: 2%;
                border: 1px solid #000000;
                box-shadow: 0px 0px 4px 0px #00000040;
                padding: 5px 0px 5px 0px;
                border-radius: 8px;
                text-align: left;
              "
            >
              <pre><code>
          &lt;script&gt;
              (function(f, l, a, b, t, i, c) {
              f[a] = f[a] || function() {
              (f[a].q = f[a].q || []).push(arguments)
              };
              i = l.createElement(b);
              i.async = 1;
              i.src ='${Config.SCRIPT_SRC}/api/config/'+t;
              c = l.getElementsByTagName(b)[0];
              c.parentNode.insertBefore(i, c);
              })(window, document, "flable", "script", "${client_id}");
          &lt;/script &gt;</code></pre>
            </div>
            <p
              style="
                margin: 0;
                margin-top: 17px;
                font-weight: 500;
                letter-spacing: 0.56px;
              "
            >
              Click on this to
              <a
                href="https://www.youtube.com/watch?v=f8uhNdkspco&feature=youtu.be"
                target="_blank"
                style="color: #499fb6; text-decoration: none"
                >link</a
              >
              see a demo on how to install the snippet.
            </p>
            <p
              style="
                margin: 0;
                margin-top: 17px;
                font-weight: 500;
                letter-spacing: 0.56px;
              "
            >
              Best Regards,
            </p>
            <p
              style="
                margin: 0;
                margin-top: 7px;
                font-weight: 700;
                letter-spacing: 0.56px;
              "
            >
              The Flable team
            </p>
          </div>
        </div>

        <p
          style="
            max-width: 400px;
            margin: 0 auto;
            margin-top: 90px;
            text-align: center;
            font-weight: 500;
            color: #8c8c8c;
          "
        >
          For any further assistance or inquiries, please don't hesitate to
          reach out to our dedicated support team at
          <a
            href="mailto:<EMAIL>"
            style="color: #499fb6; text-decoration: none"
            ><EMAIL></a
          >
          or visit our
          <a
            href="https://flable.ai/contact.html"
            target="_blank"
            style="color: #499fb6; text-decoration: none"
            >Help Center</a
          >
        </p>
      </main>

      <footer
        style="
          width: 100%;
          max-width: 490px;
          margin: 20px auto 0;
          text-align: center;
          border-top: 1px solid #e6ebf1;
        "
      >
        <p
          style="
            margin: 0;
            margin-top: 40px;
            font-size: 16px;
            font-weight: 600;
            color: #434343;
          "
        >
          Flable.ai
        </p>
        <div style="margin: 0; margin-top: 16px">
          <a
            href="https://www.linkedin.com/company/flable/"
            target="_blank"
            style="display: inline-block"
          >
            <img
              width="28px"
              alt="LinkedIn"
              src="https://cdn2.iconfinder.com/data/icons/social-media-2285/512/1_Linkedin_unofficial_colored_svg-256.png"
            />
          </a>

          <a
            href="https://twitter.com/Flableai"
            target="_blank"
            style="display: inline-block; margin-left: 8px"
          >
            <img
              width="28px"
              alt="Twitter"
              src="https://cdn2.iconfinder.com/data/icons/threads-by-instagram/24/x-logo-twitter-new-brand-64.png"
            />
          </a>
          <a
            href="https://www.youtube.com/channel/UCfTiob-n1Lzjd9d1G4fDiLQ"
            target="_blank"
            style="display: inline-block; margin-left: 8px"
          >
            <img
              width="28px"
              alt="Youtube"
              src="https://cdn2.iconfinder.com/data/icons/social-media-2285/512/1_Youtube_colored_svg-256.png"
          /></a>
        </div>
        <p style="margin: 0; margin-top: 16px; color: #434343">
          Copyright © 2025 Keinsa Private Limited
        </p>
        <table
          style="display: inline-block; vertical-align: middle"
          cellspacing="3px"
          cellpadding="3px"
        >
          <tr>
            <td style="text-align: center">
              <a href="https://flable.ai/privacy-policy.html" target="_blank"
                >Privacy policy</a
              >
            </td>
            <td style="text-align: center">
              <a href="https://flable.ai/Terms-sevice.html" target="_blank"
                >Terms of service</a
              >
            </td>
          </tr>
        </table>
      </footer>
    </div>
  </body>
</html>
 `;
};

export const returnOTPVerificationEmailHTML = (
   full_name: string,
   otp: string,
) => {
   return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8" />
                <meta name="viewport" content="width=device-width, initial-scale=1.0" />
                <meta http-equiv="X-UA-Compatible" content="ie=edge" />
                <title>OTP Verification for Flable</title>
            
                <link
                href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap"
                rel="stylesheet"
                />
            </head>
            <body
                style="
                margin: 0;
                font-family: 'Poppins', sans-serif;
                background: #ffffff;
                font-size: 14px;
                "
            >
                <div
                style="
                    max-width: 680px;
                    margin: 0 auto;
                    padding: 45px 30px 60px;
                    background: #f4f7ff;
                    background-image: url(https://archisketch-resources.s3.ap-northeast-2.amazonaws.com/vrstyler/1661497957196_595865/email-template-background-banner);
                    background-repeat: no-repeat;
                    background-size: 800px 452px;
                    background-position: top center;
                    font-size: 14px;
                    color: #434343;
                "
                >
                <header>
                    <table style="width: 100%">
                    <tbody>
                        <tr style="height: 0">
                        <td>
                            <img
                            alt=""
                            src="https://flable.ai/assets/images/logo.png"
                            height="40px"
                            />
                        </td>
                        <td style="text-align: right">
                            <span style="font-size: 14px; line-height: 30px; color: #ffffff"
                            >${new Date().toDateString()}</span
                            >
                        </td>
                        </tr>
                    </tbody>
                    </table>
                </header>
            
                <main>
                    <div
                    style="
                        margin: 0;
                        margin-top: 70px;
                        padding: 92px 30px 115px;
                        background: #ffffff;
                        border-radius: 30px;
                        text-align: center;
                    "
                    >
                    <div style="width: 100%; max-width: 489px; margin: 0 auto">
                        <h1
                        style="
                            margin: 0;
                            font-size: 24px;
                            font-weight: 500;
                            color: #1f1f1f;
                        "
                        >
                        Your OTP
                        </h1>
                        <p
                        style="
                            margin: 0;
                            margin-top: 17px;
                            font-size: 16px;
                            font-weight: 500;
                        "
                        >
                        Dear ${full_name},
                        </p>
                        <p
                        style="
                            margin: 0;
                            margin-top: 17px;
                            font-weight: 500;
                            letter-spacing: 0.56px;
                            text-align: left;
                        "
                        >
                        You are receiving this mail from ${Config.FRONTEND_DOMAIN?.split(',')[0]}
                        </p>
                        <p
                        style="
                            margin: 0;
                            margin-top: 17px;
                            font-weight: 500;
                            letter-spacing: 0.56px;
                        "
                        >
                        Thank you for choosing flable.ai. Use the following OTP to
                        complete the procedure to verify your email address. OTP is valid
                        for
                        <span style="font-weight: 600; color: #1f1f1f">10 minutes</span>.
                        Please do not share it with anyone.
                        </p>
                        <p
                        style="
                            margin: 0;
                            margin-top: 40px;
                            font-size: 40px;
                            font-weight: 600;
                            letter-spacing: 25px;
                            color: #ba3d4f;
                        "
                        >
                        ${otp}
                        </p>
                        <p
                        style="
                            margin: 0;
                            margin-top: 17px;
                            font-weight: 500;
                            letter-spacing: 0.56px;
                        "
                        >
                        If you did not request this, please ignore this email or contact
                        our support team immediately.
                        </p>
                        <p
                        style="
                            margin: 0;
                            margin-top: 17px;
                            font-weight: 500;
                            letter-spacing: 0.56px;
                        "
                        >
                        Best Regards,
                        </p>
                        <p
                        style="
                            margin: 0;
                            margin-top: 7px;
                            font-weight: 700;
                            letter-spacing: 0.56px;
                        "
                        >
                        The Flable team
                        </p>
                    </div>
                    </div>
            
                    <p
                    style="
                        max-width: 400px;
                        margin: 0 auto;
                        margin-top: 90px;
                        text-align: center;
                        font-weight: 500;
                        color: #8c8c8c;
                    "
                    >
                    For any further assistance or inquiries, please don't hesitate to
                    reach out to our dedicated support team at
                    <a
                        href="mailto:<EMAIL>"
                        style="color: #499fb6; text-decoration: none"
                        ><EMAIL></a
                    >
                    or visit our
                    <a
                        href="https://flable.ai/contact.html"
                        target="_blank"
                        style="color: #499fb6; text-decoration: none"
                        >Help Center</a
                    >
                    </p>
                </main>
            
                <footer
                    style="
                    width: 100%;
                    max-width: 490px;
                    margin: 20px auto 0;
                    text-align: center;
                    border-top: 1px solid #e6ebf1;
                    "
                >
                    <p
                    style="
                        margin: 0;
                        margin-top: 40px;
                        font-size: 16px;
                        font-weight: 600;
                        color: #434343;
                    "
                    >
                    Flable.ai
                    </p>
                    <div style="margin: 0; margin-top: 16px">
                    <a
                        href="https://www.linkedin.com/company/flable/"
                        target="_blank"
                        style="display: inline-block"
                    >
                        <img
                        width="28px"
                        alt="LinkedIn"
                        src="https://cdn2.iconfinder.com/data/icons/social-media-2285/512/1_Linkedin_unofficial_colored_svg-256.png"
                        />
                    </a>
            
                    <a
                        href="https://twitter.com/Flableai"
                        target="_blank"
                        style="display: inline-block; margin-left: 8px"
                    >
                        <img
                        width="28px"
                        alt="Twitter"
                        src="https://cdn2.iconfinder.com/data/icons/threads-by-instagram/24/x-logo-twitter-new-brand-64.png"
                        />
                    </a>
                    <a
                        href="https://www.youtube.com/channel/UCfTiob-n1Lzjd9d1G4fDiLQ"
                        target="_blank"
                        style="display: inline-block; margin-left: 8px"
                    >
                        <img
                        width="28px"
                        alt="Youtube"
                        src="https://cdn2.iconfinder.com/data/icons/social-media-2285/512/1_Youtube_colored_svg-256.png"
                    /></a>
                    </div>
                    <p style="margin: 0; margin-top: 16px; color: #434343">
                    Copyright © 2025 Keinsa Private Limited
                    </p>
                    <table
                    style="display: inline-block; vertical-align: middle"
                    cellspacing="3px"
                    cellpadding="3px"
                    >
                    <tr>
                        <td style="text-align: center">
                        <a href="https://flable.ai/privacy-policy.html" target="_blank"
                            >Privacy policy</a
                        >
                        </td>
                        <td style="text-align: center">
                        <a href="https://flable.ai/Terms-sevice.html" target="_blank"
                            >Terms of service</a
                        >
                        </td>
                    </tr>
                    </table>
                </footer>
                </div>
            </body>
            </html>`;
};

export const returnAlertEmail = (
   recipient: string,
   alert_name: string,
   trend: string,
   value: string,
   value_type: string,
   comparison: string,
   comparison_type: string,
   channel: string,
   recipients: string,
   descriptionText: string,
   questionText: string,
   user_id: string,
   alert_id: string,
   client_id: string,
) => {
   return `
    <!DOCTYPE html>
      <html lang="en">
      <head>
      <meta charset="UTF-8" />
      <title>Flable AI Alert</title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body style="margin:0;padding:0;background:#f5f7fa;font-family:-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,Helvetica,Arial,sans-serif;color:#32325d">
      <table role="presentation" width="100%" cellpadding="0" cellspacing="0" style="background:#f5f7fa;padding:40px 0">
      <tr>
      <td align="center">
      <table role="presentation" width="600" cellpadding="0" cellspacing="0" style="background:#ffffff;border-radius:8px;overflow:hidden;box-shadow:0 4px 12px rgba(50,50,93,.1)">
      <tr>
      <td style="background:#3546d4;padding:24px;text-align:center;color:#fff;font-size:22px;font-weight:600">🚨 Flable AI Alert</td>
      </tr>
      <tr>
      <td style="padding:32px 40px 16px 40px;font-size:16px">
                    Hello <strong style="color:#3546d4">${recipient}</strong>,
      </td>
      </tr>
      <tr>
      <td style="padding:0 40px 24px 40px;font-size:16px;line-height:1.6">
                    Your <strong>Alerting Agent</strong> just spotted something you’ll want to jump on:<br><br>
      <strong>${descriptionText}</strong>
      </td>
      </tr>
      <tr>
      <td style="padding:0 40px">
      <table role="presentation" width="100%" cellpadding="0" cellspacing="0" style="background:#f0f4ff;border-radius:6px;font-size:14px;line-height:1.5">
      <tr><td style="padding:16px 20px">
      <strong>📊 Data source:</strong> ${channel}<br>
      <strong>📅 Reference period:</strong> ${
         comparison_type === 'time'
            ? `${comparison}`
            : `compared with ${comparison}`
      }<br>
      <strong>💥 Threshold hit:</strong> ${value_type === 'percentage' ? `${trend} by ${value}%` : `${trend} ${value}`}<br>
      <strong>⏰ Triggered:</strong> ${new Date()
         .toISOString()
         .split('T')
         .map((item, index) => (index === 0 ? item : item.split('.')[0]))
         .join(' ')}<br>
      </td></tr>
      </table>
      </td>
      </tr>
      <tr>
      <td align="center" style="padding:32px 40px 16px 40px">
      <a href="${Config.FRONTEND_DOMAIN?.split(',')[0]}/marco/analytics-agent?query=${questionText}" style="background:#3546d4;color:#ffffff;text-decoration:none;padding:12px 24px;border-radius:4px;font-size:15px;font-weight:600;display:inline-block;margin-left:8px">Analyze Cause</a>
      <a href="${Config.FRONTEND_DOMAIN?.split(',')[0]}/dashboard" style="background:#3546d4;color:#ffffff;text-decoration:none;padding:12px 24px;border-radius:4px;font-size:15px;font-weight:600;display:inline-block;margin-left:8px">Go to Dashboard</a>
      </td>
      </tr>
      <tr>
      <td style="padding:0 40px 24px 40px;font-weight:bold;font-size:14px;line-height:1.6;color:#555">
                  Note: This alert is based on yesterday's data.
      </td>
      </tr>
      <tr>
      <td style="padding:0 40px 24px 40px;font-size:14px;line-height:1.6;color:#555">
                    “Data tells you <em>what</em>; insights tell you <em>why</em>. Let’s uncover the why.”
      </td>
      </tr>
      <tr>
      <td style="padding:0 40px 40px 40px;font-size:15px">
                    — Your Flable AI Team
      </td>
      </tr>
      <tr>
      <td style="background:#f5f7fa;padding:24px 40px;text-align:center;font-size:12px;color:#8898aa">
                    You’re receiving this because you enabled alerts in <strong>Flable AI</strong>.<br>
                    © ${new Date().getFullYear()} Flable AI. All rights reserved.
      </td>
      </tr>
      
              </table>
      
            </td>
      </tr>
      </table>
      </body>
      </html>
      `;
};

export const retrunLeadsEmail = (
   name: string,
   country: string,
   email: string,
) => {
   return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>New Lead Notification</title>
    </head>
    <body style="margin:0; padding:0; font-family: Arial, sans-serif; background-color: #f4f4f4;">
      <table align="center" width="600" cellpadding="0" cellspacing="0" style="background-color: #ffffff; border: 1px solid #dddddd; border-radius: 8px;">
        <tr>
          <td style="padding: 20px; text-align: center; background-color: #3444AE; color: #ffffff; border-top-left-radius: 8px; border-top-right-radius: 8px;">
            <img src="https://flable.ai/assets/images/logo.png" alt="Flable AI Logo" style="height: 40px; display: block; margin: 0 auto 10px;" />
            <h1 style="margin: 0; font-size: 24px;">New Lead Sign Up</h1>
          </td>
        </tr>
        <tr>
          <td style="padding: 20px; text-align: left; color: #333333;">
            <p>Hello Team,</p>
            <p>You have received a new lead from the website. Here are the details:</p>
            <table width="100%" cellpadding="0" cellspacing="0" style="border-collapse: collapse; margin-top: 10px;">
              <tr>
                <td style="padding: 8px; border: 1px solid #dddddd;"><strong>Name:</strong></td>
                <td style="padding: 8px; border: 1px solid #dddddd;">${name}</td>
              </tr>
              <tr>
                <td style="padding: 8px; border: 1px solid #dddddd;"><strong>Country:</strong></td>
                <td style="padding: 8px; border: 1px solid #dddddd;">${country}</td>
              </tr>
              <tr>
                <td style="padding: 8px; border: 1px solid #dddddd;"><strong>Email:</strong></td>
                <td style="padding: 8px; border: 1px solid #dddddd;"><a href="mailto:${email}">${email}</a></td>
              </tr>
            </table>
            <p style="margin-top: 20px;">Please follow up with this lead as soon as possible.</p>
            <a href="${Config.FRONTEND_DOMAIN?.split(',')[0]}" style="display: inline-block; background-color: #437EEB; color: #ffffff; padding: 12px 20px; text-decoration: none; border-radius: 5px; margin-top: 20px;">View in Dashboard</a>
          </td>
        </tr>
        <tr>
          <td style="padding: 15px; text-align: center; font-size: 12px; color: #888888;">
            &copy; 2025 Flable AI. All rights reserved.
          </td>
        </tr>
      </table>
    </body>
    </html>
    `;
};
