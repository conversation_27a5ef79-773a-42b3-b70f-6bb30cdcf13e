import express from 'express';
import asyncHandler from '../../../midddleware/async-handler';

import { LinkedinService } from '../../../services/LinkedinService';
import { logger } from '../../../config/logger';
import { LinkedInController } from '../../../controllers/LinkedinController';
import multer from 'multer';

const router = express.Router();

const linkedinService = new LinkedinService(logger);
const linkedinController = new LinkedInController(linkedinService, logger);

const upload = multer({
   storage: multer.memoryStorage(),
   limits: { fileSize: 50 * 1024 * 1024 },
});

router.post(
   '/user-details',
   asyncHandler(
      linkedinController.handleFetchUserDetails.bind(linkedinController),
   ),
);

router.post(
   '/post-content',
   asyncHandler(linkedinController.handlePost.bind(linkedinController)),
);
router.post(
   '/pages',
   async<PERSON>andler(linkedinController.getUserPages.bind(linkedinController)),
);

router.post(
   '/upload',
   upload.single('media'),
   asyncHandler(linkedinController.handleImageUpload.bind(linkedinController)),
);

export { router as linkedinRouter };
