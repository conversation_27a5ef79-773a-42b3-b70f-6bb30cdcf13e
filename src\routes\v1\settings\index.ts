import express from 'express';
import asyncHandler from '../../../midddleware/async-handler';

import { SettingsService } from '../../../services/SettingsService';
import { logger } from '../../../config/logger';
import { SettingsController } from '../../../controllers/SettingsController';
import { AppDataSource } from '../../../config/data-source';
import { EmailService } from '../../../services/EmailService';
import { KPIService } from '../../../services/KPIService';
import { AuthService } from '../../../services/AuthService';
import { OnboardingService } from '../../../services/OnboardingService';
import redisService from '../../../module/global/redis';

const router = express.Router();

const entityManager = AppDataSource.manager;
const emailService = new EmailService();
const authService = new AuthService(logger, entityManager, emailService);
const onboardingService = new OnboardingService(
   logger,
   entityManager,
   emailService,
   authService,
);
const kpiService = new KPIService(
   entityManager,
   logger,
   onboardingService,
   redisService,
);
const settingsService = new SettingsService(
   entityManager,
   logger,
   emailService,
   kpiService,
   onboardingService,
   redisService,
);
const settingsController = new SettingsController(settingsService, logger);

router.get(
   '/general_settings',
   asyncHandler(
      settingsController.handleFetchGeneralSettings.bind(settingsController),
   ),
);

router.put(
   '/language_timezone',
   asyncHandler(
      settingsController.handleUpdateLanguageTimezone.bind(settingsController),
   ),
);

router.put(
   '/account_details',
   asyncHandler(
      settingsController.handleUpdateAccountDetails.bind(settingsController),
   ),
);

router.put(
   '/profile_image',
   asyncHandler(
      settingsController.handleUpdateProfileImage.bind(settingsController),
   ),
);

router.get(
   '/language',
   asyncHandler(
      settingsController.handleFetchLanguage.bind(settingsController),
   ),
);

router.get(
   '/timezone',
   asyncHandler(
      settingsController.handleFetchTimezone.bind(settingsController),
   ),
);

router.get(
   '/industry_options',
   asyncHandler(
      settingsController.handleFetchIndustryOptions.bind(settingsController),
   ),
);

router.get(
   '/industry',
   asyncHandler(
      settingsController.handleFetchIndustry.bind(settingsController),
   ),
);

router.get(
   '/verify',
   asyncHandler(settingsController.verify.bind(settingsController)),
);

router.post(
   '/language/update',
   asyncHandler(
      settingsController.handleUpdateLanguage.bind(settingsController),
   ),
);

router.post(
   '/timezone/update',
   asyncHandler(
      settingsController.handleUpdateTimezone.bind(settingsController),
   ),
);
router.post(
   '/reports/send',
   asyncHandler(settingsController.handleReportsMail.bind(settingsController)),
);
router.post(
   '/reports',
   asyncHandler(settingsController.createReport.bind(settingsController)),
);
router.put(
   '/reports',
   asyncHandler(settingsController.updateReport.bind(settingsController)),
);
router.put(
   '/reports/:clientId/:is_subscribed/:reportId',
   asyncHandler(settingsController.pauseAutoReport.bind(settingsController)),
);
router.delete(
   '/reports/:clientId/:reportId',
   asyncHandler(settingsController.deleteReport.bind(settingsController)),
);
router.get(
   '/reports/:clientId/:userId',
   asyncHandler(settingsController.getReports.bind(settingsController)),
);
router.get(
   '/reports/meta/auto/:clientId',
   asyncHandler(
      settingsController.getMetaDataForAutoReport.bind(settingsController),
   ),
);
router.post(
   '/reports/fetchfromXi',
   asyncHandler(
      settingsController.getWeeklyAutoReportFromXi.bind(settingsController),
   ),
);
router.post(
   '/industry/update',
   asyncHandler(
      settingsController.handleUpdateIndustry.bind(settingsController),
   ),
);

router
   .route('/competitors')
   .get(
      asyncHandler(
         settingsController.handleFetchCompetitor.bind(settingsController),
      ),
   )
   .post(
      asyncHandler(
         settingsController.handleCompetitorPost.bind(settingsController),
      ),
   );

// Meta Ads Manager Credentials Routes
router.post(
   '/meta-ads-credentials',
   asyncHandler(
      settingsController.handleSaveMetaAdsCredentials.bind(settingsController),
   ),
);

router.get(
   '/meta-ads-credentials/:client_id',
   asyncHandler(
      settingsController.handleGetMetaAdsCredentials.bind(settingsController),
   ),
);

router
   .route('/:client_id/:user_id/feature-usage')
   .get(
      asyncHandler(
         settingsController.fetchFeatureUsage.bind(settingsController),
      ),
   )
   .post(
      asyncHandler(
         settingsController.trackFeatureUsage.bind(settingsController),
      ),
   );

export { router as settingsRouter };
