import { EntityManager } from 'typeorm';
import * as types from '../types';
import axios from 'axios';
import { Config } from '../config/index';
import {
   GOOGLE_KPI_CALCULATE,
   kpiFormulas,
   KeywordKpiFormula,
   KPI_CALCULATION_DAYWISE,
} from '../utils/googleAdsKpisCalculator';
import { logger } from '../config/logger';
import { DAYS_IN } from '../constants/index';

export class GoogleAdsService {
   constructor(private entityManager: EntityManager) {}

   async getChannelType(
      client_id: string,
   ): Promise<{ fn_get_client_GoogleAdsObjectives: string[] | null }[]> {
      const query = `
        SELECT DISTINCT channel_type 
        FROM ${Config.DB_Postgres_SCHEMA}.m_campaign_table_googleads 
        WHERE channel_type IS NOT NULL AND client_id = $1
        `;

      const result: { channel_type: string }[] = await this.entityManager.query(
         query,
         [client_id],
      );

      const channelTypes: string[] = result?.map(
         (row: { channel_type: string }) => row.channel_type,
      );

      return [
         {
            fn_get_client_GoogleAdsObjectives: channelTypes,
         },
      ];
   }

   async getKpisNames(
      client_id: string,
      channel_type: string,
   ): Promise<{ fn_get_client_GoogleAdsKpis: string[] | null }[]> {
      const query = `
        SELECT DISTINCT kpi_name 
        FROM ${Config.DB_Postgres_SCHEMA}.m_campaign_table_googleads
        WHERE 
            client_id = $1 
            AND channel_type = $2
        `;
      const result: { kpi_name: string }[] = await this.entityManager.query(
         query,
         [client_id, channel_type],
      );

      const kpiNames: string[] = result?.map(
         (row: { kpi_name: string }) => row.kpi_name,
      );

      return [
         {
            fn_get_client_GoogleAdsKpis: kpiNames,
         },
      ];
   }

   async getCampaignsDaywise(
      client_id: string,
      channel_type: string,
      kpis: string[],
      start_date: string,
      end_date: string,
      prev_start_date: string,
      prev_end_date: string,
   ): Promise<types.GoogleCampaignDaywiseDataResponse[]> {
      const trackedKpisQuery = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.performance_insights_user_track WHERE client_id = $1 AND tracked = true`;
      const trackedKpis: types.TrackedKPIs[] = await this.entityManager.query(
         trackedKpisQuery,
         [client_id],
      );

      const query = `
        SELECT *
        FROM ${Config.DB_Postgres_SCHEMA}.fn_googleads_campaign_daywise_with_multi_kpi_value_get($1, $2, $3, $4, $5)
        `;
      const currentPeriod: types.GoogleCampaignDaywiseDataResponse[] =
         await this.entityManager.query(query, [
            client_id,
            channel_type,
            kpis,
            start_date,
            end_date,
         ]);

      if (
         currentPeriod[0].fn_googleads_campaign_daywise_with_multi_kpi_value_get
      ) {
         for (const campaign of currentPeriod[0]
            .fn_googleads_campaign_daywise_with_multi_kpi_value_get) {
            const {
               campaign_status: status,
               currency,
               budget,
            } = campaign.kpis?.[campaign.kpis.length - 1] || {};
            Object.assign(campaign, {
               campaign_status: status,
               currency: currency,
               budget: budget,
            });
         }
      }

      const previousPeriod: types.GoogleCampaignDaywiseDataResponse[] =
         await this.entityManager.query(query, [
            client_id,
            channel_type,
            kpis,
            prev_start_date,
            prev_end_date,
         ]);

      const kpiAggregate: types.GoogleKpiAggregate = kpis.reduce(
         (acc: types.GoogleKpiAggregate, kpi: string) => {
            acc[kpi] = 0;
            return acc;
         },
         {},
      );

      if (
         previousPeriod[0]
            .fn_googleads_campaign_daywise_with_multi_kpi_value_get
      ) {
         for (const campaign of previousPeriod[0]
            .fn_googleads_campaign_daywise_with_multi_kpi_value_get) {
            const result: types.GoogleKpiAggregate = { ...kpiAggregate };
            campaign.prev_total_val =
               campaign.kpis && campaign.kpis.length > 0
                  ? this.calculateKpi(campaign.kpis, result)
                  : null;
         }
      }

      const previousPeriodLookup: Map<number, types.GoogleCampaignData> =
         previousPeriod?.[0]
            ?.fn_googleads_campaign_daywise_with_multi_kpi_value_get
            ? new Map<number, types.GoogleCampaignData>(
                 previousPeriod?.[0]?.fn_googleads_campaign_daywise_with_multi_kpi_value_get?.map(
                    (c) => [c.campaign_id, c],
                 ),
              )
            : new Map<number, types.GoogleCampaignData>();

      if (
         currentPeriod[0].fn_googleads_campaign_daywise_with_multi_kpi_value_get
      ) {
         for (const campaign of currentPeriod[0]
            .fn_googleads_campaign_daywise_with_multi_kpi_value_get) {
            const result: types.GoogleKpiAggregate = { ...kpiAggregate };
            campaign.total_val =
               campaign.kpis && campaign.kpis.length > 0
                  ? this.calculateKpi(campaign.kpis, result)
                  : null;

            const prev_campaign: types.GoogleCampaignData | undefined =
               previousPeriodLookup?.get(campaign?.campaign_id);

            campaign.prev_total_val = prev_campaign
               ? prev_campaign?.prev_total_val
               : null;

            campaign.day_wise_kpis =
               campaign.kpis && campaign.kpis.length > 0
                  ? this.CalculateDaywise(campaign.kpis)
                  : null;

            campaign.trackedKPIs = [...trackedKpis];
         }
      }

      return currentPeriod;
   }

   private CalculateDaywise(
      campaignKPIs: types.GoogleKPIData[],
   ): types.daywiseKPIs {
      if (!campaignKPIs?.length) return {};

      const aggreg = new Map<string, Record<string, number>>();

      for (const { kpi_name, kpi_date, kpi_value } of campaignKPIs) {
         if (!aggreg.has(kpi_name)) {
            aggreg.set(kpi_name, {});
         }
         aggreg.get(kpi_name)![kpi_date] =
            (aggreg.get(kpi_name)![kpi_date] || 0) + Number(kpi_value);
      }

      const daywiseAgg = new Map<string, Record<string, number>>();

      aggreg.forEach((dateData, kpi) => {
         for (const [date, value] of Object.entries(dateData)) {
            const dayName = new Date(date).toLocaleString('en-IN', {
               weekday: 'long',
            });

            if (!daywiseAgg.has(kpi)) {
               daywiseAgg.set(kpi, {});
            }
            daywiseAgg.get(kpi)![dayName] =
               (daywiseAgg.get(kpi)![dayName] || 0) + value;
         }
      });

      daywiseAgg.forEach((kpiValues, kpiName) => {
         if (kpiName in KPI_CALCULATION_DAYWISE) {
            for (const day in kpiValues) {
               kpiValues[day] = KPI_CALCULATION_DAYWISE[
                  kpiName as keyof typeof KPI_CALCULATION_DAYWISE
               ](day, daywiseAgg);
            }
         }
      });
      return Object.fromEntries(daywiseAgg);
   }

   async getCampaignsKpiwise(
      client_id: string,
      campaign_id: number,
      channel_type: string,
      kpis: string[],
      start_date: string,
      end_date: string,
   ): Promise<types.GoogleAdsCampaignKpiwiseDataResponse[]> {
      const query = `
        SELECT *
        FROM ${Config.DB_Postgres_SCHEMA}.fn_googleads_campaign_kpi_wise_data_get($1, $2, $3, $4, $5)
        `;
      const currentData: types.GoogleAdsCampaignKpiwiseDataResponse[] =
         await this.entityManager.query(query, [
            client_id,
            campaign_id,
            channel_type,
            start_date,
            end_date,
         ]);

      const kpiAggregate: types.GoogleKpiAggregate = kpis.reduce(
         (acc: types.GoogleKpiAggregate, kpi: string) => {
            acc[kpi] = 0;
            return acc;
         },
         {},
      );

      if (currentData[0].fn_googleads_campaign_kpi_wise_data_get) {
         for (const campaign of currentData[0]
            .fn_googleads_campaign_kpi_wise_data_get) {
            if (campaign.kpis && campaign.kpis.length > 0) {
               campaign.kpi_wise_data = this.getKpiwiseData(campaign.kpis, {});

               const result: types.GoogleKpiAggregate = { ...kpiAggregate };

               campaign.total_val =
                  campaign.kpis && campaign.kpis.length > 0
                     ? this.calculateKpi(campaign.kpis, result)
                     : null;
            }
         }
      }
      return currentData;
   }

   async getAdgroups(
      client_id: string,
      campaign_id: number,
      channel_type: string,
      kpis: string[],
      start_date: string,
      end_date: string,
      prev_start_date: string,
      prev_end_date: string,
   ): Promise<types.GoogleAdgroupsDataResponse[]> {
      const query = `
        SELECT * 
        FROM ${Config.DB_Postgres_SCHEMA}.fn_googleads_adgroup_with_kpi_get($1, $2, $3, $4, $5, $6)
    `;
      const currentPeriod: types.GoogleAdgroupsDataResponse[] =
         await this.entityManager.query(query, [
            client_id,
            campaign_id,
            channel_type,
            kpis,
            start_date,
            end_date,
         ]);

      const previousPeriod: types.GoogleAdgroupsDataResponse[] =
         await this.entityManager.query(query, [
            client_id,
            campaign_id,
            channel_type,
            kpis,
            prev_start_date,
            prev_end_date,
         ]);

      const kpiAggregate: types.GoogleKpiAggregate = kpis.reduce(
         (acc: types.GoogleKpiAggregate, kpi: string) => {
            acc[kpi] = 0;
            return acc;
         },
         {},
      );

      if (previousPeriod[0]?.fn_googleads_adgroup_with_kpi_get) {
         for (const adgroup of previousPeriod[0]
            .fn_googleads_adgroup_with_kpi_get) {
            const result = { ...kpiAggregate };
            adgroup.prev_total_val = adgroup.kpi_values?.length
               ? this.calculateKpi(adgroup.kpi_values, result)
               : null;

            adgroup.device = this.calculateKeywordsAggregate(
               adgroup.kpi_values,
               'device',
            );
            adgroup.hour = this.calculateKeywordsAggregate(
               adgroup.kpi_values,
               'hour',
            );
            adgroup.day_of_week = this.calculateKeywordsAggregate(
               adgroup.kpi_values,
               'day_of_week',
            );
         }
      }

      const previousPeriodLookup: Map<number, types.GoogleAdgroupsData> =
         previousPeriod[0]?.fn_googleads_adgroup_with_kpi_get
            ? new Map<number, types.GoogleAdgroupsData>(
                 previousPeriod[0].fn_googleads_adgroup_with_kpi_get?.map(
                    (c) => [c.ad_group_id, c],
                 ),
              )
            : new Map<number, types.GoogleAdgroupsData>();

      if (currentPeriod[0]?.fn_googleads_adgroup_with_kpi_get) {
         for (const adgroup of currentPeriod[0]
            .fn_googleads_adgroup_with_kpi_get) {
            const result = { ...kpiAggregate };
            const total_val = adgroup.kpi_values?.length
               ? this.calculateKpi(adgroup.kpi_values, result)
               : null;

            const prev_campaign: types.GoogleAdgroupsData | undefined =
               previousPeriodLookup?.get(adgroup?.ad_group_id);

            adgroup.kpis = kpis.map((kpi) => ({
               kpi,
               kpi_current: total_val?.[kpi] ?? null,
               kpi_previous: prev_campaign?.prev_total_val?.[kpi] ?? null,
            }));

            const processDimension = (dimension: types.DimensionKeys) => {
               const current = this.calculateKeywordsAggregate(
                  adgroup.kpi_values,
                  dimension,
               );
               const previous = prev_campaign?.[dimension] || {};
               return Object.keys(current).reduce((acc, kpi) => {
                  acc[kpi] = current[kpi].map((currEntry) => {
                     const prevEntry = (previous[kpi] || []).find(
                        (p: {
                           keyword_name: string;
                           aggregate_value: number;
                        }) => p.keyword_name === currEntry.keyword_name,
                     );

                     return {
                        keyword_name: currEntry.keyword_name,
                        current_value: currEntry.aggregate_value ?? null,
                        previous_value: prevEntry?.aggregate_value ?? null,
                     };
                  });
                  return acc;
               }, {} as types.DimensionsAggregate);
            };

            adgroup.device_wise = processDimension('device');
            adgroup.hour_wise = processDimension('hour');
            adgroup.day_of_week_wise = processDimension('day_of_week');
         }
      }

      return currentPeriod;
   }

   async updateUserTrack(
      client_id: string,
      objective: string,
      kpi_name: string,
      campaign_id: string,
      tracked: boolean,
      channel: string,
   ): Promise<string | null> {
      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_update_performance_insights_user_track($1, $2, $3, $4, $5,$6)`;
      const result: [string] = await this.entityManager.query(query, [
         client_id,
         objective,
         kpi_name,
         campaign_id,
         tracked,
         channel,
      ]);

      return result[0] || null;
   }

   async getAds(
      client_id: string,
      campaign_id: number,
      ad_group_id: number,
      channel_type: string,
      kpis: string[],
      start_date: string,
      end_date: string,
      prev_start_date: string,
      prev_end_date: string,
   ): Promise<types.GoogleAdsDataResponse[]> {
      const query = `
        SELECT * 
        FROM ${Config.DB_Postgres_SCHEMA}.fn_googleads_ads_with_kpi_get($1, $2, $3, $4, $5, $6,$7)
    `;
      const currentPeriod: types.GoogleAdsDataResponse[] =
         await this.entityManager.query(query, [
            client_id,
            campaign_id,
            ad_group_id,
            channel_type,
            kpis,
            start_date,
            end_date,
         ]);

      const previousPeriod: types.GoogleAdsDataResponse[] =
         await this.entityManager.query(query, [
            client_id,
            campaign_id,
            ad_group_id,
            channel_type,
            kpis,
            prev_start_date,
            prev_end_date,
         ]);

      const kpiAggregate: types.GoogleKpiAggregate = kpis.reduce(
         (acc: types.GoogleKpiAggregate, kpi: string) => {
            acc[kpi] = 0;
            return acc;
         },
         {},
      );

      if (previousPeriod[0]?.fn_googleads_ads_with_kpi_get) {
         for (const ad of previousPeriod[0].fn_googleads_ads_with_kpi_get) {
            const result = { ...kpiAggregate };
            ad.prev_total_val = ad.kpi_values?.length
               ? this.calculateKpi(ad.kpi_values, result)
               : null;
         }
      }

      const previousPeriodLookup: Map<number, types.GoogleAdsData> =
         previousPeriod[0]?.fn_googleads_ads_with_kpi_get
            ? new Map<number, types.GoogleAdsData>(
                 previousPeriod[0].fn_googleads_ads_with_kpi_get.map((c) => [
                    c.ad_id,
                    c,
                 ]),
              )
            : new Map<number, types.GoogleAdsData>();

      if (currentPeriod[0]?.fn_googleads_ads_with_kpi_get) {
         for (const ad of currentPeriod[0].fn_googleads_ads_with_kpi_get) {
            const result = { ...kpiAggregate };
            const total_val = ad.kpi_values?.length
               ? this.calculateKpi(ad.kpi_values, result)
               : null;

            const prev_campaign: types.GoogleAdsData | undefined =
               previousPeriodLookup?.get(ad.ad_id);
            ad.kpis = kpis.map((kpi) => ({
               kpi,
               kpi_current: total_val?.[kpi] ?? null,
               kpi_previous: prev_campaign?.prev_total_val?.[kpi] ?? null,
            }));
         }
      }

      return currentPeriod;
   }

   async getCampaignChartSummary(payload: types.GetCampaignSummary) {
      return await this.callCasGPT<Response | null>(
         'get_googleads_chart_recommendations',
         payload,
      );
   }
   async getCampaignAdgroupSummary(payload: types.GetCampaignSummary) {
      return await this.callCasGPT<Response | null>(
         'get_googleads_adgroup_recommendations',
         payload,
      );
   }
   async getAdgroupKeywordSSummary(payload: types.GetCampaignSummary) {
      return await this.callCasGPT<Response | null>(
         'get_googleads_keyword_recommendations',
         payload,
      );
   }
   async getAdgroupGraphSummary(payload: types.GetCampaignSummary) {
      return await this.callCasGPT<Response | null>(
         'get_googleads_graph_recommendations',
         payload,
      );
   }

   async getAdgroupSummary(payload: types.GetCampaignSummary) {
      return await this.callCasGPT<Response | null>(
         'get_googleads_ads_recommendations',
         payload,
      );
   }

   async callCasGPT<T>(
      endpoint: string,
      payload: types.GetAdsetSummary | types.GetCampaignSummary,
   ): Promise<T | null> {
      try {
         const response = await axios.post<T>(
            `${Config.GPT_URL}/${endpoint}`,
            payload,
            {
               headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Bearer ${Config.GPT_BEARER}`,
               },
            },
         );

         if (!response) {
            throw new Error('No Data found');
         }

         return response.data;
      } catch (error) {
         return null;
      }
   }

   async getKeywords(
      client_id: string,
      campaign_id: number,
      ad_group_id: number,
      kpis: string[],
      start_date: string,
      end_date: string,
   ): Promise<types.KeywordGoogleAdsData> {
      const query1 = `
WITH base_data AS (
  SELECT
    keyword_match_type,
    keyword_text,
    search_term_match_type,
    search_term,
    SUM(spend) AS spend,
    SUM(clicks) AS clicks,
    SUM(impressions) AS impressions,
    SUM(conversions) AS conversions,
    CASE 
      WHEN SUM(impressions) > 0 THEN (SUM(clicks)::float / SUM(impressions)) * 100
      ELSE NULL
    END AS ctr,
    CASE 
      WHEN SUM(conversions) > 0 THEN SUM(spend) / SUM(conversions)
      ELSE NULL
    END AS cpa,
    CASE 
      WHEN SUM(clicks) > 0 THEN SUM(spend) / SUM(clicks)
      ELSE NULL
    END AS cpc
  FROM ${Config.DB_Postgres_SCHEMA}.${Config.DB_KEYWORD_TABLE_NAME}
  WHERE client_id = $1
    AND campaign_id = $2
    AND ad_group_id = $3
    AND date BETWEEN $4 AND $5
  GROUP BY keyword_match_type, keyword_text, search_term_match_type, search_term
),

filtered_base_data AS (
  SELECT *
  FROM base_data
  WHERE NOT (
    (spend IS NULL OR spend = 0) AND
    (conversions IS NULL OR conversions = 0) AND
    (ctr IS NULL OR ctr = 0) AND
    (cpa IS NULL OR cpa = 0) AND
    (cpc IS NULL OR cpc = 0)
  )
)

SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM filtered_base_data) THEN (
      SELECT jsonb_object_agg(keyword_match_type, keyword_texts)
      FROM (
        SELECT
          keyword_match_type,
          jsonb_object_agg(keyword_text, search_term_match_types) AS keyword_texts
        FROM (
          SELECT
            keyword_match_type,
            keyword_text,
            jsonb_object_agg(search_term_match_type, search_terms) AS search_term_match_types
          FROM (
            SELECT
              keyword_match_type,
              keyword_text,
              search_term_match_type,
              jsonb_object_agg(
                search_term,
                jsonb_build_object(
                  'spend', spend,
                  'conversions', conversions,
                  'ctr', ctr,
                  'cpa', cpa,
                  'cpc', cpc
                )
              ) AS search_terms
            FROM filtered_base_data
            GROUP BY keyword_match_type, keyword_text, search_term_match_type
          ) AS search_term_level
          GROUP BY keyword_match_type, keyword_text
        ) AS search_term_match_type_level
        GROUP BY keyword_match_type
      ) AS keyword_text_level
    )
    ELSE '{}'::jsonb
  END AS structured_result;


`;

      const query2 = `
WITH base_data AS (
  SELECT
    keyword_match_type,
    keyword_text,
    SUM(spend) AS spend,
    SUM(clicks) AS clicks,
    SUM(impressions) AS impressions,
    SUM(conversions) AS conversions,

    CASE 
      WHEN SUM(impressions) > 0 THEN (SUM(clicks)::float / SUM(impressions)) * 100
      ELSE NULL
    END AS ctr,
    CASE 
      WHEN SUM(conversions) > 0 THEN SUM(spend) / SUM(conversions)
      ELSE NULL
    END AS cpa,
    CASE 
      WHEN SUM(clicks) > 0 THEN SUM(spend) / SUM(clicks)
      ELSE NULL
    END AS cpc
  FROM ${Config.DB_Postgres_SCHEMA}.m_keyword_daywise_table
  WHERE client_id = $1
    AND campaign_id = $2
    AND ad_group_id = $3
    AND date BETWEEN $4 AND $5
  GROUP BY keyword_match_type, keyword_text
),
aggregated_data AS (
  SELECT
    keyword_match_type,
    keyword_text,
    SUM(spend) AS spend,
    SUM(conversions) AS conversions,
    MAX(ctr) AS ctr,
    MAX(cpa) AS cpa,
    MAX(cpc) AS cpc
  FROM base_data
  GROUP BY keyword_match_type, keyword_text
)

SELECT 
  CASE 
    WHEN COUNT(*) > 0 THEN (
      SELECT jsonb_object_agg(keyword_match_type, keyword_texts)
      FROM (
        SELECT
          keyword_match_type,
          jsonb_object_agg(keyword_text, jsonb_build_object(
            'spend', spend,
            'conversions', conversions,
            'ctr', ctr,
            'cpa', cpa,
            'cpc', cpc
          )) AS keyword_texts
        FROM aggregated_data
        GROUP BY keyword_match_type
      ) AS keyword_text_level
    )
    ELSE '{}'::jsonb
  END AS structured_result
FROM aggregated_data;


        
        `;

      const searchTermval: types.GoogleAdsSearchTermDataResponse[] =
         await this.entityManager.query(query1, [
            client_id,
            campaign_id,
            ad_group_id,
            start_date,
            end_date,
         ]);

      const keywordval: types.GoogleAdsKeywordDataResponse[] =
         await this.entityManager.query(query2, [
            client_id,
            campaign_id,
            ad_group_id,
            start_date,
            end_date,
         ]);

      const searchTerms = searchTermval[0].structured_result;
      const keywordValues = keywordval[0].structured_result;

      for (const matchType in keywordValues) {
         if (searchTerms[matchType]) {
            for (const keyword in keywordValues[matchType]) {
               const kpi = keywordValues[matchType][keyword];
               const kpiCopy = { ...kpi };

               if (searchTerms[matchType][keyword]) {
                  searchTerms[matchType][keyword].kpi = { keyword: kpiCopy };
                  keywordValues[matchType][keyword] =
                     searchTerms[matchType][keyword];
               } else {
                  // Clear the object while keeping reference intact
                  Object.keys(keywordValues[matchType][keyword]).forEach(
                     (key) => delete keywordValues[matchType][keyword][key],
                  );

                  // Reassign with only kpi
                  Object.assign(keywordValues[matchType][keyword], {
                     kpi: { keyword: kpiCopy },
                  });
               }
            }
         } else {
            for (const keyword in keywordValues[matchType]) {
               const kpi = keywordValues[matchType][keyword];
               const kpiCopy = { ...kpi };

               Object.keys(keywordValues[matchType][keyword]).forEach(
                  (key) => delete keywordValues[matchType][keyword][key],
               );
               Object.assign(keywordValues[matchType][keyword], {
                  kpi: { keyword: kpiCopy },
               });
            }
         }
      }
      return keywordValues;
   }

   async getTrackedKpis(
      client_id: string,
      channel: string,
      start_date: string,
      end_date: string,
      groupBy: string,
   ): Promise<types.trackedkpisDataResponse[]> {
      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_get_googleads_campaign_kpis($1, $2, $3, $4)`;

      const result: types.trackedkpisDataResponse[] =
         await this.entityManager.query(query, [
            client_id,
            channel,
            start_date,
            end_date,
         ]);
      const correctGrpBy = groupBy === 'day' ? 'N/A' : groupBy;

      const fnResult: types.trackedkpisData[] =
         result?.[0]?.fn_get_googleads_campaign_kpis.flatMap((item) => item);

      const groupedKpiResult = this.groupKpis(
         fnResult || [],
         correctGrpBy,
         start_date,
         end_date,
      ) as types.trackedkpisData[];

      return [{ fn_get_googleads_campaign_kpis: groupedKpiResult }];
   }
   async getTrackedPrevKpis(
      client_id: string,
      channel: string,
      groupBy: string,
      start_date: string,
      end_date: string,
      prev_start_date: string,
      prev_end_date: string,
   ): Promise<types.trackedPrevKpisDataResponse[]> {
      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_get_googleads_campaign_kpis($1, $2, $3, $4)`;

      const result: types.trackedPrevKpisDataResponse[] =
         await this.entityManager.query(query, [
            client_id,
            channel,
            prev_start_date,
            prev_end_date,
         ]);

      const fnResult: types.trackedPrevkpisData[] =
         result?.[0]?.fn_get_googleads_campaign_kpis
            .flatMap((item) => item)
            .map((item) => ({
               ...item,
               kpi_start_date: start_date,
               kpi_end_date: end_date,
               kpi_prev_start_date: prev_start_date,
               kpi_prev_end_date: prev_end_date,
            }));

      const groupedKpiResult = this.groupKpis(
         fnResult || [],
         groupBy,
         prev_start_date,
         prev_end_date,
      ) as types.trackedPrevkpisData[];

      return [{ fn_get_googleads_campaign_kpis: groupedKpiResult }];
   }

   private groupKpis(
      campaignData: types.trackedkpisData[],
      groupBy: string,
      startDate: string,
      endDate: string,
   ): types.trackedkpisData[] | types.trackedPrevkpisData[] {
      const groupedCampaigns: types.trackedkpisData[] = campaignData.map(
         (trackedkpis: types.trackedkpisData) => {
            if (!trackedkpis.kpis) {
               trackedkpis.total_val = null;
               trackedkpis.grouped_kpis = null;
            } else {
               const { currency, campaign_status } =
                  trackedkpis.kpis?.[trackedkpis.kpis?.length - 1] || {};
               const kpiKey: string = trackedkpis.kpi_name;
               const dateAgg = this.splitKpiByDate(trackedkpis.kpis);
               const kpiaggregate = {
                  [kpiKey]: GOOGLE_KPI_CALCULATE[
                     kpiKey as types.GoogleAdsKpiKeys
                  ]
                     ? GOOGLE_KPI_CALCULATE[kpiKey as types.GoogleAdsKpiKeys](
                          dateAgg,
                       )
                     : this.calculateSum(dateAgg, kpiKey),
               };

               trackedkpis.currency = currency;
               trackedkpis.campaign_status = campaign_status;
               trackedkpis.total_val = kpiaggregate;

               if (groupBy !== 'N/A') {
                  const groupedKPIs = this.splitKPIByGroupBy(
                     trackedkpis,
                     groupBy,
                     startDate,
                     endDate,
                  );
                  trackedkpis.grouped_kpis = groupedKPIs;
               }
            }

            return trackedkpis;
         },
      );

      return groupedCampaigns;
   }

   private splitKPIByGroupBy(
      trackedkpis: types.trackedkpisData,
      groupBy: string,
      startDate: string,
      endDate: string,
   ) {
      const result: types.trackedGroupKpis = {};

      const filteredKPIbyRange = this.filterByRange(
         trackedkpis.kpis || [],
         groupBy,
         startDate,
         endDate,
      );

      for (const [key, kpis] of Object.entries(filteredKPIbyRange)) {
         const kpiKey = trackedkpis.kpi_name;
         const dateAgg = this.splitKpiByDate(kpis);

         const kpiAggregate = {
            [kpiKey]: GOOGLE_KPI_CALCULATE[kpiKey as types.GoogleAdsKpiKeys]
               ? GOOGLE_KPI_CALCULATE[kpiKey as types.GoogleAdsKpiKeys](dateAgg)
               : this.calculateSum(dateAgg, kpiKey),
         };

         result[key] = kpiAggregate;
      }

      return result;
   }

   private filterByRange(
      trackedCampaignKpis: types.GoogleKPIData[],
      groupBy: string,
      startDateStr: string,
      endDateStr: string,
   ) {
      const startDateValue = startDateStr.split('-');
      const endDateValue = endDateStr.split('-');
      const startDate = new Date(
         Date.UTC(
            +startDateValue[0],
            +startDateValue[1] - 1,
            +(startDateValue[2].length === 1
               ? `0${startDateValue[2]}`
               : startDateValue[2]),
         ),
      );
      const endDate = new Date(
         Date.UTC(
            +endDateValue[0],
            +endDateValue[1] - 1,
            +(endDateValue[2].length === 1
               ? `0${endDateValue[2]}`
               : endDateValue[2]),
         ),
      );
      startDate.setUTCHours(0, 0, 0, 0);
      endDate.setUTCHours(0, 0, 0, 0);
      const result: { [key: string]: types.GoogleKPIData[] } = {};
      const days = DAYS_IN[groupBy];

      const currentStart = new Date(startDate);
      let currentEnd = new Date(currentStart);
      currentEnd.setDate(currentEnd.getDate() + Number(days) - 1);
      while (currentStart <= endDate) {
         if (currentEnd > endDate) {
            currentEnd = new Date(endDate);
         }

         const weekKey = `${currentStart.toISOString().split('T')[0]} - ${currentEnd.toISOString().split('T')[0]}`;
         const filteredItems: types.GoogleKPIData[] =
            trackedCampaignKpis.filter((item) => {
               const itemDate = new Date(item.kpi_date);
               return itemDate >= currentStart && itemDate <= currentEnd;
            });
         result[weekKey] = filteredItems;

         currentStart.setDate(currentStart.getDate() + days);
         currentEnd = new Date(currentStart);
         currentEnd.setDate(currentEnd.getDate() + days - 1);
      }

      return result;
   }

   private calculateKpi(
      campaignKpis: types.GoogleKPIData[],
      kpiAggregate: types.GoogleKpiAggregate,
   ): types.GoogleKpiAggregate {
      const dateAgg = this.splitKpiByDate(campaignKpis);

      for (const kpi in kpiAggregate) {
         const kpiKey: string = kpi;
         if (GOOGLE_KPI_CALCULATE[kpiKey as types.GoogleAdsKpiKeys]) {
            kpiAggregate[kpi] =
               GOOGLE_KPI_CALCULATE[kpiKey as types.GoogleAdsKpiKeys](dateAgg);
         } else {
            kpiAggregate[kpi] = this.calculateSum(dateAgg, kpi);
         }
      }

      return kpiAggregate;
   }

   private splitKpiByDate(data: types.GoogleKPIData[]): types.GoogleDateAgg {
      const dateAgg: types.GoogleDateAgg = {};
      data.forEach((x) => {
         const dateKey: string = x.kpi_date.split('T')[0];
         if (dateAgg[dateKey]) {
            dateAgg[dateKey] = {
               ...dateAgg[dateKey],
               [x.kpi_name]: x.kpi_value,
            };
         } else {
            dateAgg[dateKey] = { [x.kpi_name]: x.kpi_value };
         }
      });

      return dateAgg;
   }

   private calculateSum(data: types.GoogleDateAgg, kpi: string): number {
      let sum = 0;

      for (const date in data) {
         const value = data[date][kpi];
         if (value) sum += Number(value);
      }

      return sum;
   }

   private calculatePreviousPeriod(
      start_date: string,
      end_date: string,
   ): {
      startDate: string;
      endDate: string;
   } {
      const startDate = new Date(start_date);
      const endDate = new Date(end_date);

      const differenceInMs = endDate.getTime() - startDate.getTime();

      const previousStartDate = new Date(startDate.getTime() - differenceInMs);
      const previousEndDate = new Date(endDate.getTime() - differenceInMs);
      const formatDate = (date: Date) => date.toISOString().split('T')[0];

      return {
         startDate: formatDate(previousStartDate),
         endDate: formatDate(previousEndDate),
      };
   }

   private getKpiwiseData(
      campaignKpis: types.GoogleKPIData[],
      kpiAggregate: types.KpiwiseAggregate,
   ): types.KpiwiseAggregate {
      const transformedKpis = campaignKpis.reduce(
         (
            acc: types.KpiwiseAggregate,
            { kpi_date, kpi_name, kpi_value, budget, currency },
         ) => {
            if (!acc[kpi_name]) {
               acc[kpi_name] = [];
            }
            acc[kpi_name].push({
               date_time: kpi_date,
               kpi_value,
               budget,
               currency,
            });
            return acc;
         },
         kpiAggregate,
      );
      return transformedKpis;
   }

   private calculateKeywordsAggregate(
      data: types.GoogleKPIData[],
      keyword: types.DimensionKeys,
   ): types.AggregatedResult {
      const DIMENSION_VALUES = {
         day_of_week: [
            'SUNDAY',
            'MONDAY',
            'TUESDAY',
            'WEDNESDAY',
            'THURSDAY',
            'FRIDAY',
            'SATURDAY',
         ],
         hour: Array.from({ length: 24 }, (_, i) => i.toString()),
         device: ['CONNECTED TV', 'DESKTOP', 'MOBILE', 'TABLET'],
      };

      const aggregatedResults: Record<string, Map<string, number>> = {};

      const parseJSONData = (
         rawValue: string,
      ): Record<string, number> | undefined => {
         try {
            if (!rawValue) return undefined;

            const normalizedJson = rawValue
               .replace(/'/g, '"')
               .replace(/([{,]\s*)(\w+)(\s*:)/g, '$1"$2"$3')
               .replace(/:\s*(nan|inf)\b/gi, ': "__DELETE__"');

            return JSON.parse(normalizedJson, (key, value) =>
               value === '__DELETE__' || value === null ? undefined : value,
            );
         } catch (error) {
            logger.warn(`Failed to parse JSON:`, error);
            return undefined;
         }
      };

      for (const item of data) {
         const kpiName = item.kpi_name;
         const parsedData = parseJSONData(item[keyword] as types.DimensionKeys);

         if (!parsedData) continue;

         if (!aggregatedResults[kpiName]) {
            aggregatedResults[kpiName] = new Map<string, number>();
         }

         Object.entries(parsedData).forEach(([dimKey, dimValue]) => {
            aggregatedResults[kpiName].set(
               dimKey,
               (aggregatedResults[kpiName].get(dimKey) || 0) + dimValue,
            );
         });
      }

      return Object.fromEntries(
         Object.entries(aggregatedResults).map(([kpiName, entriesMap]) => {
            const resultsArray = Array.from(entriesMap.entries()).map(
               ([keyword_name, aggregate_value]) => ({
                  keyword_name,
                  aggregate_value:
                     kpiFormulas[kpiName as types.GoogleAdsKpiKeys]?.(
                        aggregatedResults,
                        keyword_name,
                     ) ?? aggregate_value,
               }),
            );

            const dimensionSet = new Set(
               resultsArray.map((entry) => entry.keyword_name),
            );
            const expectedKeys = DIMENSION_VALUES[keyword] ?? [];

            expectedKeys.forEach((key) => {
               if (!dimensionSet.has(key)) {
                  resultsArray.push({
                     keyword_name: key,
                     aggregate_value: NaN,
                  });
               }
            });

            resultsArray.sort(
               (a, b) =>
                  expectedKeys.indexOf(a.keyword_name) -
                  expectedKeys.indexOf(b.keyword_name),
            );

            return [kpiName, resultsArray];
         }),
      );
   }
}
