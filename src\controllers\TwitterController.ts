import { NextFunction, Request, Response } from 'express';
import { TwitterService } from '../services/TwitterService';
import { Logger } from 'winston';
import {
   ContentCalendarDto,
   GetCaptionPayload,
   GetContentCalendarDto,
   PostTweetDto,
} from '../types';
import { scheduleContentCalendarData } from '../utils/cron/content-calendar';

export class TwitterController {
   constructor(
      private twitterService: TwitterService,
      private logger: Logger,
   ) {}

   async handlePostContentCalendar(req: Request, res: Response) {
      const message = 'Content Calendar posted successfully';
      const payload = req.body as ContentCalendarDto;
      await this.twitterService.postContentCalendar(payload);
      this.logger.info(message);

      if (payload.is_schedule) {
         void scheduleContentCalendarData(payload);
         this.logger.info('Content Calendar scheduled successfully');
      }

      res.status(201).send(message);
   }
   async handlePostTweets(req: Request, res: Response) {
      await this.twitterService.postTweets(req.body as PostTweetDto);
      this.logger.info('Tweets posted successfully');
      res.status(200).send({ message: 'Tweet posted successfully' });
   }

   async handleUploadMediaToTwitter(req: Request, res: Response) {
      if (!req.file) throw new Error('No file provided');

      const { oauthToken, oauthTokenSecret } = req.body as {
         oauthToken: string;
         oauthTokenSecret: string;
      };
      const result = await this.twitterService.uploadMediaToTwitter(
         req.file,
         oauthToken,
         oauthTokenSecret,
      );
      this.logger.info('Image uploaded to twitter successfully');
      res.status(200).send({ mediaId: result });
   }
   async handleGetContentCalendar(req: Request, res: Response) {
      const contentCalendarData = await this.twitterService.getContentCalendar(
         req.body as GetContentCalendarDto,
      );
      this.logger.info('Content Calendar fetched successfully');
      res.status(200).send({ contentCalendarData });
   }

   async handleDeleteTweets(req: Request, res: Response) {
      const result = await this.twitterService.deleteTweets(
         req.body as { uuid: string },
      );
      this.logger.info('Tweets deleted successfully');
      res.status(200).send(result);
   }

   async handleUpdateContentCalendar(req: Request, res: Response) {
      const updatedData = await this.twitterService.updateContentCalendar(
         req.body as ContentCalendarDto,
      );
      this.logger.info('Content Calendar updated successfully');

      const {
         is_schedule,
         post_data,
         date,
         time,
         uuid,
         client_id,
         media,
         social_media_type,
      } = updatedData as ContentCalendarDto;

      if (is_schedule) {
         void scheduleContentCalendarData({
            post_data,
            date,
            time,
            uuid,
            client_id,
            media,
            social_media_type,
         });
         this.logger.info('Content Calendar scheduled successfully');
      }

      res.status(200).send({ updatedData });
   }

   async handleGetCaption(req: Request, res: Response, next: NextFunction) {
      try {
         const body = req.body as GetCaptionPayload;
         const captions = await this.twitterService.getCaption(body);
         this.logger.info('Caption fetched successfully');
         res.status(200).send({
            captions,
            media: body.social_channel.toLowerCase(),
         });
      } catch (error) {
         this.logger.error('Error while fetching caption');
         next(error);
      }
   }
}
