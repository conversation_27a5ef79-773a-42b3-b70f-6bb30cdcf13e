import express from 'express';
import asyncHandler from '../../../../midddleware/async-handler';
import { AppDataSource } from '../../../../config/data-source';
import { logger } from '../../../../config/logger';
import { AlertingAgentController } from '../../../../controllers/agents/alerting-agent/alerting-agent.controller';
import { AlertingAgentService } from '../../../../services/agents/alerting-agent/alerting-agent.service';
import { AlertingAgentModel } from '../../../../models/agents/alerting-agent/alerting-agent.model';
import { EmailService } from '../../../../services/EmailService';
import { WhatsappService } from '../../../../services/WhatsappService';
import { EntityManager } from 'typeorm';

const router = express.Router();

const emailService = new EmailService();
const alertingAgentModel = new AlertingAgentModel(AppDataSource.manager);
const whatsappService = new WhatsappService();
const entityManager = AppDataSource.manager;
const alertingAgentService = new AlertingAgentService(
   alertingAgentModel,
   emailService,
   whatsappService,
   entityManager,
);
const alertingAgentController = new AlertingAgentController(
   logger,
   alertingAgentService,
);

router
   .route('/:client_id/:user_id/session')
   .get(
      asyncHandler(
         alertingAgentController.fetchAllSessionsByUserID.bind(
            alertingAgentController,
         ),
      ),
   );

router
   .route('/:client_id/:user_id/session/:session_id')
   .get(
      asyncHandler(
         alertingAgentController.fetchHistoryBySessionID.bind(
            alertingAgentController,
         ),
      ),
   )
   .post(
      asyncHandler(
         alertingAgentController.addChatToSessionHistory.bind(
            alertingAgentController,
         ),
      ),
   );

router
   .route('/:client_id/:user_id/session/:session_id/chat/:chat_id')
   .get(
      asyncHandler(
         alertingAgentController.fetchChatByChatId.bind(
            alertingAgentController,
         ),
      ),
   );

router
   .route('/:client_id/:user_id/session/:session_id/alerts')
   .get(
      asyncHandler(
         alertingAgentController.fetchAlertsBySessionID.bind(
            alertingAgentController,
         ),
      ),
   );

router
   .route('/:client_id/:user_id/alerts')
   .get(
      asyncHandler(
         alertingAgentController.fetchAllAlerts.bind(alertingAgentController),
      ),
   )
   .post(
      asyncHandler(
         alertingAgentController.createAlert.bind(alertingAgentController),
      ),
   )
   .delete(
      asyncHandler(
         alertingAgentController.deleteMultipleAlerts.bind(
            alertingAgentController,
         ),
      ),
   );

router
   .route('/:client_id/:user_id/alerts/:alert_id')
   .get(
      asyncHandler(
         alertingAgentController.fetchAlertById.bind(alertingAgentController),
      ),
   )
   .put(
      asyncHandler(
         alertingAgentController.updateAlert.bind(alertingAgentController),
      ),
   )
   .delete(
      asyncHandler(
         alertingAgentController.deleteAlert.bind(alertingAgentController),
      ),
   );

router
   .route('/:client_id/:user_id/alerts/:alert_id/pause')
   .put(
      asyncHandler(
         alertingAgentController.pauseUnpauseAlert.bind(
            alertingAgentController,
         ),
      ),
   );

router
   .route('/:client_id/:user_id/alerts/:alert_id/update-recipients')
   .put(
      asyncHandler(
         alertingAgentController.updateEmailRecipients.bind(
            alertingAgentController,
         ),
      ),
   );

router
   .route('/alerts/check-criteria')
   .post(
      asyncHandler(
         alertingAgentController.checkAlertCriteria.bind(
            alertingAgentController,
         ),
      ),
   );

export { router as alertingAgentRouter };
