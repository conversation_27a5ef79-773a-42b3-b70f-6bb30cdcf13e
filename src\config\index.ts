import { config } from 'dotenv';

config();
const {
   PORT,
   LOG_LEVEL,
   NODE_ENV,
   DB_Postgres_HOST,
   DB_Postgres_USER,
   DB_Postgres_PASSWORD,
   DB_Postgres_DATABASE,
   DB_Postgres_PORT,
   DB_Postgres_SCHEMA,
   DB_Postgres_CONFIG_SCHEMA,
   DB_Mongo_URL,
   DB_KEYWORD_TABLE_NAME,
   DB_Postgres_SUBS_SCHEMA,
   TWITTER_CONSUMER_KEY,
   TWITTER_CONSUMER_SECRET,
   GOOGLE_ADS_DEVELOPER_TOKEN,
   GOOGLE_CLIENT_ID,
   GOOGLE_CLIENT_SECRET,
   FRONTEND_DOMAIN,
   BACKEND_DOMAIN,
   AZURE_STORAGE_ACCOUNT_KEY,
   ACCOUNT_NAME_AZURE,
   CONTAINER_NAME_AZURE,
   COPILOT_URL,
   CRON_JOB_CYCL<PERSON>,
   DB_Postgres_SCHEMA_AIRBYTE,
   K<PERSON>_DATA_PATH,
   GPT_URL,
   GPT_BEARER,
   <PERSON>IVE<PERSON>AN_KEY,
   <PERSON>IVE<PERSON>AN_GROUP_ID,
   MAIL_HOST,
   MAIL_USERNAME,
   MAIL_PASSWORD,
   MAIL_SALES,
   AIRBYTE_DATA_SYNC_FREQ,
   AMAZON_SELLER_PARTNER_MERCHANT_ID,
   JWT_REFRESH_TOKEN_SECRET,
   SCRIPT_SRC,
   TWITTER_REQUEST_TOKEN_URL,
   TWITTER_AUTHORIZE_URL,
   TWITTER_ACCESS_TOKEN_URL,
   LINKEDIN_CLIENT_ID,
   LINKEDIN_CLIENT_SECRET,
   LINKEDIN_REDIRECT_URI,
   LINKEDIN_SCOPE,
   TESTCONNECTION_VALIDATE_URL,
   AIRBYTE_CLIENT_ID,
   AIRBYTE_CLIENT_SECRET,
   MAX_AGE_REFRESH_TOKEN,
   DB_Postgres_AGENTS_SCHEMA,
   GOOGLE_REDIRECT_URI,
   GOOGLE_ANALYTICS_REDIRECT_URI,
   META_OAUTH_VERSION,
   META_APP_ID,
   META_APP_SECRET,
   META_REDIRECT_URI,
   META_ACCESS_TOKEN,
   META_BASE_URL,
   AD_ACCOUNT_ID,
   OPENAI_API_KEY,
   DIAGNOSTICS_URL,
   RUNWAYML_API_SECRET,
   REDIS_PREFIX,
   REDIS_HOST,
   REDIS_PORT,
   REDIS_PASS,
   RUNWAYML_MODEL,
   AISENSY_API_KEY,
   VITE_XI_AGENT_API,
   ENCRYPTION_KEY,
} = process.env;

export const Config = {
   PORT,
   LOG_LEVEL,
   ENVIRONMENT: NODE_ENV,
   DB_Postgres_HOST,
   DB_Postgres_DATABASE,
   DB_Postgres_PASSWORD,
   DB_Postgres_USER,
   DB_Postgres_PORT,
   DB_Postgres_SCHEMA,
   DB_Postgres_CONFIG_SCHEMA,
   DB_Mongo_URL,
   DB_KEYWORD_TABLE_NAME,
   DB_Postgres_SUBS_SCHEMA,
   TWITTER_CONSUMER_KEY,
   TWITTER_CONSUMER_SECRET,
   GOOGLE_ADS_DEVELOPER_TOKEN,
   GOOGLE_CLIENT_ID,
   GOOGLE_CLIENT_SECRET,
   FRONTEND_DOMAIN,
   BACKEND_DOMAIN,
   AZURE_STORAGE_ACCOUNT_KEY,
   ACCOUNT_NAME_AZURE,
   CONTAINER_NAME_AZURE,
   COPILOT_URL,
   GPT_URL,
   GPT_BEARER,
   CRON_JOB_CYCLE,
   DB_Postgres_SCHEMA_AIRBYTE,
   KPI_DATA_PATH,
   FIVETRAN_KEY,
   FIVETRAN_GROUP_ID,
   MAIL_HOST,
   MAIL_USERNAME,
   MAIL_PASSWORD,
   MAIL_SALES,
   AIRBYTE_DATA_SYNC_FREQ,
   AMAZON_SELLER_PARTNER_MERCHANT_ID,
   JWT_REFRESH_TOKEN_SECRET,
   SCRIPT_SRC,
   TWITTER_REQUEST_TOKEN_URL,
   TWITTER_AUTHORIZE_URL,
   TWITTER_ACCESS_TOKEN_URL,
   LINKEDIN_CLIENT_ID,
   LINKEDIN_CLIENT_SECRET,
   LINKEDIN_SCOPE,
   LINKEDIN_REDIRECT_URI,
   TESTCONNECTION_VALIDATE_URL,
   AIRBYTE_CLIENT_ID,
   AIRBYTE_CLIENT_SECRET,
   MAX_AGE_REFRESH_TOKEN,
   DB_Postgres_AGENTS_SCHEMA,
   GOOGLE_REDIRECT_URI,
   GOOGLE_ANALYTICS_REDIRECT_URI,
   META_OAUTH_VERSION,
   META_APP_ID,
   META_APP_SECRET,
   META_REDIRECT_URI,
   META_ACCESS_TOKEN,
   META_BASE_URL,
   AD_ACCOUNT_ID,
   OPENAI_API_KEY,
   DIAGNOSTICS_URL,
   RUNWAYML_API_SECRET,
   REDIS_PREFIX,
   REDIS_HOST,
   REDIS_PORT: Number(REDIS_PORT),
   REDIS_PASS,
   RUNWAYML_MODEL,
   AISENSY_API_KEY,
   VITE_XI_AGENT_API,
   ENCRYPTION_KEY,
};
