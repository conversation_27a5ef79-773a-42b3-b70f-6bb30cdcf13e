import express from 'express';
import { logger } from '../../config/logger';
import { EntityManager } from 'typeorm';
import asyncHandler from '../../midddleware/async-handler';
import { AppDataSource } from '../../config/data-source';
import { EmailService } from '../../services/EmailService';
import { WhatsappService } from '../../services/WhatsappService';
import { CustomAlertModel } from './custom.alert.model';
import { CustomAlertController } from '../custom-alert/custom-alert.controller';
import { CustomAlertService } from '../custom-alert/custom-alert.service';

const router = express.Router();

const emailService = new EmailService();
const whatsappService = new WhatsappService();
const customAlertModel = new CustomAlertModel(AppDataSource.manager);
const entityManager = AppDataSource.manager;
const customAlertService = new CustomAlertService(
   customAlertModel,
   emailService,
   whatsappService,
   entityManager,
);
const customAlertController = new CustomAlertController(
   logger,
   customAlertService,
);

// GET all alerts
router
   .route('/:client_id/:user_id/alerts')
   .get(
      asyncHandler(
         customAlertController.getAllAlerts.bind(customAlertController),
      ),
   );

// GET one alert by ID
router
   .route('/:client_id/:user_id/alerts/:alertId')
   .get(
      asyncHandler(
         customAlertController.getAlertById.bind(customAlertController),
      ),
   );

// POST create new alert (or) update existing alert
router
   .route('/:client_id/:user_id/alerts')
   .post(
      asyncHandler(
         customAlertController.createOrUpdateAlert.bind(customAlertController),
      ),
   );

// DELETE alert
router
   .route('/:client_id/:user_id/alerts/:alertId')
   .delete(
      asyncHandler(
         customAlertController.deleteAlert.bind(customAlertController),
      ),
   );

// DELETE multiple alerts
router
   .route('/:client_id/:user_id/alerts/delete-multiple')
   .post(
      asyncHandler(
         customAlertController.deleteMultipleAlerts.bind(customAlertController),
      ),
   );

// GET alert options
router
   .route('/:client_id/:user_id/alert-options')
   .get(
      asyncHandler(
         customAlertController.fetchOptions.bind(customAlertController),
      ),
   );

// POST check alert conditions
router
   .route('/check-conditions')
   .post(
      asyncHandler(
         customAlertController.checkAlertConditions.bind(customAlertController),
      ),
   );

export { router as customAlertRouter };
