import { Request, Response } from 'express';
import { Logger } from 'winston';
import { TwitterAuthService } from '../services/TwitterAuthService';

export class TwitterAuthController {
   constructor(
      private twitterAuthService: TwitterAuthService,
      private logger: Logger,
   ) {}

   async handleRequestToken(req: Request, res: Response) {
      const tokenData = await this.twitterAuthService.requestToken();
      res.json({ tokenData });
   }

   async handleAccessToken(req: Request, res: Response) {
      const { oauthToken, clientId, pin } = req.body as {
         oauthToken: string;
         pin: string;
         clientId: string;
      };

      const connectionDetails = await this.twitterAuthService.fetchTokenDetails(
         oauthToken,
         pin,
         clientId,
      );

      if (!connectionDetails) throw new Error('Unauthorized');

      res.json({ connectionDetails });
   }
}
