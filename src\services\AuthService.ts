import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import {
   RegisterDetails,
   User,
   LoginDetails,
   LogoutDetails,
   VerifyEmailPayload,
   IsEmailVerifiedPayload,
   EmailVerificationDetails,
   ClientRoleDetails,
   UserLoginStatusResponse,
   UserLoginStatus,
   ResetPasswordPayload,
   RefreshTokenDetails,
} from '../types';
import { Logger } from 'winston';
import { Config } from '../config';
import { EntityManager } from 'typeorm';

import { EmailService } from './EmailService';
import { regex } from '../utils/authHelpers';
import { returnOTPVerificationEmailHTML } from '../constants/email-templates';

export class AuthService {
   constructor(
      private logger: Logger,
      private entityManager: EntityManager,
      private emailService: EmailService,
   ) {}

   private createRefreshToken(email_address: string): string {
      return jwt.sign(
         { email_address: email_address },
         `${Config.JWT_REFRESH_TOKEN_SECRET}`,
         {
            expiresIn: '1d',
         },
      );
   }

   private validateFields(name: string, value: string) {
      switch (name) {
         case 'full_name':
            if (!value) {
               return 'Full Name cannot be empty';
            }
            break;
         case 'email_address':
            if (!regex.email_address.test(value)) {
               return 'Please enter a valid email address';
            }
            break;
         case 'password':
            if (!regex.password.test(value)) {
               return 'Password must be at least 8 characters long, must have atleast one number and one special character';
            }
            break;
      }
   }

   private async findUserByEmail(email: string): Promise<User | null> {
      try {
         const userDetailsQuery = `SELECT * FROM ${Config.DB_Postgres_CONFIG_SCHEMA}.user_login WHERE email_address = $1`;
         const user: User[] = await this.entityManager.query(userDetailsQuery, [
            email,
         ]);
         return user?.length ? user[0] : null;
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'findUserByEmail',
         });
         throw new Error(error.message);
      }
   }

   private isMoreThan10Minutes(timestamp: string) {
      const TEN_MINUTES_IN_MS = 10 * 60 * 1000;

      const timestampDate = new Date(`${timestamp}Z`);
      const currentTime = Date.now();

      return currentTime - timestampDate.getTime() > TEN_MINUTES_IN_MS;
   }

   private async checkIfOTPExpired(
      email: string,
      action: 'email-verification' | 'password-reset',
   ): Promise<boolean> {
      try {
         const query = `SELECT * FROM ${Config.DB_Postgres_CONFIG_SCHEMA}.user_email_verification WHERE email_address = $1`;
         const result: EmailVerificationDetails[] =
            await this.entityManager.query(query, [email]);

         return this.isMoreThan10Minutes(
            action === 'password-reset'
               ? result[0].forget_password_update_date
               : result[0].update_date,
         );
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'checkIfOTPExpired',
         });
         throw new Error(error.message);
      }
   }

   private async checkIfOTPIsValid(
      email: string,
      otp: string,
      action: 'email-verification' | 'password-reset',
   ): Promise<boolean> {
      try {
         const query = `SELECT * FROM ${Config.DB_Postgres_CONFIG_SCHEMA}.user_email_verification WHERE email_address = $1`;
         const result: EmailVerificationDetails[] =
            await this.entityManager.query(query, [email]);

         const relatedOtp =
            action === 'password-reset' ? 'forget_password_otp' : 'email_otp';

         return Number(result[0][relatedOtp]) === Number(otp);
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'checkIfOTPIsValid',
         });
         throw new Error(error.message);
      }
   }

   private async validateUserLogin(
      email_address: string,
      password: string,
   ): Promise<UserLoginStatus> {
      try {
         const query = `SELECT * FROM ${Config.DB_Postgres_CONFIG_SCHEMA}.fn_user_login_validate($1::json)`;
         const result: UserLoginStatusResponse[] =
            await this.entityManager.query(query, [
               JSON.stringify([{ email_address, password }]),
            ]);

         return result?.[0]?.user_login_status?.[0];
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'validateUserLogin',
         });
         throw new Error(error.message);
      }
   }

   async register(payload: RegisterDetails) {
      const { full_name, email_address, password, cb_product_updates } =
         payload;

      // Check if any field is empty
      if (!full_name || !email_address || !password) {
         throw new Error('Please fill all required fields');
      }

      try {
         // Validate the fields
         const fullNameError = this.validateFields('full_name', full_name);
         const emailError = this.validateFields('email_address', email_address);
         const passwordError = this.validateFields('password', password);
         if (fullNameError || emailError || passwordError) {
            throw new Error('One or more fields are invalid');
         }

         // Check if the user already exists
         const userExists = await this.findUserByEmail(email_address);
         if (userExists) {
            throw new Error('Email already exists');
         }

         // Hash the password
         const hashedPassword = await bcrypt.hash(password, 10);

         // Insert the user into the database
         const userData = [
            {
               full_name: full_name,
               email_address: email_address,
               password: hashedPassword,
               user_active: 'Y',
               cb_product_updates: cb_product_updates ? 'Y' : 'N',
               create_date: new Date().toISOString(),
               register_progress: 'Step 1',
            },
         ];
         const registerQuery = `CALL ${Config.DB_Postgres_CONFIG_SCHEMA}.q_user_login_put($1::json)`;
         await this.entityManager.query(registerQuery, [
            JSON.stringify(userData),
         ]);

         // Generate a six digit otp
         const otp = Math.floor(100000 + Math.random() * 900000).toString();

         // Insert the otp into the database
         const emailVerificationStatus = {
            email_address: email_address,
            email_otp: otp,
            is_email_verified: false,
         };
         const emailVerificationQuery = `CALL ${Config.DB_Postgres_CONFIG_SCHEMA}.q_upsert_user_email_verification($1::jsonb)`;
         await this.entityManager.query(emailVerificationQuery, [
            JSON.stringify(emailVerificationStatus),
         ]);

         // Send the otp to the user via email
         const emailInfo = {
            to: email_address,
            subject: 'Your OTP for Verification',
            text: 'OTP Verification',
            html: returnOTPVerificationEmailHTML(full_name, otp),
         };
         await this.emailService.sendEmail(emailInfo);
         this.logger.info('OTP verification email sent successfully');

         return {
            response: {
               status: 'Success',
               message: 'Registered successfully.',
               email_address: email_address,
               register_progress: 'Step 1',
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, { serviceFunction: 'register' });
         throw new Error(error.message);
      }
   }

   async login(payload: LoginDetails) {
      const { email_address, password } = payload;

      if (!email_address || !password) {
         throw new Error('Please fill all required fields');
      }

      try {
         // Validate the fields
         const emailError = this.validateFields('email_address', email_address);
         const passwordError = this.validateFields('password', password);
         if (emailError || passwordError) {
            throw new Error('One or more fields are invalid');
         }

         // Check if the user exists
         const user = await this.findUserByEmail(email_address);
         if (!user) {
            throw new Error('Invalid email or password');
         }

         // Verify the password
         const isPasswordMatch = await bcrypt.compare(password, user.password);
         if (!isPasswordMatch) {
            throw new Error('Invalid email or password');
         }

         // If login is successful, check if the user has verified their email
         const emailVerificationQuery = `SELECT * FROM ${Config.DB_Postgres_CONFIG_SCHEMA}.user_email_verification WHERE email_address = $1`;
         const result: EmailVerificationDetails[] =
            await this.entityManager.query(emailVerificationQuery, [
               email_address,
            ]);

         // If the user has not verified their email, generate an otp and send it to their email
         if (!(result.length > 0) || result?.[0]?.is_email_verified === false) {
            const otp = Math.floor(100000 + Math.random() * 900000).toString();
            const emailVerificationStatus = {
               email_address: email_address,
               email_otp: otp,
               is_email_verified: false,
            };

            const emailVerificationUpdateQuery = `CALL ${Config.DB_Postgres_CONFIG_SCHEMA}.q_upsert_user_email_verification($1::jsonb)`;
            await this.entityManager.query(emailVerificationUpdateQuery, [
               JSON.stringify(emailVerificationStatus),
            ]);

            const emailInfo = {
               to: email_address,
               subject: 'Your OTP for Verification',
               text: 'OTP Verification',
               html: returnOTPVerificationEmailHTML(user.full_name, otp),
            };
            await this.emailService.sendEmail(emailInfo);
            this.logger.info('OTP verification email sent successfully');

            return {
               response: {
                  status: 'Success',
                  message: 'OTP Verification Required.',
                  email_address: email_address,
               },
            };
         }

         // Check the current step the user is at
         if (user.register_progress?.trim() !== 'Completed') {
            return {
               response: {
                  status: 'Success',
                  message: 'Logged in successfully',
                  email_address: user.email_address,
                  register_progress: user.register_progress,
               },
            };
         }

         /**
          * If the user onboarding is complete, then fetch the client id and
          * create refresh token, store refresh token in db
          */
         const refreshToken = this.createRefreshToken(user.email_address);

         // Fetch the client id
         const clientDetailsQuery = `SELECT * FROM ${Config.DB_Postgres_CONFIG_SCHEMA}.user_role WHERE email_address = $1`;
         const clientDetails: ClientRoleDetails[] =
            await this.entityManager.query(clientDetailsQuery, [
               user.email_address,
            ]);

         const query = `CALL ${Config.DB_Postgres_CONFIG_SCHEMA}.q_user_login_token_put($1, $2, $3)`;
         await this.entityManager.query(query, [
            user.email_address,
            refreshToken,
            clientDetails[0].client_id,
         ]);

         return {
            response: {
               status: 'Success',
               message: 'Logged in successfully.',
               register_progress: 'Completed',
               email_address: user.email_address,
               client_id: clientDetails[0].client_id,
               full_name: user.full_name,
               refreshToken,
            },
            refreshToken,
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, { serviceFunction: 'login' });
         throw new Error(error.message);
      }
   }

   async setRefreshToken(payload: RefreshTokenDetails) {
      const { email_address, client_id } = payload;

      try {
         const user = await this.findUserByEmail(email_address);
         if (!user) {
            throw new Error('Invalid email address');
         }

         const refreshToken = this.createRefreshToken(email_address);

         const query = `CALL ${Config.DB_Postgres_CONFIG_SCHEMA}.q_user_login_token_put($1, $2, $3)`;
         await this.entityManager.query(query, [
            email_address,
            refreshToken,
            client_id,
         ]);

         return {
            response: {
               status: 'Success',
               message: 'Logged in successfully.',
               register_progress: 'Completed',
               email_address: email_address,
               client_id: client_id,
               full_name: user.full_name,
               refreshToken,
            },
            refreshToken,
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, { serviceFunction: 'login' });
         throw new Error(error.message);
      }
   }

   async sendOtp(
      email_address: string,
      action: 'email-verification' | 'password-reset',
   ) {
      try {
         const otp = Math.floor(100000 + Math.random() * 900000).toString();

         if (action === 'password-reset') {
            const otpQuery = `CALL ${Config.DB_Postgres_CONFIG_SCHEMA}.q_update_forget_password_otp($1, $2)`;
            await this.entityManager.query(otpQuery, [email_address, otp]);
         } else if (action === 'email-verification') {
            const emailVerificationStatus = {
               email_address: email_address,
               email_otp: otp,
               is_email_verified: false,
            };

            const otpQuery = `CALL ${Config.DB_Postgres_CONFIG_SCHEMA}.q_upsert_user_email_verification($1::jsonb)`;
            await this.entityManager.query(otpQuery, [emailVerificationStatus]);
         }

         const emailInfo = {
            to: email_address,
            subject: 'Your OTP for Verification',
            text: 'OTP Verification',
            html: returnOTPVerificationEmailHTML('', otp),
         };

         await this.emailService.sendEmail(emailInfo);
         this.logger.info('OTP verification email sent successfully');

         return {
            response: {
               status: 'Success',
               message: 'OTP sent successfully.',
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, { serviceFunction: 'sendOtp' });
         throw new Error(error.message);
      }
   }

   async verifyEmail(payload: VerifyEmailPayload) {
      const { email_address, email_otp, action } = payload;

      try {
         const isOTPExpired = await this.checkIfOTPExpired(
            email_address,
            action,
         );

         if (isOTPExpired) {
            throw new Error('OTP expired. Please login again.');
         }

         const isValidOtp = await this.checkIfOTPIsValid(
            email_address,
            email_otp,
            action,
         );

         if (!isValidOtp) {
            throw new Error('Invalid OTP. Please try again.');
         }

         if (action === 'email-verification') {
            const updatedData = {
               email_address,
               email_otp,
               is_email_verified: true,
            };

            const query = `CALL ${Config.DB_Postgres_CONFIG_SCHEMA}.q_upsert_user_email_verification($1::jsonb)`;
            await this.entityManager.query(query, [
               JSON.stringify(updatedData),
            ]);
         }

         return {
            response: {
               status: 'Success',
               message: 'Email verified successfully.',
               email_address,
               register_progress: 'Step 1',
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, { serviceFunction: 'verifyEmail' });
         throw new Error(error.message);
      }
   }

   async isEmailVerified(payload: IsEmailVerifiedPayload) {
      const { email_address } = payload;

      try {
         const query = `SELECT * FROM ${Config.DB_Postgres_CONFIG_SCHEMA}.user_email_verification WHERE email_address = $1`;
         const result: EmailVerificationDetails[] =
            await this.entityManager.query(query, [email_address]);

         return {
            response: {
               status: 'Success',
               message: 'Email verified successfully.',
               email_address: email_address,
               is_email_verified:
                  result.length > 0 ? result[0].is_email_verified : false,
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'isEmailVerified',
         });
         throw new Error(error.message);
      }
   }

   async resetPassword(payload: ResetPasswordPayload) {
      try {
         const { email_address, password } = payload;

         const hashedPassword = await bcrypt.hash(password, 10);

         const query = `CALL ${Config.DB_Postgres_CONFIG_SCHEMA}.q_user_login_pwd_reset($1, $2)`;
         await this.entityManager.query(query, [email_address, hashedPassword]);

         return {
            response: {
               status: 'Success',
               message: 'Password reset successfully.',
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'isEmailVerified',
         });
         throw new Error(error.message);
      }
   }

   async logout(payload: LogoutDetails) {
      const { email_address } = payload;
   }
}
