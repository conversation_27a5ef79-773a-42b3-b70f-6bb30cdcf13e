import { Logger } from 'winston';
import { EntityManager } from 'typeorm';
import { EmailService } from '../EmailService';
import { retrunLeadsEmail } from '../../constants/email-templates';
import { Config } from '../../config/index';

export class FlableService {
    constructor(
        private logger: Logger,
        private emailService: EmailService,
    ) { }
    async sendLeadEmail(
        name: string,
        country: string,
        email: string
    ) {
        try {
            const emailInfo = {
                to: Config.MAIL_SALES || '<EMAIL>',
                subject: 'Flable AI Lead Sign Up',
                text: 'Flable AI Lead Sign Up',
                html: retrunLeadsEmail(
                    name,
                    country,
                    email
                ),
            };
            const result = await this.emailService.sendEmail(emailInfo);
            return result

        } catch (err) {
            const error = err as Error;

            throw new Error(error.message);
        }
    }
}