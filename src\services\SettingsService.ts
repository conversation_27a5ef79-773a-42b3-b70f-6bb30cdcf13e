import {
   AddCompetitorRequest,
   FetchCompetitorsRequest,
   FetchCompetitorsResponse,
   SettingsRequest,
   FetchLanguageRequest,
   FetchLanguageResponse,
   FetchTimezoneRequest,
   FetchTimezoneResponse,
   FetchIndustryOptionsResponse,
   FetchIndustryRequest,
   FetchIndustryResponse,
   Competitors,
   IndustryOptionsBaseModel,
   IndustryBaseModel,
   MetricReport,
   GetReportPayload,
   UpdateReportPayload,
   pauseReportPayload,
   DeleteReportPayload,
   GeneralSettingsResponse,
   GeneralSettingsPayload,
   LanguageTimezonePayload,
   LanguageTimezoneResponse,
   AccountDetailsResponse,
   LoginDetailsResponse,
   ProfileImagePayload,
   FetchFeatureUsagePayload,
   TrackFeatureUsagePayload,
   FeatureUsage,
} from '../types';
import axios from 'axios';
import { Logger } from 'winston';
import { EntityManager } from 'typeorm';
import { Config } from '../config';
import { EmailService } from './EmailService';
import {
   getEmailTemplate,
   convertAgentMarkdownToHtml,
   getLabel,
   getStartDate,
   getUserDate,
   GROUP_BY_RANGE,
   generateXiInput,
} from '../utils/reports-helper';
import { TOP_KPIS } from '../utils/reports-helper';
import { KPI_CATEGORY } from '../utils/reports-helper';
import { KPIService } from './KPIService';
import { FinalAgg, KPIAgg, KPIDetails } from '../types/kpi';
import { timezoneMap } from '../utils/settingsHelper';
import {
   AccountDetailsPayload,
   WeeklyAutoReportFromXiPayload,
} from '../types/settings';
import { DatabaseError } from 'pg';
import { OnboardingService } from './OnboardingService';
import { RedisServiceClass } from '../module/global/redis';
export class SettingsService {
   constructor(
      private entityManager: EntityManager,
      private logger: Logger,
      private emailService: EmailService,
      private kpiService: KPIService,
      private OnboardingService: OnboardingService,
      private redisService: RedisServiceClass,
   ) {}

   private async fetchLanguageTimezone(client_id: string) {
      const languageTimezoneQuery = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.language_and_region WHERE client_id = ($1)`;
      const languageTimezoneDetails: LanguageTimezoneResponse[] =
         await this.entityManager.query(languageTimezoneQuery, [client_id]);

      return languageTimezoneDetails[0];
   }

   private async fetchAccountDetails(client_id: string) {
      const userAccountQuery = `SELECT * FROM ${Config.DB_Postgres_CONFIG_SCHEMA}.user_account WHERE client_id = ($1)`;
      const userAccountDetails: AccountDetailsResponse[] =
         await this.entityManager.query(userAccountQuery, [client_id]);

      return userAccountDetails[0];
   }

   private async fetchLoginDetails(email_address: string) {
      const userLoginQuery = `SELECT * FROM ${Config.DB_Postgres_CONFIG_SCHEMA}.user_login WHERE email_address = ($1)`;
      const userLoginDetails: LoginDetailsResponse[] =
         await this.entityManager.query(userLoginQuery, [email_address]);

      return userLoginDetails[0];
   }

   async getGeneralSettings(
      payload: GeneralSettingsPayload,
   ): Promise<GeneralSettingsResponse> {
      try {
         const { client_id, email_address } = payload;

         const loginDetails = await this.fetchLoginDetails(email_address);
         const accountDetails = await this.fetchAccountDetails(client_id);
         const languageTimezone = await this.fetchLanguageTimezone(client_id);

         const result: GeneralSettingsResponse = {
            client_id: client_id,
            language: languageTimezone?.language ?? '',
            timezone: languageTimezone?.timezone ?? '',
            industry: accountDetails?.company_business_type ?? '',
            currency: accountDetails?.company_currency ?? '',
            annual_revenue: accountDetails?.company_annual_revenue ?? '',
            profile_image: loginDetails?.profile_image ?? '',
            timezone_name: languageTimezone?.timezone_name ?? '',
         };

         return result;
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'getGeneralSettings',
         });
         throw new Error(error.message);
      }
   }

   async updateLanguageTimezone(payload: LanguageTimezonePayload) {
      try {
         const { client_id, language, timezone, timezone_name } = payload;
         const query = `INSERT INTO ${Config.DB_Postgres_SCHEMA}.language_and_region
                        (client_id, language, timezone, timezone_name)
                        VALUES($1, $2, $3, $4)
                        ON CONFLICT (client_id)
                        DO UPDATE SET language = EXCLUDED.language,
                           timezone = EXCLUDED.timezone,
                           timezone_name = EXCLUDED.timezone_name,
                           updated_date = CURRENT_TIMESTAMP`;
         await this.entityManager.query(query, [
            client_id,
            language,
            timezone,
            timezone_name,
         ]);
         this.logger.info(
            `Updated language and timezone for client id: ${client_id}`,
         );

         return {
            status: 'Success',
            message: 'Language and timezone updated successfully',
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'updateLanguageTimezone',
         });
         throw new Error(error.message);
      }
   }

   async updateAccountDetails(payload: AccountDetailsPayload) {
      try {
         const { client_id, annual_revenue, currency, industry } = payload;

         const query = `UPDATE ${Config.DB_Postgres_CONFIG_SCHEMA}.user_account
                        SET company_annual_revenue = $1,
                            company_business_type = $2,
                            company_currency = $3
                        WHERE client_id = $4`;
         await this.entityManager.query(query, [
            annual_revenue,
            industry,
            currency,
            client_id,
         ]);
         this.logger.info(`Updated profile image for client id: ${client_id}`);

         return {
            status: 'Success',
            message: 'Account details updated successfully',
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'updateLanguageTimezone',
         });
         throw new Error(error.message);
      }
   }

   async updateProfileImage(payload: ProfileImagePayload) {
      try {
         const { client_id, email_address, profile_image } = payload;
         const query = `UPDATE ${Config.DB_Postgres_CONFIG_SCHEMA}.user_login
                        SET profile_image = $1
                        WHERE email_address = $2`;
         await this.entityManager.query(query, [profile_image, email_address]);
         this.logger.info(`Updated profile image for client id: ${client_id}`);

         return {
            status: 'Success',
            message: 'Profile image updated successfully',
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'updateProfileImage',
         });
         throw new Error(error.message);
      }
   }

   async verifySocialHandle(payload: { handle: string }): Promise<boolean> {
      const { handle } = payload;
      const response = await axios.get(
         'https://api.builtwith.com/social1/api.json',
         {
            params: {
               KEY: '65818851-d0e0-4816-8d77-650e78b65eb3',
               LOOKUP: handle,
            },
         },
      );
      this.logger.info('Social verify response: ', response?.data);
      // The Socials key contains empty array when no url is found. Using this to validate urls
      return response?.data?.['Socials'].length > 0 || false;
   }

   async getLanguage(
      payload: FetchLanguageRequest,
   ): Promise<FetchLanguageResponse | null> {
      const { client_id } = payload;
      const query = `SELECT language FROM ${Config.DB_Postgres_SCHEMA}.language_and_region
            WHERE client_id = $1`;
      const result: FetchLanguageResponse[] = await this.entityManager.query(
         query,
         [client_id],
      );
      return result[0] ?? null;
   }

   async getTimezone(
      payload: FetchTimezoneRequest,
   ): Promise<FetchTimezoneResponse | null> {
      const { client_id } = payload;
      const query = `SELECT timezone FROM ${Config.DB_Postgres_SCHEMA}.language_and_region
            WHERE client_id = $1`;
      const result: FetchTimezoneResponse[] = await this.entityManager.query(
         query,
         [client_id],
      );
      return result[0] ?? null;
   }

   async getIndustry(
      payload: FetchIndustryRequest,
   ): Promise<IndustryBaseModel | null> {
      const { client_id } = payload;
      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_get_industry_category_by_client_id($1)`;
      const result: FetchIndustryResponse[] = await this.entityManager.query(
         query,
         [client_id],
      );
      return result[0].fn_get_industry_category_by_client_id ?? null;
   }

   async getIndustryOptions(): Promise<IndustryOptionsBaseModel | null> {
      const query = `SELECT * FROM ${Config.DB_Postgres_CONFIG_SCHEMA}.fn_get_industry_categories()`;
      const result: FetchIndustryOptionsResponse[] =
         await this.entityManager.query(query);
      return result[0].fn_get_industry_categories ?? null;
   }

   async updateLanguage(payload: SettingsRequest): Promise<void> {
      const { client_id, language } = payload;
      const query = `INSERT INTO ${Config.DB_Postgres_SCHEMA}.language_and_region
            (client_id, language)
            VALUES($1, $2)
            ON CONFLICT (client_id)
            DO UPDATE SET language = EXCLUDED.language,
               updated_date = CURRENT_TIMESTAMP`;
      await this.entityManager.query(query, [client_id, language]);
      this.logger.info(`Updated language for client id: ${client_id}`);
      return;
   }

   async updateTimezone(payload: SettingsRequest): Promise<void> {
      const { client_id, timezone } = payload;
      const timezone_name: string = timezoneMap[timezone];

      const query = `INSERT INTO ${Config.DB_Postgres_SCHEMA}.language_and_region
            (client_id, timezone, timezone_name)
            VALUES($1, $2, $3)
            ON CONFLICT (client_id)
            DO UPDATE SET timezone = EXCLUDED.timezone,
               timezone_name = EXCLUDED.timezone_name,
               updated_date = CURRENT_TIMESTAMP`;
      await this.entityManager.query(query, [
         client_id,
         timezone,
         timezone_name,
      ]);
      this.logger.info(`Updated timezone for client id: ${client_id}`);
      return;
   }

   async updateIndustry(payload: SettingsRequest): Promise<void> {
      const { client_id, industry, category } = payload;
      const jsonInput = JSON.stringify({
         client_id,
         Industry: industry,
         category,
      });
      const query = `CALL ${Config.DB_Postgres_SCHEMA}.q_upsert_client_industry($1::jsonb)`;
      await this.entityManager.query(query, [jsonInput]);
      this.logger.info(`Updated industry for client id: ${client_id}`);
      return;
   }

   async getCompetitors(
      payload: FetchCompetitorsRequest,
   ): Promise<Competitors[]> {
      const { client_id, channel } = payload;
      const query = `SELECT competitor_handles FROM ${Config.DB_Postgres_SCHEMA}.competitors
            WHERE client_id = $1
            AND channel = $2`;
      const result: FetchCompetitorsResponse[] = await this.entityManager.query(
         query,
         [client_id, channel],
      );
      return result[0]?.competitor_handles ?? [];
   }

   async addCompetitors(payload: AddCompetitorRequest): Promise<void> {
      const { client_id, channel, competitor_handles } = payload;
      const query = `INSERT INTO ${Config.DB_Postgres_SCHEMA}.competitors
            (client_id, channel, competitor_handles)
            VALUES($1, $2, $3)
            ON CONFLICT (client_id, channel)
            DO UPDATE SET competitor_handles = EXCLUDED.competitor_handles,
               updated_date = CURRENT_TIMESTAMP`;
      await this.entityManager.query(query, [
         client_id,
         channel,
         JSON.stringify(competitor_handles),
      ]);
      this.logger.info(`Updated competitor for client id: ${client_id}`);
      return;
   }
   async getWeeklyAutoReportFromXi(payload: {
      clientId: string;
      userId: string;
      activeCategories: string[];
   }): Promise<string> {
      const { clientId, userId, activeCategories } = payload;

      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayDateStr = yesterday.toISOString().split('T')[0];
      const xiPayload: WeeklyAutoReportFromXiPayload = {
         client_id: clientId,
         context_variables: {},
         message_history: [],
         session_id: '*********',
         text: generateXiInput(yesterdayDateStr, activeCategories),
         user_id: userId,
         mode: 'cmo',
      };

      const redisKey = `weekly_auto_report:${clientId}`;

      const cached = await this.redisService.get(redisKey);
      if (cached) {
         this.logger.info(`Cache hit for key: ${redisKey}`);
         return cached;
      }

      const apiUrl = `${Config.VITE_XI_AGENT_API}/query_stream`;

      let cmoFinalResult: string | null = null;
      let attempts = 0;
      const maxAttempts = 1;
      //let lastChunk: any = null;
      let apiResponse;

      while (attempts < maxAttempts && !cmoFinalResult) {
         try {
            apiResponse = await axios.post(apiUrl, xiPayload, {
               headers: { 'Content-Type': 'application/json' },
            });

            let dataArr: any[] = [];
            if (typeof apiResponse.data === 'string') {
               dataArr = apiResponse.data
                  .split('\n')
                  .filter((line) => line.trim() !== '')
                  .map((line) => {
                     try {
                        return JSON.parse(line);
                     } catch (e) {
                        console.error('Failed to parse line as JSON:', line, e);
                        return null;
                     }
                  })
                  .filter((obj) => obj !== null);
            } else if (Array.isArray(apiResponse.data)) {
               dataArr = apiResponse.data;
            } else {
               dataArr = [];
            }

            if (Array.isArray(dataArr) && dataArr.length > 0) {
               for (let i = dataArr.length - 1; i >= 0; i--) {
                  const chunk = dataArr[i];

                  if (chunk?.type === 'final_result') {
                     cmoFinalResult = chunk.content;
                     break;
                  }
               }

               break;
            }
         } catch (err) {
            this.logger.error(
               'Error fetching weekly auto report from external API',
               { error: err, attempt: attempts + 1 },
            );
         }
         attempts++;
      }

      if (!cmoFinalResult) {
         this.logger.error(
            'Failed to get final result from external API after retries',
         );
         throw new Error('Failed to fetch valid weekly auto report');
      }

      await this.redisService.set(redisKey, cmoFinalResult as string, {
         ttlInSeconds: 6 * 60 * 60,
      });
      this.logger.info(
         `Stored weekly auto report in Redis for key: ${redisKey}`,
      );

      return cmoFinalResult as string;
   }
   async sendMail(payload: MetricReport): Promise<string> {
      const {
         date,
         frequency,
         clientId,
         emails,
         kpis,
         title,
         is_auto_report,
         is_subscribed,
      } = payload;
      const start = getStartDate(date, frequency);
      const kpiData = await this.kpiService.getKpiData({
         clientId: clientId,
         compareBy: frequency,
         startDate: start,
         endDate: new Date(
            new Date(date).setHours(23, 59, 59, 999),
         ).toISOString(),
         prevStartDate: '',
         prevEndDate: '',
      });

      const prevEnd = new Date(new Date(start).setHours(23, 59, 59, 999));

      prevEnd.setDate(prevEnd.getDate() - 1);
      const prevkpiData = await this.kpiService.getKpiData({
         clientId: clientId,
         compareBy: frequency,
         startDate: getStartDate(start, frequency, -1),
         endDate: prevEnd.toISOString(),
         prevStartDate: '',
         prevEndDate: '',
      });
      const finalKpiData: Record<string, KPIAgg> = {};
      const prevFinalKpiData: Record<string, KPIAgg> = {};
      for (const { kpi, category } of kpis) {
         if (finalKpiData[category]) {
            if (kpiData[category] && (kpiData[category] as KPIAgg)[kpi])
               finalKpiData[category][kpi] = (kpiData[category] as KPIAgg)[kpi];
         } else {
            finalKpiData[category] = {};
            if (kpiData[category] && (kpiData[category] as KPIAgg)[kpi])
               finalKpiData[category][kpi] = (kpiData[category] as KPIAgg)[kpi];
         }
         if (prevFinalKpiData[category]) {
            if (prevkpiData[category] && (prevkpiData[category] as KPIAgg)[kpi])
               prevFinalKpiData[category][kpi] = (
                  prevkpiData[category] as KPIAgg
               )[kpi];
         } else {
            prevFinalKpiData[category] = {};
            if (prevkpiData[category] && (prevkpiData[category] as KPIAgg)[kpi])
               prevFinalKpiData[category][kpi] = (
                  prevkpiData[category] as KPIAgg
               )[kpi];
         }
      }

      const startLabel =
         frequency == 'day'
            ? getUserDate(new Date(date), true)
            : `${getLabel({ startDate: new Date(start), endDate: new Date(date) })}`;
      const endLabel =
         frequency == 'day'
            ? getUserDate(new Date(getStartDate(start, frequency, -1)), true)
            : `${getLabel({ startDate: new Date(getStartDate(start, frequency, -1)), endDate: prevEnd })}`;

      const redisKey = `weekly_auto_report:${clientId}`;

      const cached = await this.redisService.get(redisKey);

      let weeklyAutoReportHtml = cached;

      const info = this.emailService.sendEmail({
         to: emails,
         subject: `${GROUP_BY_RANGE[frequency]} Performance Report of ${title} - Flable AI`,
         text: '',
         html: cached
            ? convertAgentMarkdownToHtml(weeklyAutoReportHtml as string)
            : getEmailTemplate(
                 finalKpiData,
                 prevFinalKpiData,
                 payload,
                 startLabel,
                 endLabel,
              ),
      });

      return info;
   }

   async createReport(payload: MetricReport): Promise<void> {
      const query = `CALL ${Config.DB_Postgres_SCHEMA}.q_insert_email_report($1::JSONB)`;
      await this.entityManager.query(query, [
         JSON.stringify({
            client_id: payload.clientId,
            user_id: payload.user_id,
            title: payload.title,
            frequency: payload.frequency,
            interval: payload.interval,
            start_date: payload.start_date.split('T')[0],
            end_date: payload.end_date.split('T')[0],
            date: payload.date.split('T')[0],
            time: payload.time,
            emails: payload.emails,
            kpis: payload.kpis,
            username: payload.username,
            is_auto_report: payload.is_auto_report,
            is_subscribed: payload.is_subscribed,
         }),
      ]);
      return;
   }

   async getAllReports(payload: GetReportPayload): Promise<MetricReport[]> {
      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_get_email_reports_by_client($1,$2)`;
      const reports = await this.entityManager.query(query, [
         payload.clientId,
         payload.userId,
      ]);
      return reports[0]?.fn_get_email_reports_by_client as MetricReport[];
   }

   async updateReport(payload: UpdateReportPayload): Promise<void> {
      const query = `CALL ${Config.DB_Postgres_SCHEMA}.q_update_email_report($1)`;
      await this.entityManager.query(query, [
         JSON.stringify({
            client_id: payload.clientId,
            user_id: payload.user_id,
            title: payload.title,
            frequency: payload.frequency,
            interval: payload.interval,
            start_date: payload.start_date.split('T')[0],
            end_date: payload.end_date.split('T')[0],
            date: payload.date.split('T')[0],
            time: payload.time,
            emails: payload.emails,
            kpis: payload.kpis,
            report_id: payload.reportId,

            username: payload.username,
         }),
      ]);
      return;
   }
   async pauseAutoReport(payload: pauseReportPayload): Promise<void> {
      const query = `
         UPDATE ${Config.DB_Postgres_SCHEMA}.email_report
         SET is_subscribed = $1,
             updated_at = CURRENT_TIMESTAMP
         WHERE report_id = $2
           AND client_id = $3
      `;

      await this.entityManager.query(query, [
         payload.isSubscribed,
         payload.reportId,
         payload.clientId,
      ]);
   }
   async deleteReport(payload: DeleteReportPayload): Promise<void> {
      const query = `CALL ${Config.DB_Postgres_SCHEMA}.q_delete_email_report($1)`;
      await this.entityManager.query(query, [
         JSON.stringify({
            client_id: payload.clientId,
            report_id: payload.reportId,
         }),
      ]);
      return;
   }

   async getMetaDataForAutoReport(clientId: string): Promise<any> {
      const connDetails = await this.OnboardingService.getUserSocialDetails({
         client_id: clientId,
      });

      const channArr = connDetails.response.details;

      const categoryMappings: Record<string, KPI_CATEGORY> = {
         facebookads: 'facebookads',
         flable_pixel: 'web',
         shopify: 'store',
         googleads: 'googleads',
         amazon_ads: 'amazon_ads',
         amazon_selling_partner: 'amazon_selling_partner',
      };

      //  Get active categories (excluding overall_metrics)
      const activeCategories: KPI_CATEGORY[] = Object.entries(categoryMappings)
         .filter(([channelName]) => {
            const channelData = channArr.find(
               (x) => x.channel_name === channelName,
            );
            return channelData && channelData.is_active;
         })
         .map(([, category]) => category);

      //  Add overall_metrics only if store is active
      if (activeCategories.includes('store')) {
         activeCategories.push('overall_metrics');
      }

      //  Get KPIs for all active categories
      const activeKpis = activeCategories.flatMap(
         (category) => TOP_KPIS[category] || [],
      );

      return {
         activeCategories,
         activeKpis,
      };
   }

   async saveMetaAdsCredentials(payload: {
      client_id: string;
      meta_access_token: string;
      page_id: string;
      ad_account_id: string;
   }): Promise<void> {
      try {
         const { client_id, meta_access_token, page_id, ad_account_id } =
            payload;
         const query = `INSERT INTO agents.temp_meta_ads_manager_cred
            (client_id, meta_access_token, page_id,ad_account_id)
            VALUES($1, $2, $3, $4)
            ON CONFLICT (client_id)
            DO UPDATE SET 
               meta_access_token = EXCLUDED.meta_access_token,
               page_id = EXCLUDED.page_id,
               ad_account_id = EXCLUDED.ad_account_id`;

         await this.entityManager.query(query, [
            client_id,
            meta_access_token,
            page_id,
            ad_account_id,
         ]);
         this.logger.info(
            `Saved Meta Ads credentials for client id: ${client_id}`,
         );
         return;
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'saveMetaAdsCredentials',
         });
         throw new Error(error.message);
      }
   }

   async getMetaAdsCredentials(client_id: string): Promise<{
      meta_access_token: string;
      page_id: string;
      ad_account_id: string;
   } | null> {
      try {
         const query = `SELECT meta_access_token, page_id, ad_account_id
            FROM agents.temp_meta_ads_manager_cred
            WHERE client_id = $1`;

         const result = await this.entityManager.query(query, [client_id]);
         return result[0] || null;
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'getMetaAdsCredentials',
         });
         throw new Error(error.message);
      }
   }

   async fetchFeatureUsage(
      payload: FetchFeatureUsagePayload,
   ): Promise<FeatureUsage[]> {
      try {
         const { client_id, user_id, feature_name, feature_type } = payload;

         const query = `
                  SELECT * FROM ${Config.DB_Postgres_CONFIG_SCHEMA}.client_feature_usage_tracker
                  WHERE client_id = $1
                  AND user_id = $2
                  AND feature_name = $3
                  AND feature_type = $4;
               `;

         const result: FeatureUsage[] = await this.entityManager.query(query, [
            client_id,
            user_id,
            feature_name,
            feature_type,
         ]);

         return result || {};
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async trackFeatureUsage(payload: TrackFeatureUsagePayload): Promise<void> {
      try {
         const { client_id, user_id, feature_name, feature_type, mode } =
            payload;

         const updatedMode = mode ?? '';

         const USAGE_LIMITS: Record<string, number> = {
            data_analyst: 100,
            cmo: 20,
         };

         const insertQuery = `
         INSERT INTO ${Config.DB_Postgres_CONFIG_SCHEMA}.client_feature_usage_tracker
            (client_id, user_id, feature_name, feature_type, mode, no_of_calls)
         VALUES ($1, $2, $3, $4, $5, 1)
         ON CONFLICT (client_id, user_id, feature_name, feature_type, mode)
         DO UPDATE SET no_of_calls = ${Config.DB_Postgres_CONFIG_SCHEMA}.client_feature_usage_tracker.no_of_calls + 1
         RETURNING no_of_calls;
      `;

         const [{ no_of_calls }] = await this.entityManager.query(insertQuery, [
            client_id,
            user_id,
            feature_name,
            feature_type,
            updatedMode,
         ]);

         if (feature_name === 'analytics_agent' && mode && USAGE_LIMITS[mode]) {
            const limit = USAGE_LIMITS[mode];

            if (no_of_calls >= limit) {
               const updateQuery = `
               UPDATE ${Config.DB_Postgres_CONFIG_SCHEMA}.client_feature_usage_tracker
               SET free_limit_expired = true, is_enabled = false
               WHERE client_id = $1
                 AND user_id = $2
                 AND feature_name = $3
                 AND feature_type = $4
                 AND mode = $5;
            `;

               await this.entityManager.query(updateQuery, [
                  client_id,
                  user_id,
                  feature_name,
                  feature_type,
                  updatedMode,
               ]);
            }
         }
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }
}
