import {
   <PERSON>Agg,
   DateAgg,
   FinalAgg,
   GPTData,
   GPTResponse,
   KPIAgg,
   KPIData,
   KPIDetails,
   KPIKeys,
   KPIMetaRes,
   KPIMetaPayload,
   KPIPayload,
   PinPayload,
   PinVisibleResponse,
   VisblePayload,
   KPIMeta,
   PinVisibleOrderPayload,
   BreakDown,
   KPIAnomaly,
   KPIAnomalyPayload,
   KPIAnomalyCausePayload,
   AnomalyCause,
} from '@/types/kpi';
import fsPromise from 'fs/promises';
import path from 'path';
import { Config } from '../config/index';
import axios from 'axios';
import {
   AVG_KPI_BY_NON_ZERO,
   DAYS_IN,
   DEFAULT_KPI_AGG,
   GPT,
} from '../constants/index';
import { KPI_CALCULATE } from '../utils/kpiHelpers';
import { EntityManager } from 'typeorm';
import { Logger } from 'winston';
import { OnboardingService } from './OnboardingService';
import { callDiagnostics } from '../utils/CallDiagnostics';
import { RedisServiceClass } from '../module/global/redis';
export class KPIService {
   constructor(
      private entityManager: EntityManager,
      private logger: Logger,
      private OnboardingService: OnboardingService,
      private redisService: RedisServiceClass,
   ) {}
   // private async readKPIDataFromCache(clientId: string): Promise<KPIData[]> {
   //    const filePath = path.join(
   //       __dirname,
   //       Config.KPI_DATA_PATH + `${clientId}_KPI.json`,
   //    );
   //    const fileExist = await this.checkFileExists(filePath);
   //    if (!fileExist) {
   //       this.logger.info(`File ${filePath} doesn't exist`);
   //       return [];
   //    }
   //    const rawdata = await fsPromise.readFile(filePath, { encoding: 'utf8' });
   //    const data = JSON.parse(rawdata) as KPIData[];
   //    return data;
   // }
   async updatePinned(payload: PinPayload): Promise<string> {
      const { clientId, category, kpis, pinned } = payload;
      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_update_kpi_visible_and_pins($1, $2, $3::varchar[], $4, $5)`;
      const result = await this.entityManager.query(query, [
         clientId,
         category,
         [...kpis],
         null,
         pinned,
      ]);
      return result[0].fn_update_kpi_visible_and_pins;
   }
   async updateVisible(payload: VisblePayload): Promise<string> {
      const { clientId, category, kpis, visible } = payload;
      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_update_kpi_visible_and_pins($1, $2, $3::varchar[], $4, $5)`;
      const result = await this.entityManager.query(query, [
         clientId,
         category,
         [...kpis],
         visible,
         null,
      ]);
      return result[0].fn_update_kpi_visible_and_pins;
   }
   async updatePinVisibleOrder(
      payload: PinVisibleOrderPayload,
   ): Promise<string> {
      const jsonInput = JSON.stringify({
         client_id: payload.clientId,
         pin_order: payload.pinOrder,
         kpi_data: payload.kpiOrder,
      });
      const query = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_update_kpis_order($1::jsonb)`;
      const result = await this.entityManager.query(query, [jsonInput]);
      return result[0].fn_update_kpis_order as string;
   }
   async getMeta(payload: KPIMetaPayload): Promise<KPIMeta[]> {
      const { clientId } = payload;
      const q = `SELECT * FROM ${Config.DB_Postgres_SCHEMA}.fn_kpi_get_meta($1)`;
      const result = await this.entityManager.query(q, [clientId]);
      const filteredData = result[0].fn_kpi_get_meta;
      if (!filteredData) return [];
      const { data } = await this.getActiveChannelData(
         filteredData as KPIMeta[],
         clientId,
      );
      return data as KPIMeta[];
   }
   private filterKpiDataByDates(
      jsonData: KPIData[],
      start: string,
      end: string,
   ): KPIData[] {
      const startDate = new Date(start);
      const endDate = new Date(end);

      // Filter the data by date range
      const filteredData = jsonData.filter((item) => {
         const itemDate = new Date(item.date);
         if (isNaN(item.kpi_value)) item.kpi_value = 0;
         return itemDate >= startDate && itemDate <= endDate;
      });
      return filteredData;
   }

   async getActiveChannelData(
      data: (KPIData | KPIMeta)[],
      clientId: string,
      key?: boolean,
   ): Promise<{ data: (KPIData | KPIMeta)[]; aggData: FinalAgg }> {
      const aggData = {
         web: {},
         googleads: {},
         facebookads: {},
         store: {},
         amazon_selling_partner: {},
         amazon_ads: {},
         overall_metrics: {},
      } as FinalAgg;
      const connDetails = await this.OnboardingService.getUserSocialDetails({
         client_id: clientId,
      });
      const channArr = connDetails.response.details;

      const facebookads = channArr.find((x) => x.channel_name == 'facebookads');
      const web = channArr.find((x) => x.channel_name == 'flable_pixel');
      const shopify = channArr.find((x) => x.channel_name == 'shopify');
      const googleads = channArr.find((x) => x.channel_name == 'googleads');
      const amazonads = channArr.find((x) => x.channel_name == 'amazon_ads');
      const amazon_selling_partner = channArr.find(
         (x) => x.channel_name == 'amazon_selling_partner',
      );
      const overall_metrics = channArr.find(
         (x) => x.channel_name == 'overall_metrics',
      );
      if (!(facebookads && facebookads.is_active)) {
         data = this.filterDataByCategory(data, 'facebookads');
         delete aggData['facebookads'];
      }
      if (!(shopify && shopify.is_active)) {
         data = this.filterDataByCategory(data, 'store');
         delete aggData['store'];
         // Delete overall metrics if store is not active
         data = this.filterDataByCategory(data, 'overall_metrics');
         delete aggData['overall_metrics'];
      }
      if (!(googleads && googleads.is_active)) {
         data = this.filterDataByCategory(data, 'googleads');
         delete aggData['googleads'];
      }
      if (!(amazonads && amazonads.is_active)) {
         data = this.filterDataByCategory(data, 'amazon_ads');
         delete aggData['amazon_ads'];
      }
      if (!(amazon_selling_partner && amazon_selling_partner.is_active)) {
         data = this.filterDataByCategory(data, 'amazon_selling_partner');
         delete aggData['amazon_selling_partner'];
      }
      //   if (!overall_metrics) {
      //    data = this.filterDataByCategory(data, 'overall_metrics');
      //    delete aggData['overall_metrics']
      //   }
      if (!(web && web.is_active)) {
         data = this.filterDataByCategory(data, 'web');
         delete aggData['web'];
      }
      return { data, aggData };
   }
   async getKpiData(payload: KPIPayload): Promise<FinalAgg> {
      const {
         clientId,
         startDate,
         endDate,
         prevStartDate,
         prevEndDate,
         compareBy,
      } = payload;

      const cacheKey = `dashboard:KPIs:${clientId}:${startDate.split('T')[0]}-${endDate.split('T')[0]}: ${prevStartDate.split('T')[0]}-${prevEndDate.split('T')[0]}:${compareBy}`;
      const cacheKeyData = `cache:kpiData:${clientId}`;
      const cached = await this.redisService.get(cacheKey);
      if (cached) {
         return JSON.parse(cached) as FinalAgg;
      }
      
      const cachedDataStr = await this.redisService.get(cacheKeyData);
      const cachedData = cachedDataStr ? (JSON.parse(cachedDataStr) as KPIData[]) : [];

      const filteredData = this.filterKpiDataByDates(
         cachedData,
         startDate,
         endDate,
      );

      const { data, aggData } = await this.getActiveChannelData(
         filteredData,
         clientId,
      );

      const KPIAnomalyPayload = {
         client_id: clientId,
         start_date: startDate.split('T')[0],
         end_date: endDate.split('T')[0],
         prev_start_date: prevStartDate && prevStartDate.split('T')[0],
         prev_end_date: prevEndDate && prevEndDate.split('T')[0],
      };

      const anomalyData = await callDiagnostics<KPIAnomaly, KPIAnomalyPayload>(
         'get-anomaly-data',
         KPIAnomalyPayload,
      );
      const finalData = this.getAggData(data as KPIData[], compareBy, aggData);

      if (anomalyData) {
         finalData['anomaly'] = anomalyData;
      }

      await this.redisService.set(cacheKey, JSON.stringify(finalData), {
         ttlInSeconds: 60 * 60 * 12, // Cache for 12 hours
      });
      return finalData;
   }

   private filterDataByCategory(
      data: (KPIData | KPIMeta)[],
      channel: string,
   ): (KPIData | KPIMeta)[] {
      return data.filter((x) => x.category !== channel);
   }

   private groupByDate(data: KPIData[]): DateAgg {
      const dateAgg: DateAgg = {};
      data.forEach((x) => {
         const dateKey: string = x.date.split('T')[0];
         if (dateAgg[dateKey]) {
            dateAgg[dateKey] = {
               ...dateAgg[dateKey],
               [x.kpi_names]: x.kpi_value,
            };
         } else {
            dateAgg[dateKey] = { [x.kpi_names]: x.kpi_value };
         }
      });
      return dateAgg;
   }

   private calculateKPI(kpiAgg: KPIAgg, wholeData?: KPIData[]): KPIAgg {
      const allKPIData = Object.values(kpiAgg).reduce(
         (finalDat: KPIData[], kpiData: KPIDetails) => {
            return finalDat.concat(kpiData.allData);
         },
         [],
      );

      const dateAgg = this.groupByDate(allKPIData);

      for (const kpi in kpiAgg) {
         const kpiKey: string = kpi;

         if (KPI_CALCULATE[kpiKey as KPIKeys]) {
            kpiAgg[kpi].totalVal = KPI_CALCULATE[kpiKey as KPIKeys](dateAgg);
         } else if (AVG_KPI_BY_NON_ZERO.includes(kpi)) {
            kpiAgg[kpi].totalVal =
               kpiAgg[kpi].allData.reduce(
                  (prev, curr) => prev + curr.kpi_value,
                  0,
               ) / kpiAgg[kpi].allData.filter((kpi) => kpi.kpi_value).length;
         }

         if (isNaN(kpiAgg[kpi].totalVal) || kpiAgg[kpi].totalVal == Infinity)
            kpiAgg[kpi].totalVal = 0;
      }

      return kpiAgg;
   }

   private aggBreakdown = (
      data: BreakDown | null | undefined,
      kpiAggBreak: BreakDown | null | undefined,
      kpi: string,
   ): void => {
      if (!data || !data[kpi]) return;

      if (data?.[0]?.kpi_display_name === 'Blended Returns') console.log(data);

      // Ensure kpiAggBreak is not null or undefined
      kpiAggBreak = kpiAggBreak ?? {};

      // Ensure kpiAggBreak[kpi] is initialized
      if (!kpiAggBreak[kpi]) {
         kpiAggBreak[kpi] = { ...data[kpi], sub_items: {} };
      } else {
         kpiAggBreak[kpi].value += data[kpi].value;
      }

      // Ensure sub_items exists before accessing
      kpiAggBreak[kpi].sub_items = kpiAggBreak[kpi].sub_items ?? {};

      const kpiBreakItems = data[kpi]?.sub_items;

      if (!kpiBreakItems) return;

      Object.entries(kpiBreakItems).forEach(([key, value]) => {
         if (!value) return;

         // Recursive call for sub_items if they exist
         if (value.sub_items && Object.keys(value.sub_items).length > 0) {
            this.aggBreakdown(kpiBreakItems, kpiAggBreak![kpi].sub_items, key);
         } else {
            // Ensure sub_items exist before modifying them
            kpiAggBreak![kpi].sub_items![key] = kpiAggBreak![kpi].sub_items![
               key
            ] ?? { ...value, sub_items: {} };

            kpiAggBreak![kpi].sub_items![key].value += value.value;
         }
      });
   };

   private getKpiAgg = (data: KPIData[]): KPIAgg => {
      const kpiAgg = {} as KPIAgg;

      data.forEach((x) => {
         if (kpiAgg[x.kpi_names]) {
            kpiAgg[x.kpi_names].totalVal += x.kpi_value;
            kpiAgg[x.kpi_names].allData.push(x);

            this.aggBreakdown(
               x.daily_breakdown,
               kpiAgg[x.kpi_names].daily_breakdown,
               x.kpi_names,
            );
         } else {
            kpiAgg[x.kpi_names] = {
               totalVal: x.kpi_value,
               unit: x.kpi_unit,
               displayName: x.kpi_display_name,
               allData: [x],
               ...x,
               daily_breakdown: JSON.parse(JSON.stringify(x.daily_breakdown)),
            };
         }
      });

      return kpiAgg;
   };

   private getCategoryKPI = (
      data: KPIData[],
      groupBy: string,
      wholeData: KPIData[],
   ): KPIAgg => {
      let kpiAgg = this.getKpiAgg(data);

      if (data[0].category === 'web') {
         const total_orders = wholeData.filter(
            (x) => x.kpi_names === 'total_orders' && x.category === 'store',
         );

         const meta_spend = wholeData.filter(
            (x) =>
               x.kpi_names === 'total_spent' && x.category === 'facebookads',
         );

         const google_spend = wholeData.filter(
            (x) =>
               x.kpi_names === 'google_total_spend' &&
               x.category === 'googleads',
         );

         const amazon_spend = wholeData.filter(
            (x) =>
               x.kpi_names === 'amazon_ads_spent' &&
               x.category === 'amazon_ads',
         );

         const orderagg = this.getKpiAgg(total_orders);
         const meta_spend_agg = this.getKpiAgg(meta_spend);
         const google_spend_agg = this.getKpiAgg(google_spend);
         const amazon_spend_agg = this.getKpiAgg(amazon_spend);

         kpiAgg = {
            ...kpiAgg,
            ...(orderagg ? orderagg : {}),
            ...(meta_spend_agg ? meta_spend_agg : {}),
            ...(google_spend_agg ? google_spend_agg : {}),
            ...(amazon_spend_agg ? amazon_spend_agg : {}),
         };
      }

      if (groupBy == 'day') {
         const result = this.calculateKPI(kpiAgg, wholeData);

         if (data[0].category === 'web') {
            delete result['total_orders'];
            delete result['total_spent'];
            delete result['google_total_spend'];
            delete result['amazon_ads_spent'];
         }

         if (result) return result;
      }

      const groupedData = this.splitDataByWeek(data, groupBy);

      for (const kpi in kpiAgg) {
         kpiAgg[kpi].allData = [];
      }

      for (const group in groupedData) {
         const grpKpiAgg = this.calculateKPI(
            this.getKpiAgg(groupedData[group]),
         );

         for (const kpi in grpKpiAgg) {
            if (kpiAgg[kpi]) {
               const newKpi: KPIData = grpKpiAgg[kpi].allData[0];
               newKpi.kpi_value = grpKpiAgg[kpi].totalVal || 0;
               newKpi.date = group;
               kpiAgg[kpi].allData.unshift(newKpi);
            }
         }
      }

      const result = this.calculateKPI(kpiAgg);

      if (data[0].category === 'web') {
         delete result['total_orders'];
         delete result['total_spent'];
         delete result['google_total_spend'];
         delete result['amazon_ads_spent'];
      }

      return result;
   };

   private splitDataByWeek(data: KPIData[], groupBy: string) {
      // Map to group data by week
      const result: {
         [key: string]: KPIData[];
      } = {};
      let currentWeek: KPIData[] = [];
      let currentEndofWeek = data[data.length - 1]; // Get the end of the day of the week
      let currentstartofWeek =
         new Date(currentEndofWeek.date).getTime() -
         (DAYS_IN[groupBy] || 7) * 24 * 60 * 60 * 1000;
      data.reverse().forEach((item) => {
         const itemDate = new Date(item.date);

         if (itemDate.getTime() > currentstartofWeek) {
            // If the item is in the current week, add it to the current week's array
            currentWeek.push(item);
         } else {
            // Push the completed week data to the result array and start a new week
            const key = `${currentWeek[currentWeek.length - 1].date.split('T')[0]} - ${currentWeek[0].date.split('T')[0]}`;
            result[key] = currentWeek;
            currentWeek = [item];
            currentEndofWeek = item;
            currentstartofWeek =
               new Date(currentEndofWeek.date).getTime() -
               (DAYS_IN[groupBy] || 7) * 24 * 60 * 60 * 1000;
         }
      });

      if (currentWeek.length > 0) {
         const key = `${currentWeek[currentWeek.length - 1].date.split('T')[0]} - ${currentWeek[0].date.split('T')[0]}`;
         result[key] = currentWeek;
      }
      return result;
   }

   private getAggData = (
      data: KPIData[] | undefined,
      groupBy: string,
      finalAgg: FinalAgg,
   ): FinalAgg => {
      const aggCategoryData = {} as CategoryAgg;

      if (!data || !data.length) {
         return finalAgg;
      }

      data.forEach((x) => {
         x.category = x.category.trim();

         if (aggCategoryData[x.category]) {
            aggCategoryData[x.category].push(x);
         } else {
            aggCategoryData[x.category] = [x];
         }
      });

      for (const cat in aggCategoryData) {
         finalAgg[cat] = this.getCategoryKPI(
            aggCategoryData[cat],
            groupBy,
            data,
         );
      }

      return finalAgg;
   };

   async getGPTSummary(
      payload: GPTData[],
      kpi: string,
      category: string,
      currency: string,
   ): Promise<GPTResponse> {
      const query = GPT.KPISummary(payload, kpi, category, currency);
      const gptV = GPT.GPTVersion;
      const url = `${Config.GPT_URL}/get_chat_response`;
      const res = await this.callGPT(query, gptV, url);
      return res;
   }

   private async callGPT(
      query: string,
      gptVersion: string,
      url: string,
   ): Promise<GPTResponse> {
      const headers = {
         Authorization: `Bearer ${Config.GPT_BEARER}`,
         'Content-Type': 'application/json',
      };
      const data = {
         userask: query,
         gpt_version: gptVersion,
      };

      const response = await axios.post(url, data, { headers });
      return response.data as GPTResponse;
   }

   async checkFileExists(filePath: string) {
      try {
         await fsPromise.access(filePath);
         return true;
      } catch (err) {
         return false;
      }
   }

   async getAnomalyRootCause(
      client_id: string,
      start_date: string,
      end_date: string,
      kpi: string,
   ): Promise<AnomalyCause | null> {
      const KPIAnomalyPayload: KPIAnomalyCausePayload = {
         client_id: client_id,
         start_date: start_date,
         end_date: end_date,
         kpi: kpi,
      };
      const anomalyCause = await callDiagnostics<
         AnomalyCause,
         KPIAnomalyCausePayload
      >('get_data', KPIAnomalyPayload);

      return anomalyCause;
   }
}
