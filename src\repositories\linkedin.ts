import { logger } from '../config/logger';
import axios from 'axios';

interface Media {
   status: string;
   description: {
      text: string;
   };
   media: string;
   title: {
      text: string;
   };
}
interface LinkedinProfile {
   id: string;
   localizedFirstName: string;
}
interface RolesList {
   role: string;
   organizationalTarget: string;
}
interface OrganisationList {
   results: object;
}

async function postOnLinkedin(
   content: string,
   token: string,
   userId: string,
   pageId: string | null,
   mediaId?: string,
): Promise<string | null> {
   const media: Media[] = [];

   if (mediaId) {
      const mediaIds = mediaId.split(',');

      mediaIds.forEach((mediaId) => {
         media.push({
            status: 'READY',
            description: {
               text: 'Image description',
            },
            media: mediaId,
            title: {
               text: 'Image title',
            },
         });
      });
   }
   const author = pageId
      ? `urn:li:organization:${pageId}`
      : `urn:li:person:${userId}`;
   const payload = {
      author: author,
      lifecycleState: 'PUBLISHED',
      specificContent: {
         'com.linkedin.ugc.ShareContent': {
            shareCommentary: {
               text: content,
            },
            shareMediaCategory: mediaId ? 'IMAGE' : 'NONE',
            media,
         },
      },
      visibility: {
         'com.linkedin.ugc.MemberNetworkVisibility': 'PUBLIC',
      },
   };

   const url = 'https://api.linkedin.com/v2/ugcPosts';

   try {
      const { data } = await axios.post<{ id: string }>(url, payload, {
         headers: {
            Authorization: `Bearer ${token}`,
         },
      });

      return data.id;
   } catch (err) {
      logger.error('Failed to post on linkedin ' + String(err));
      return null;
   }
}

interface RegisterUploadResponse {
   value: {
      uploadMechanism: {
         'com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest': {
            uploadUrl: string;
         };
      };
      asset: string;
   };
}

async function uploadImageToLinkedin(
   imageBuffer: Express.Multer.File,
   token: string,
   userId: string,
): Promise<string | null> {
   const uploadUrlFirst =
      'https://api.linkedin.com/v2/assets?action=registerUpload';

   const payload = {
      registerUploadRequest: {
         owner: `urn:li:person:${userId}`,
         recipes: ['urn:li:digitalmediaRecipe:feedshare-image'],
         serviceRelationships: [
            {
               identifier: 'urn:li:userGeneratedContent',
               relationshipType: 'OWNER',
            },
         ],
         supportedUploadMechanism: ['SYNCHRONOUS_UPLOAD'],
      },
   };

   try {
      const { data } = await axios.post<RegisterUploadResponse>(
         uploadUrlFirst,
         payload,
         {
            headers: {
               Authorization: `Bearer ${token}`,
               'Content-Type': 'application/json',
            },
         },
      );

      const uploadMechanism =
         data.value.uploadMechanism[
            'com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest'
         ];
      const uploadUrl = uploadMechanism.uploadUrl;
      const asset = data.value.asset;

      // const imageBuffer = await fs.readFile(imagePath);

      await axios.put(uploadUrl, imageBuffer.buffer, {
         headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/octet-stream',
         },
      });

      return asset;
   } catch (err) {
      logger.error('Failed to upload image to LinkedIn ' + String(err));
      return null;
   }
}

async function fetchUserDetails(
   token: string,
): Promise<LinkedinProfile | null> {
   const url = 'https://api.linkedin.com/v2/me';

   try {
      const { data } = await axios.get<LinkedinProfile>(url, {
         headers: {
            Authorization: `Bearer ${token}`,
         },
      });

      return data;
   } catch (err) {
      logger.error('Failed to fetch user details from linkedin ' + String(err));
      return null;
   }
}

async function getUserOrganisations(token: string): Promise<object[] | null> {
   const url =
      'https://api.linkedin.com/v2/organizationalEntityAcls?q=roleAssignee';

   try {
      const { data } = await axios.get<{ elements: RolesList[] }>(url, {
         headers: {
            Authorization: `Bearer ${token}`,
         },
      });
      const pageIds = data.elements
         .filter((x) => x.role === 'ADMINISTRATOR')
         .map((x) => {
            return x.organizationalTarget.split(':')[3];
         });
      const pagesUrl = `https://api.linkedin.com/rest/organizations?ids=List(${pageIds.join(',')})`;
      const pageData = await axios.get<OrganisationList>(pagesUrl, {
         headers: {
            Authorization: `Bearer ${token}`,
            'LinkedIn-Version': '202407',
            'X-Restli-Protocol-Version': '2.0.0',
         },
      });
      return Object.values(pageData.data.results);
   } catch (err) {
      logger.error('Failed to fetch user details from linkedin ' + String(err));
      return null;
   }
}
export {
   postOnLinkedin,
   fetchUserDetails,
   uploadImageToLinkedin,
   getUserOrganisations,
};
