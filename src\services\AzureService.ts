import {
   BlobServiceClient,
   StorageSharedKeyCredential,
   BlobSASPermissions,
   generateBlobSASQueryParameters,
} from '@azure/storage-blob';
import { Logger } from 'winston';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';

import { Config } from '../config';

const account = Config.ACCOUNT_NAME_AZURE as string;
const accountKey = Config.AZURE_STORAGE_ACCOUNT_KEY as string;
const container = Config.CONTAINER_NAME_AZURE as string;

interface DecodeBlobPayload {
   image_url: string | undefined;
   longTerm?: boolean;
}

export class AzureService {
   private readonly blobServiceClient: BlobServiceClient;
   constructor(private logger: Logger) {
      const sharedKeyCredential = new StorageSharedKeyCredential(
         account,
         accountKey,
      );
      this.blobServiceClient = new BlobServiceClient(
         `https://${account}.blob.core.windows.net`,
         sharedKeyCredential,
      );
   }

   decodeBlob({ image_url,  longTerm = false}: DecodeBlobPayload): Promise<string> {
      if (!image_url || !image_url.startsWith('https://')) {
         throw new Error('Image URL is missing or invalid');
      }

      const sharedKeyCredential = new StorageSharedKeyCredential(account, accountKey);
      const containerClient = this.blobServiceClient.getContainerClient(container);

      const blobName = image_url.split('/').pop();
      if (!blobName) {
         throw new Error('Failed to extract blob name from URL');
      }

      const blobClient = containerClient.getBlobClient(blobName);
      const permissions = BlobSASPermissions.parse('r');

      const expiresOn = new Date();
      expiresOn.setHours(expiresOn.getHours() + (longTerm ? 24 * 365 * 10 : 1)); // 10 years or 1 hour

      const sasToken = generateBlobSASQueryParameters(
         {
            containerName: container,
            blobName,
            permissions,
            expiresOn,
         },
         sharedKeyCredential,
      ).toString();

      const blobUrlWithSAS = `${blobClient.url}?${sasToken}`;
      return Promise.resolve(blobUrlWithSAS);
   }


   async encodeBlob(payload: Express.Multer.File) {
      if (!payload.buffer) {
         throw new Error('Invalid or missing file buffer');
      }

      const containerClient =
         this.blobServiceClient.getContainerClient(container);

      const blobName = `${uuidv4()}${path.extname(payload.originalname)}`;
      const blockBlobClient = containerClient.getBlockBlobClient(blobName);

      const uploadBlobResponse = await blockBlobClient.upload(
         payload.buffer,
         payload.buffer.length,
         { blobHTTPHeaders: { blobContentType: payload.mimetype } },
      );

      this.logger.info(
         `File uploaded successfully. Etag: ${uploadBlobResponse.etag}`,
      );

      // const expiryDate = new Date();
      // expiryDate.setMinutes(expiryDate.getMinutes() + 30);

      // const sasToken = generateBlobSASQueryParameters(
      //    {
      //       containerName: container,
      //       blobName,
      //       permissions: BlobSASPermissions.parse('r'),
      //       startsOn: new Date(),
      //       expiresOn: expiryDate,
      //    },
      //    this.blobServiceClient.credential as StorageSharedKeyCredential,
      // ).toString();

      const blobUrlWithSas = `${blockBlobClient.url}`;
      return blobUrlWithSas;
   }

   async uploadBuffer(buffer: Buffer, filename: string, contentType: string): Promise<string> {
      const containerClient = this.blobServiceClient.getContainerClient(container);
      const blockBlobClient = containerClient.getBlockBlobClient(filename);

      const uploadResponse = await blockBlobClient.upload(buffer, buffer.length, {
         blobHTTPHeaders: { blobContentType: contentType },
      });

      this.logger.info(`Buffer uploaded successfully. Etag: ${uploadResponse.etag}`);

      // Return direct URL (you can generate SAS if you want controlled access)
      return blockBlobClient.url;
   }

}
