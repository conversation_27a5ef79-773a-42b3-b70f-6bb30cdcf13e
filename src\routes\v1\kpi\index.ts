import express from 'express';
import asyncHandler from '../../../midddleware/async-handler';
import { KPIService } from '../../../services/KPIService';
import { logger } from '../../../config/logger';
import { KPIController } from '../../../controllers/KPIController';
import { AppDataSource } from '../../../config/data-source';
import { AuthService } from '../../../services/AuthService';
import { EmailService } from '../../../services/EmailService';
import { OnboardingService } from '../../../services/OnboardingService';
import redisService from '../../../module/global/redis';

const router = express.Router();
const entityManager = AppDataSource.manager;
const emailService = new EmailService();
const authService = new AuthService(logger, entityManager, emailService);
const onboardingService = new OnboardingService(
   logger,
   entityManager,
   emailService,
   authService,
);
const kpiService = new KPIService(entityManager, logger, onboardingService, redisService);
const kpiController = new KPIController(kpiService, logger);

router
   .route('/data')
   .post(asyncHandler(kpiController.getKpiData.bind(kpiController)));
router
   .route('/summary')
   .post(asyncHandler(kpiController.getGPTSummary.bind(kpiController)));
router
   .route('/pinned')
   .post(asyncHandler(kpiController.updatePinned.bind(kpiController)));
router
   .route('/visible')
   .post(asyncHandler(kpiController.updateVisible.bind(kpiController)));
router
   .route('/order')
   .post(asyncHandler(kpiController.updateVisibleOrder.bind(kpiController)));
router
   .route('/meta/:clientId')
   .get(asyncHandler(kpiController.getMeta.bind(kpiController)));
router
   .route('/anomaly-root-cause')
   .post(asyncHandler(kpiController.fetchAnomalyRootCause.bind(kpiController)));

export { router as kpiRouter };
