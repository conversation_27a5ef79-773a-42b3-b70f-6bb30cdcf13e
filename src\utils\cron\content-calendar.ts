import schedule from 'node-schedule';

import { logger } from '../../config/logger';
import { getAuthHeader } from '../../services/TwitterService';
import { postTweet } from '../../repositories/twitter';
import { Media } from '../../entity/ContentCalendar';
import { SocialMediaType } from '../../types';
import { postOnLinkedin } from '../../repositories/linkedin';
import { AppDataSource } from '../../config/data-source';
import { getConnectionDetails } from '../db';

interface ScheduleItem {
   date: string;
   time: string;
   post_data: string;
   uuid: string;
   client_id: string;
   linkedinPageId?: string | null;
   media?: Media[];
   social_media_type: SocialMediaType;
}

interface PageInfo {
   id: string;
   name: string;
   page: boolean;
}

function fetchConnectionDetails(clientId: string, channelName: string) {
   const entityManager = AppDataSource.manager;
   const details = getConnectionDetails(entityManager, clientId, channelName);
   return details;
}

function parseDateTime(date: string, time: string): Date {
   const [hour, minute] = time.split(':').map(Number);
   const [day, month, year] = date.split('/').map(Number);
   return new Date(year, month - 1, day, hour, minute, 0);
}

function scheduleTwitterJob(
   item: ScheduleItem,
   authHeader: OAuth.Header,
): void {
   const { date, time, post_data, uuid, media } = item;
   const scheduledDate = parseDateTime(date, time);

   let mediaId: string | undefined;

   if (media) mediaId = media.map((item) => item.media_id).join(',');

   schedule.scheduleJob(scheduledDate, async () => {
      logger.info(
         `Executing job for UUID: ${uuid} at ${new Date().toLocaleString()}`,
      );
      try {
         await postTweet(post_data, authHeader, mediaId);
         logger.info(
            `Successfully posted tweet for UUID: ${uuid} at ${scheduledDate.toLocaleString()}`,
         );
      } catch (err) {
         logger.error(
            `Failed to post tweet: ${(err as { message: string }).message} for UUID: ${uuid} at ${scheduledDate.toLocaleString()}:`,
            String(err),
         );
      }
   });
}

function scheduleLinkedinJob(
   item: ScheduleItem,
   token: string,
   userId: string,
): void {
   const { date, time, post_data, uuid, media, linkedinPageId } = item;
   const scheduledDate = parseDateTime(date, time);

   let mediaId: string | undefined;

   if (media) mediaId = media.map((item) => item.media_id).join(',');

   schedule.scheduleJob(scheduledDate, async () => {
      logger.info(
         `Executing job for UUID: ${uuid} at ${new Date().toLocaleString()}`,
      );
      try {
         await postOnLinkedin(
            post_data,
            token,
            userId,
            linkedinPageId || null,
            mediaId,
         );
         logger.info(
            `Successfully posted content on Linkedin for UUID: ${uuid} at ${scheduledDate.toLocaleString()}`,
         );
      } catch (err) {
         logger.error(
            `Failed to post content on Linkedin for UUID: ${uuid} at ${scheduledDate.toLocaleString()}:`,
            String(err),
         );
      }
   });
}

async function scheduleContentCalendarData(data: ScheduleItem) {
   const {
      date,
      time,
      client_id,
      post_data,
      uuid,
      media,
      social_media_type,
      linkedinPageId,
   } = data;

   if (social_media_type === SocialMediaType.Twitter) {
      const twitterAuth = await fetchConnectionDetails(
         client_id,
         SocialMediaType.Twitter,
      );

      if (!twitterAuth) return;

      const { oauthToken, oauthTokenSecret } = twitterAuth.meta_data;

      const authHeader = getAuthHeader(
         'https://api.twitter.com/2/tweets',
         oauthToken as string,
         oauthTokenSecret as string,
      );

      scheduleTwitterJob(
         { date, time, post_data, uuid, client_id, media, social_media_type },
         authHeader,
      );
   }
   if (social_media_type === SocialMediaType.LinkedIn) {
      const linkedinAuth = await fetchConnectionDetails(
         client_id,
         SocialMediaType.LinkedIn,
      );

      if (!linkedinAuth) {
         logger.error('No linkedin connections found for scheduling ');
         return;
      }

      const { accessToken, pages } = linkedinAuth.meta_data;

      if (!accessToken || !pages) {
         logger.error('No linkedin user id found for scheduling post');
         return;
      }

      const userDetails = (pages as unknown as PageInfo[]).find(
         (page) => page.page === false,
      );

      if (!userDetails) return;

      scheduleLinkedinJob(
         {
            date,
            time,
            post_data,
            uuid,
            client_id,
            social_media_type,
            media,
            linkedinPageId,
         },
         accessToken as string,
         userDetails.id,
      );
   }
}

export { scheduleContentCalendarData };
