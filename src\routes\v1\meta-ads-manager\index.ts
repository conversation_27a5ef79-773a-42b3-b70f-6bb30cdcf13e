import express, { Request, Response } from 'express';
import asyncHandler from '../../../midddleware/async-handler';
import { MetaAdsService } from '../../../services/MetamanagerService';
import { logger } from '../../../config/logger';
import { MetaAdsController } from '../../../controllers/MetaManagerController';
import { AppDataSource } from '../../../config/data-source';

const entityManager = AppDataSource.manager;
const metaAdsService = new MetaAdsService(logger, entityManager);
const router = express.Router();
const metaAdsController = new MetaAdsController(metaAdsService, logger);

// Campaign Routes
router
   .route('/campaigns')
   .post(
      asyncHandler(metaAdsController.createCampaign.bind(metaAdsController)),
   );

router
   .route('/campaigns')
   .get(asyncHandler(metaAdsController.fetchCampaigns.bind(metaAdsController)));

// Ad Set Routes
router
   .route('/adsets')
   .post(asyncHandler(metaAdsController.createAdSet.bind(metaAdsController)));

router
   .route('/adsets')
   .get(asyncHandler(metaAdsController.fetchAdSets.bind(metaAdsController)));

// Ad Creative Routes

router
   .route('/adcreative')
   .post(
      asyncHandler(metaAdsController.createAdCreative.bind(metaAdsController)),
   );

// Ad Routes
router
   .route('/ad')
   .post(asyncHandler(metaAdsController.createAd.bind(metaAdsController)));

router
   .route('/ad')
   .delete(asyncHandler(metaAdsController.deleteAd.bind(metaAdsController)));

router
   .route('/chat-history')
   .post(
      asyncHandler(
         metaAdsController.UpdatetChatHistory.bind(metaAdsController),
      ),
   );

router
   .route('/summarization')
   .post(asyncHandler(metaAdsController.summarization.bind(metaAdsController)));

router
   .route('/chat-history')
   .get(
      asyncHandler(metaAdsController.getAllChatHistory.bind(metaAdsController)),
   );

router
   .route('/auto-agent-history')
   .post(
      asyncHandler(
         metaAdsController.saveAutoAgentHistory.bind(metaAdsController),
      ),
   );

router
   .route('/auto-agent-history')
   .get(
      asyncHandler(
         metaAdsController.fetchAllAutoAgentHistory.bind(metaAdsController),
      ),
   );

router
   .route('/auto-agent-history/session')
   .get(
      asyncHandler(
         metaAdsController.fetchAutoAgentHistoryBySession.bind(
            metaAdsController,
         ),
      ),
   );

/*router
   .route('/uploadChatGptImage')
   .post(
      asyncHandler(
         metaAdsController.uploadChatGptImageToAzure.bind(metaAdsController),
      ),
   );*/

router
   .route('/get-creative')
   .get(
      asyncHandler(
         metaAdsController.generateCreativeImage.bind(metaAdsController),
      ),
   );

export { router as metaAdsManagerRouter };
