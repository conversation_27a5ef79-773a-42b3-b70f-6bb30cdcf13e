import express from 'express';
import { logger } from '../../config/logger';
import { AirbyteService } from '../../services/AirbyteService';
import { AirbyteController } from '../../controllers/AirbyteController';
import asyncHandler from '../../midddleware/async-handler';

const router = express.Router();
const airbyteService = new AirbyteService('https://api.airbyte.com/v1/');
const airbyteController = new AirbyteController(airbyteService, logger);

router
   .route('/workspace')
   .post(
      asyncHandler(
         airbyteController.handleWorkspaceCreation.bind(airbyteController),
      ),
   );

router
   .route('/auth')
   .post(
      asyncHandler(
         airbyteController.handleInitiateAuthForSource.bind(airbyteController),
      ),
   );

router
   .route('/workspace/:workspaceId')
   .get(
      asyncHandler(
         airbyteController.handleWorkspaceDetails.bind(airbyteController),
      ),
   );

router
   .route('/source')
   .post(
      asyncHandler(
         airbyteController.handleSourceCreation.bind(airbyteController),
      ),
   );

router
   .route('/destination')
   .post(
      asyncHandler(
         airbyteController.handleDestinationCreation.bind(airbyteController),
      ),
   );

router.post(
   '/remove',
   asyncHandler(airbyteController.handleRemoveSource.bind(airbyteController)),
);

router
   .route('/connection')
   .post(
      asyncHandler(
         airbyteController.handleConnectionCreation.bind(airbyteController),
      ),
   );

router
   .route('/job')
   .post(
      asyncHandler(airbyteController.handleTriggerSync.bind(airbyteController)),
   );

export { router as airbyteRouter };
