import { DatabaseError } from 'pg';
import { EntityManager } from 'typeorm';
import { Config } from '../../../config';
import * as types from '../../../types/agents/analytics-agent/analytics-agent.types';

export class AnalyticsAgentModel {
   constructor(private entityManager: EntityManager) {}

   async fetchAllSessionsByUserID(
      payload: types.FetchAllSessionsByUserIDPayload,
   ): Promise<types.AnalyticsAgentChat[]> {
      try {
         const { client_id, user_id, page } = payload;

         const LIMIT = 10;
         const OFFSET = (page - 1) * LIMIT;

         const query = `
            SELECT DISTINCT ON (c.session_id)
               c.*,
               qm.question_modes
            FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.analytics_agent_chat c
            JOIN LATERAL (
               SELECT ARRAY_AGG(DISTINCT ac.question_mode) AS question_modes
               FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.analytics_agent_chat ac
               WHERE ac.session_id = c.session_id
            ) qm ON true
            WHERE c.client_id = $1
            AND c.user_id = $2
            ORDER BY c.session_id DESC
            LIMIT $3 OFFSET $4;
         `;

         const result: types.AnalyticsAgentChat[] =
            await this.entityManager.query(query, [
               client_id,
               user_id,
               LIMIT,
               OFFSET,
            ]);

         return result;
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async fetchHistoryBySessionID(
      payload: types.FetchSessionHistoryByIDPayload,
   ): Promise<types.AnalyticsAgentChat[]> {
      try {
         const { session_id, client_id, user_id } = payload;

         const query = `
            SELECT * FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.analytics_agent_chat
            WHERE client_id = $1
            AND user_id = $2
            AND session_id = $3
            ORDER BY created_at ASC;
         `;

         const result: types.AnalyticsAgentChat[] =
            await this.entityManager.query(query, [
               client_id,
               user_id,
               session_id,
            ]);

         return result;
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async fetchFeatureUsage(
      payload: types.FetchFeatureUsagePayload,
   ): Promise<types.FeatureUsage> {
      try {
         const { client_id, user_id, feature_name, feature_type } = payload;

         const query = `
            SELECT * FROM ${Config.DB_Postgres_CONFIG_SCHEMA}.client_feature_usage_tracker
            WHERE client_id = $1
            AND user_id = $2
            AND feature_name = $3
            AND feature_type = $4;
         `;

         const result: types.FeatureUsage[] = await this.entityManager.query(
            query,
            [client_id, user_id, feature_name, feature_type],
         );

         return result[0] || {};
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async trackFeatureUsage(
      payload: types.TrackFeatureUsagePayload,
   ): Promise<void> {
      try {
         const { client_id, user_id, feature_name, feature_type } = payload;

         const query = `SELECT ${Config.DB_Postgres_CONFIG_SCHEMA}.update_feature_usage($1, $2, $3, $4);`;

         await this.entityManager.query(query, [
            client_id,
            user_id,
            feature_name,
            feature_type,
         ]);
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async addChatToSessionHistory(
      payload: types.AddChatToSessionHistoryPayload,
   ): Promise<types.AnalyticsAgentChat[]> {
      try {
         const {
            client_id,
            user_id,
            session_id,
            chat_id,
            user_query,
            response_status,
            final_response,
            agent_name,
            prev_agent_response,
            prev_agent_name,
            channel,
            context_snapshot,
            response_like_dislike,
            response_feedback_category,
            rewrite_response,
            copy_response,
            response_time,
            user_feedback_comments,
            question_mode,
            diagnostics_prompt_meta_data,
            session_name,
         } = payload;

         const query = `
            INSERT INTO ${Config.DB_Postgres_AGENTS_SCHEMA}.analytics_agent_chat (
            client_id, user_id, session_id, chat_id, user_query, response_status, final_response,
            agent_name, prev_agent_response, prev_agent_name, channel, context_snapshot,
            response_like_dislike, response_feedback_category, rewrite_response, copy_response,
            response_time, user_feedback_comments, question_mode, diagnostics_prompt_meta_data,
            session_name
            )
            VALUES ($1, $2, $3, $4, $5,
                  $6, $7, $8, $9, $10,
                  $11, $12::jsonb, $13, $14, $15, $16, $17, $18, $19, $20::jsonb, $21)
            ON CONFLICT (chat_id) DO UPDATE SET
            response_status = EXCLUDED.response_status,
            final_response = EXCLUDED.final_response,
            agent_name = EXCLUDED.agent_name,
            prev_agent_response = EXCLUDED.prev_agent_response,
            prev_agent_name = EXCLUDED.prev_agent_name,
            context_snapshot = EXCLUDED.context_snapshot,
            response_time = EXCLUDED.response_time,
            session_name = EXCLUDED.session_name,
            updated_at = NOW();
         `;

         await this.entityManager.query(query, [
            client_id,
            user_id,
            session_id,
            chat_id,
            user_query,
            response_status,
            final_response,
            agent_name,
            prev_agent_response,
            prev_agent_name,
            channel,
            context_snapshot,
            response_like_dislike,
            response_feedback_category,
            rewrite_response,
            copy_response,
            response_time,
            user_feedback_comments,
            question_mode,
            diagnostics_prompt_meta_data,
            session_name,
         ]);

         const updatedHistoryQuery = `
            SELECT * FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.analytics_agent_chat
            WHERE client_id = $1
            AND user_id = $2
            AND session_id = $3
            ORDER BY created_at DESC
            LIMIT 6;
         `;

         const updatedHistory: types.AnalyticsAgentChat[] =
            await this.entityManager.query(updatedHistoryQuery, [
               client_id,
               user_id,
               session_id,
            ]);

         return updatedHistory;
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async fetchSessionInsightsByID(
      payload: types.FetchChatInsightsByIDPayload,
   ): Promise<types.AnalyticsAgentChat[]> {
      try {
         const { session_id, client_id, user_id } = payload;

         const query = `
            SELECT * FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.analytics_insights_history
            WHERE client_id = $1
            AND user_id = $2
            AND session_id = $3;
         `;

         const result: types.AnalyticsAgentChat[] =
            await this.entityManager.query(query, [
               client_id,
               user_id,
               session_id,
            ]);

         return result;
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async addInsightsToSessionHistory(
      payload: types.AddInsightsToSessionHistoryPayload,
   ) {
      try {
         const { client_id, user_id, session_id, chat_id, chat_flow_context } =
            payload;

         const query = `
            INSERT INTO ${Config.DB_Postgres_AGENTS_SCHEMA}.analytics_insights_history (
            client_id, user_id, session_id, chat_id, chat_flow_context
            ) VALUES ($1, $2, $3, $4, $5::jsonb);
         `;

         await this.entityManager.query(query, [
            client_id,
            user_id,
            session_id,
            chat_id,
            JSON.stringify(chat_flow_context),
         ]);
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async likeDislikeChat(payload: types.LikeDislikeChatPayload) {
      try {
         const { session_id, client_id, user_id, chat_id, action } = payload;

         const query = `
            UPDATE ${Config.DB_Postgres_AGENTS_SCHEMA}.analytics_agent_chat
            SET response_like_dislike = $1
            WHERE client_id = $2
            AND user_id = $3
            AND session_id = $4
            AND chat_id = $5;
         `;

         await this.entityManager.query(query, [
            action,
            client_id,
            user_id,
            session_id,
            chat_id,
         ]);
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async updateChatRewritten(payload: types.UpdateChatRewrittenPayload) {
      try {
         const { session_id, client_id, user_id, chat_id } = payload;

         const query = `
            UPDATE ${Config.DB_Postgres_AGENTS_SCHEMA}.analytics_agent_chat
            SET rewrite_response = TRUE
            WHERE client_id = $1
            AND user_id = $2
            AND session_id = $3
            AND chat_id = $4;
         `;

         await this.entityManager.query(query, [
            client_id,
            user_id,
            session_id,
            chat_id,
         ]);
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async updateChatCopied(payload: types.UpdateChatCopiedPayload) {
      try {
         const { session_id, client_id, user_id, chat_id } = payload;

         const query = `
            UPDATE ${Config.DB_Postgres_AGENTS_SCHEMA}.analytics_agent_chat
            SET copy_response = TRUE
            WHERE client_id = $1
            AND user_id = $2
            AND session_id = $3
            AND chat_id = $4;
         `;

         await this.entityManager.query(query, [
            client_id,
            user_id,
            session_id,
            chat_id,
         ]);
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }

   async updateChatFeedback(payload: types.UpdateChatFeedbackPayload) {
      try {
         const { client_id, user_id, session_id, chat_id, type, feedback } =
            payload;

         const query = `
            UPDATE ${Config.DB_Postgres_AGENTS_SCHEMA}.analytics_agent_chat
            SET ${type === 'custom' ? 'user_feedback_comments' : 'response_feedback_category'} = $1
            WHERE client_id = $2
              AND user_id = $3
              AND session_id = $4
              AND chat_id = $5;
         `;

         await this.entityManager.query(query, [
            feedback,
            client_id,
            user_id,
            session_id,
            chat_id,
         ]);
      } catch (err) {
         const error = err as DatabaseError;
         throw new Error(error.message);
      }
   }
}
