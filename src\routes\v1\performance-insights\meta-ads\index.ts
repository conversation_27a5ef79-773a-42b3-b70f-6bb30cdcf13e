import express from 'express';
import asyncHandler from '../../../../midddleware/async-handler';
import { AppDataSource } from '../../../../config/data-source';
import { logger } from '../../../../config/logger';
import { PulseMetaAdsService } from '../../../../services/performance-insights/PulseMetaAdsService';
import { PulseMetaAdsController } from '../../../../controllers/performance-insights/PulseMetaAdsController';

const router = express.Router();

const entityManager = AppDataSource.manager;
const pulseMetaAdsService = new PulseMetaAdsService(entityManager);
const pulseMetaAdsController = new PulseMetaAdsController(
   pulseMetaAdsService,
   logger,
);

router
   .route('/meta/objectives')
   .get(
      asyncHandler(
         pulseMetaAdsController.fetchObjectives.bind(pulseMetaAdsController),
      ),
   );

router
   .route('/meta/campaigns/tracked')
   .get(
      asyncHandler(
         pulseMetaAdsController.fetchTrackedCampaigns.bind(
            pulseMetaAdsController,
         ),
      ),
   );

router
   .route('/meta/campaigns/budget-spend')
   .get(
      asyncHandler(
         pulseMetaAdsController.fetchCampaignsWithBudgetSpend.bind(
            pulseMetaAdsController,
         ),
      ),
   );

router
   .route('/meta/campaigns')
   .get(
      asyncHandler(
         pulseMetaAdsController.fetchCampaignsDaywise.bind(
            pulseMetaAdsController,
         ),
      ),
   );

router
   .route('/meta/adsets')
   .get(
      asyncHandler(
         pulseMetaAdsController.fetchAdsetsDaywise.bind(pulseMetaAdsController),
      ),
   );

router
   .route('/meta/ads')
   .get(
      asyncHandler(
         pulseMetaAdsController.fetchAdsDaywise.bind(pulseMetaAdsController),
      ),
   );

router
   .route('/meta/targeting')
   .get(
      asyncHandler(
         pulseMetaAdsController.fetchTargetingDaywise.bind(
            pulseMetaAdsController,
         ),
      ),
   );

router
   .route('/meta/chart-insights')
   .post(
      asyncHandler(
         pulseMetaAdsController.fetchChartInsights.bind(pulseMetaAdsController),
      ),
   );

router
   .route('/meta/benchmark-insights')
   .post(
      asyncHandler(
         pulseMetaAdsController.fetchBenchmarkInsights.bind(
            pulseMetaAdsController,
         ),
      ),
   );

router
   .route('/meta/adset-insights')
   .post(
      asyncHandler(
         pulseMetaAdsController.fetchAdsetInsights.bind(pulseMetaAdsController),
      ),
   );

router
   .route('/meta/targeting-insights')
   .post(
      asyncHandler(
         pulseMetaAdsController.fetchTargetingInsights.bind(
            pulseMetaAdsController,
         ),
      ),
   );

router
   .route('/meta/tracked-campaign-kpis')
   .post(
      asyncHandler(
         pulseMetaAdsController.fetchTrackedCampaignKPIs.bind(pulseMetaAdsController),
      ),
   );

router
   .route('/update-track')
   .put(
      asyncHandler(
         pulseMetaAdsController.updateTrackedKpis.bind(pulseMetaAdsController),
      ),
   );

router
   .route('/get_tracked_prev_kpis')
   .post(
      asyncHandler(
         pulseMetaAdsController.fetchTrackedPrevKpis.bind(
            pulseMetaAdsController,
         ),
      ),
   );

export { router as pulseMetaAdsRouter };
