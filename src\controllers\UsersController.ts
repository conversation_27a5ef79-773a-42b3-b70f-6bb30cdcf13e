import { Request, Response } from 'express';
import { UsersService } from '../services/UsersService';
import { Logger } from 'winston';

import {
   GetAllUsersPayload,
   CreateUpdateUserPayload,
   DeleteUserPayload,
} from '../types';

export class UsersController {
   constructor(
      private usersService: UsersService,
      private logger: Logger,
   ) {}

   async getAllProfiles(req: Request, res: Response) {
      const result = await this.usersService.getAllProfiles(
         req.query as GetAllUsersPayload,
      );
      this.logger.info('Fetched all profiles successfully');
      res.status(200).send(result.response);
   }

   async getAllUsers(req: Request, res: Response) {
      const result = await this.usersService.getAllUsers(
         req.query as GetAllUsersPayload,
      );
      this.logger.info('Fetched all users successfully');
      res.status(200).send(result.response);
   }

   async createUser(req: Request, res: Response) {
      const result = await this.usersService.createUser(
         req.body as CreateUpdateUserPayload,
      );
      this.logger.info('Created user successfully');
      res.status(200).send(result.response);
   }

   async updateUser(req: Request, res: Response) {
      const result = await this.usersService.updateUser(
         req.body as CreateUpdateUserPayload,
      );
      this.logger.info('Created user successfully');
      res.status(200).send(result.response);
   }

   async deleteUser(req: Request, res: Response) {
      const result = await this.usersService.deleteUser(
         req.body as DeleteUserPayload,
      );
      this.logger.info('Deleted user successfully');
      res.status(200).send(result.response);
   }
}
