import OAuth from 'oauth-1.0a';
import axios from 'axios';
import qs from 'querystring';

import { Logger } from 'winston';
import { getTwitterAuth } from './TwitterService';
import { Config } from '../config';

const authorizeURL = new URL(Config.TWITTER_AUTHORIZE_URL!);

export class TwitterAuthService {
   private readonly oauth: OAuth | null = null;

   constructor(private logger: Logger) {
      this.oauth = getTwitterAuth();
   }
   async requestToken() {
      if (!this.oauth) throw new Error('Unauthorized');
      try {
         const { data } = await axios.post<string>(
            Config.TWITTER_REQUEST_TOKEN_URL!,
            null,
            {
               headers: {
                  ...this.oauth.toHeader(
                     this.oauth.authorize({
                        url: Config.TWITTER_REQUEST_TOKEN_URL!,
                        method: 'POST',
                     }),
                  ),
               },
            },
         );
         const tokenData = qs.parse(data) as {
            oauth_token: string;
            oauth_token_secret: string;
            oauth_callback_confirmed: string;
         };

         const oauthToken = tokenData.oauth_token;
         const oauthTokenSecret = tokenData.oauth_token_secret;
         const authorizeURLWithToken = new URL(authorizeURL);
         authorizeURLWithToken.searchParams.append('oauth_token', oauthToken);

         return {
            oauthToken,
            oauthTokenSecret,
            authorizeURL: authorizeURLWithToken.href,
         };
      } catch (err) {
         this.logger.error(String(err));
         return null;
      }
   }

   async fetchTokenDetails(oauthToken: string, pin: string, clientId: string) {
      if (!this.oauth) throw new Error('Unauthorized');
      const authHeader = this.oauth.toHeader(
         this.oauth.authorize({
            url: Config.TWITTER_ACCESS_TOKEN_URL!,
            method: 'POST',
         }),
      );
      const url = `https://api.twitter.com/oauth/access_token?oauth_verifier=${pin}&oauth_token=${oauthToken}`;
      try {
         const { data } = await axios.post<string>(
            url,
            {},
            {
               headers: {
                  Authorization: authHeader['Authorization'],
               },
            },
         );
         const { screen_name, oauth_token, oauth_token_secret, user_id } =
            qs.parse(data) as {
               screen_name: string;
               oauth_token: string;
               user_id: string;
               oauth_token_secret: string;
            };

         //  Store details in Mongo
         //  await TwitterAuth.findOneAndUpdate(
         //     { clientID: clientId },
         //     {
         //        $set: {
         //           oauthToken: oauth_token,
         //           oauthTokenSecret: oauth_token_secret,
         //           screenName: screen_name,
         //           socialMedia: 'twitter',
         //           userId: user_id,
         //        },
         //     },
         //     { new: true, upsert: true, setDefaultsOnInsert: true }, // upsert = if no doc matches, then will insert
         //  );

         const connectionDetails = {
            oauthToken: oauth_token,
            oauthTokenSecret: oauth_token_secret,
            screenName: screen_name,
            userId: user_id,
            clientID: clientId,
         };

         return connectionDetails;
      } catch (err) {
         this.logger.error(String(err));
         return null;
      }
   }
}
