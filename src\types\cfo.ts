export interface ClientPayload {
    clientId: string;
}
export interface UpdateCOGSPayload {
    clientId: string;
    fixedPercent: string;
    id: number | null
}
export interface ShippingCost {
    clientId: string;
    is_default: string;
    id: number | null
}
export interface ShippingCostByOrderId {
    clientId: string;
    Order_Id: number;
    Shipping_Cost: number;
 }
export interface ShippingProfile {
    clientId: string;
    profileName: string;
    zones: string;
    fixedRate: number;
    weightBased: WeightBased[];
}

export interface FixedExpense {
    clientId: string;
    id: number;
    title: string;
    cost: number | string;
    source: string;
    categories: string[];
    selCategory: string;
    startDate: Date | null;
    endDate: Date | null;
    adSpend: boolean;
    recurringDays: number | string;
 }
 export interface VariableExpense {
    clientId: string;
    id: number;
    name: string;
    campaign: string;
    metric: string;
    source: string;
    percent: number | string;
    categories: string[];
    selCategory: string;
    startDate: Date | null;
    endDate: Date | null;
    adSpend: boolean;
 }
 
export interface UpdateCOGSPayload {
    clientId: string;
    fixedPercent: string;
    id: number | null
}
export interface PaymentMethod {
    clientId: string;
    method: string;
    cost: string;
    fee: string;
    id: number | null;
}

export interface DeleteRecord {
    clientId: string;
    id?: number;
    type: string;
    zone?: string;
}
export interface WeightBased {
    rate: string;
 
    minWeight: string;
 
    maxWeight: string;
 
    measure: string;
 }
 