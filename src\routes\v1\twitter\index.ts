import express from 'express';
import multer from 'multer';
import asyncHandler from '../../../midddleware/async-handler';

import { TwitterService } from '../../../services/TwitterService';
import { logger } from '../../../config/logger';
import { TwitterController } from '../../../controllers/TwitterController';

const router = express.Router();

const twitterService = new TwitterService(logger);
const twitterController = new TwitterController(twitterService, logger);

const upload = multer({
   storage: multer.memoryStorage(),
   limits: { fileSize: 50 * 1024 * 1024 },
});

router.post(
   '/upload',
   upload.single('media'),
   asyncHandler(
      twitterController.handleUploadMediaToTwitter.bind(twitterController),
   ),
);

router
   .route('/contentcalendar')
   .post(
      asyncHandler(
         twitterController.handlePostContentCalendar.bind(twitterController),
      ),
   );

router
   .route('/tweets')
   .post(
      asyncHandler(twitterController.handlePostTweets.bind(twitterController)),
   );

router
   .route('/content-calendar')
   .post(
      asyncHandler(
         twitterController.handleGetContentCalendar.bind(twitterController),
      ),
   );

router
   .route('/delete')
   .post(
      asyncHandler(
         twitterController.handleDeleteTweets.bind(twitterController),
      ),
   );

router
   .route('/update')
   .put(
      asyncHandler(
         twitterController.handleUpdateContentCalendar.bind(twitterController),
      ),
   );

router
   .route('/get_caption')
   .post(
      asyncHandler(twitterController.handleGetCaption.bind(twitterController)),
   );

export { router as twitterRouter };
