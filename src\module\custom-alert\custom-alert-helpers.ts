import { Config } from '../../config';
import * as types from './custom-alert-types';

const CHANNELS = {
   facebookads: 'Meta Ads',
   googleads: 'Google Ads',
   amazon_ads: 'Amazon Ads',
   store: 'Shopify',
   web: 'Web',
   amazon_selling_partner: 'Amazon Selling Partner',
};

const TRENDS = {
   increasing: 'Increasing',
   decreasing: 'Decreasing',
};

const COMPARISON = {
   more_than: 'More Than',
   less_than: 'Less Than',
   equal_to: 'Equal To',
};

export const CHANNEL_MAP = {
   facebookads: 'Meta Ads',
   googleads: 'Google Ads',
   amazon_ads: 'Amazon Ads',
   store: 'Shopify',
   web: 'Web',
   amazon_selling_partner: 'Amazon Selling Partner',
};

export const COMPARISON_MAP = {
   more_than: 'more than',
   less_than: 'less than',
   equal_to: 'exactly',
};

export const TREND_MAP = {
   increasing: 'increase',
   decreasing: 'decrease',
};

export const splitAndUppercase = (str: string): string => {
   return str
      .split('_')
      .map((word) => word.toUpperCase())
      .join(' ');
};

export const getAverage = (arr: number[]) => {
   if (!Array.isArray(arr) || arr.length === 0) return 0;

   const sum = arr.reduce((acc, val) => acc + val, 0);
   return sum / arr.length;
};

export const getKeysAverage = (obj: Record<string, number[]>) => {
   const averages: Record<string, number> = {};

   for (const key in obj) {
      if (Array.isArray(obj[key])) {
         averages[key] = getAverage(obj[key]);
      }
   }

   return averages;
};

export const evaluateKpiRules = (
   targetData: Record<string, number>,
   referenceData: Record<string, number>,
   kpiRules: types.KPIRules[],
) => {
   const compare = (a: number, b: number, op: string) => {
      switch (op) {
         case 'more_than':
            return a > b;
         case 'less_than':
            return a < b;
         case 'equal_to':
            return a === b;
         default:
            return false;
      }
   };

   return kpiRules.every((rule) => {
      const { metric, trend, value, comparison, value_type } = rule;
      const targetValue = targetData[metric];
      const referenceValue = referenceData[metric];

      if (
         typeof targetValue !== 'number' ||
         typeof referenceValue !== 'number'
      ) {
         return false;
      }

      let comparisonValue;

      if (value_type === 'percentage') {
         trend === 'increasing'
            ? (comparisonValue =
                 ((targetValue - referenceValue) / referenceValue) * 100)
            : (comparisonValue =
                 ((referenceValue - targetValue) / targetValue) * 100);
      } else {
         comparisonValue =
            trend === 'increasing'
               ? targetValue - referenceValue
               : referenceValue - targetValue;
      }

      if (
         !comparisonValue ||
         isNaN(comparisonValue) ||
         !isFinite(comparisonValue) ||
         comparisonValue < 0
      ) {
         return false;
      }

      console.log(
         `[KPI Rule] Metric: ${metric}, Trend: ${trend}, Type: ${value_type}, ` +
            `Comparison: ${comparison}, Threshold: ${value}, ` +
            `Calculated: ${comparisonValue}, Target: ${targetValue}, Reference: ${referenceValue}`,
      );

      return compare(comparisonValue, parseFloat(value), comparison);
   });
};

export const frameQuestion = (alert: types.CustomAlert) => {
   const allMetrics = alert.alert_conditions.kpi_rules
      .map(
         (rule) =>
            `${splitAndUppercase(rule.metric)} ${TREND_MAP[rule.trend]} by ${COMPARISON_MAP[rule.comparison]} ${rule.value}${rule.value_type === 'percentage' ? ' percent' : ''}`,
      )
      .join(', ');

   return `Why did my ${CHANNEL_MAP[alert.alert_conditions.channel as keyof typeof CHANNEL_MAP]} 
            ${allMetrics}${alert.alert_conditions.campaigns.length > 0 ? (alert.alert_conditions.campaigns.length > 1 ? ' for campaigns' : ' for campaign') : ''}
            ${alert.alert_conditions.campaigns.map((c) => c.name).join(', ')}
            during last ${alert.alert_conditions.target_period.value} days compared to last ${alert.alert_conditions.reference_period.value} days?`;
};

export const alertEmailTemplate = (
   recipient: string,
   channel: string,
   campaigns: string,
   multipleCampaigns: boolean,
   metrics: string,
   multipleMetrics: boolean,
   targetPeriodDays: number,
   referencePeriodDays: number,
   kpiRules: Record<string, string>[],
   questionText: string,
) => {
   return `<!DOCTYPE html>
            <html lang="en">
            <head>
            <meta charset="UTF-8" />
            <meta name="viewport" content="width=device-width, initial-scale=1.0" />
            <title>KPI Alert Email</title>
            </head>
            <body style="margin: 0; padding: 0; background-color: #f4f4f4; font-family: 'Segoe UI', sans-serif;">
            <table width="100%" cellpadding="0" cellspacing="0" role="presentation" style="background-color: #f4f4f4; padding: 40px 0;">
               <tr>
                  <td align="center">
                  <table width="600" cellpadding="0" cellspacing="0" style="background: #ffffff; border-radius: 12px; box-shadow: 0 0 10px rgba(0,0,0,0.07); overflow: hidden;">
                     <tr>
                        <td style="background-color: #d32f2f; color: #ffffff; padding: 24px 40px; font-size: 20px; font-weight: bold;">
                        🚨 Flable AI Alert
                        </td>
                     </tr>
                     <tr>
                        <td style="padding: 32px 40px; color: #333;">
                        <p style="font-size: 16px; margin: 0 0 16px;">Hi <strong>${recipient}</strong>,</p>

                        <p style="font-size: 16px; margin-bottom: 20px;">
                           We’ve detected that one of your alerts has matched all the rules you have set for <strong>${channel}</strong> channel.
                        </p>

                        <h3 style="margin: 24px 0 10px; font-size: 17px; color: #3546d4;">🔔 Alert Summary</h3>
                        <table cellpadding="0" cellspacing="0" width="100%" style="background: #f8f9fa; border-radius: 8px; padding: 12px; font-size: 15px; margin-bottom: 20px;">
                           <tr><td style="padding: 10px;">Channel: <strong>${channel}</strong></td></tr>
                           ${campaigns ? `<tr><td style="padding: 10px;">${multipleCampaigns ? 'Campaigns' : 'Campaign'}: <strong>${campaigns}</strong></td></tr>` : ''}
                           ${metrics ? `<tr><td style="padding: 10px;">${multipleMetrics ? 'Metrics' : 'Metric'}: <strong>${metrics}</strong></td></tr>` : ''}
                        </table>

                        <h3 style="margin: 24px 0 10px; font-size: 17px; color: #3546d4;">📊 Period Comparison</h3>
                        <table cellpadding="0" cellspacing="0" width="100%" style="background: #f8f9fa; border-radius: 8px; padding: 12px; font-size: 15px; margin-bottom: 20px;">
                           <tr><td style="padding: 10px;">Target Period: <strong>Last ${targetPeriodDays} Days</strong></td></tr>
                           <tr><td style="padding: 10px;">Reference Period: <strong>Last ${referencePeriodDays} Days</strong></td></tr>
                        </table>

                        <h3 style="margin: 24px 0 10px; font-size: 17px; color: #3546d4;">📈 KPI Rules</h3>
                        <ul style="padding-left: 0; list-style: none;">
                           ${kpiRules
                              .map(
                                 (rule) => `
                              <li style="margin-bottom: 16px;">
                              <div style="background: #f0f0f0; border-radius: 6px; padding: 12px 16px; font-size: 14px; line-height: 1.5;">
                                 <strong>Metric:</strong> ${rule.metric}<br/>
                                 <strong>Trend:</strong> ${rule.trend}<br/>
                                 <strong>Value:</strong> ${rule.value} (${rule.value_type})<br/>
                                 <strong>Comparison:</strong> ${rule.comparison}
                              </div>
                              </li>
                           `,
                              )
                              .join('')}
                        </ul>

                        <p style="font-size: 15px; margin-top: 24px;">
                           The <strong>${metrics}</strong> ${multipleMetrics ? 'metrics have' : 'metric has'} matched your threshold condition${campaigns ? (multipleCampaigns ? ` in the campaigns <strong>${campaigns}</strong>.` : ` in the campaign <strong>${campaigns}</strong>.`) : '.'}
                        </p>

                        <h3 style="margin: 30px 0 10px; font-size: 17px; color: #3546d4;">📥 What You Can Do</h3>
                        <table cellspacing="0" cellpadding="0" style="margin-top: 16px;">
                           <tr>
                              <td style="padding: 8px;">
                              <a href="${Config.FRONTEND_DOMAIN?.split(',')[0]}/marco/analytics-agent?query=${questionText}"
                                 style="background-color: #3546d4; color: #fff; text-decoration: none; padding: 12px 20px; border-radius: 6px; font-weight: bold; display: inline-block;">
                                 🔍 Analyze Cause
                              </a>
                              </td>
                              <td style="padding: 8px;">
                              <a href="${Config.FRONTEND_DOMAIN?.split(',')[0]}/dashboard"
                                 style="background-color: #3546d4; color: #fff; text-decoration: none; padding: 12px 20px; border-radius: 6px; font-weight: bold; display: inline-block;">
                                 📊 Go to Dashboard
                              </a>
                              </td>
                           </tr>
                        </table>

                        <p style="margin-top: 30px; font-size: 14px; background: #fff3cd; padding: 12px 16px; border-left: 4px solid #ffca28; border-radius: 4px;">
                           <strong>Note:</strong> This alert was generated using data available until the end of yesterday.
                        </p>

                        <blockquote style="margin-top: 28px; font-size: 15px; color: #555; font-style: italic; border-left: 4px solid #ccc; padding-left: 16px;">
                           “Data tells you <em>what</em>; insights tell you <em>why</em>. Let’s uncover the why.”
                        </blockquote>

                        <p style="margin-top: 30px; font-size: 15px;">
                           — Your Flable AI Team
                        </p>
                        </td>
                     </tr>

                     <tr>
                        <td style="background: #f0f2f7; text-align: center; padding: 20px; font-size: 13px; color: #7b809a;">
                        You’re receiving this because you enabled alerts in <strong>Flable AI</strong>.<br />
                        © ${new Date().getFullYear()} Flable AI. All rights reserved.
                        </td>
                     </tr>

                  </table>
                  </td>
               </tr>
            </table>
            </body>
            </html>
            `;
};

export const returnValidValues = (alert: types.CustomAlert) => {
   const questionText = frameQuestion(alert);
   return {
      channel:
         CHANNELS[alert.alert_conditions.channel as keyof typeof CHANNELS],
      campaigns: alert.alert_conditions.campaigns
         .map((campaign) => campaign.name)
         .join(', '),
      multipleCampaigns: alert.alert_conditions.campaigns.length > 1,
      metrics: alert.alert_conditions.metrics.map(splitAndUppercase).join(', '),
      multipleMetrics: alert.alert_conditions.metrics.length > 1,
      targetPeriodDays: parseInt(
         alert.alert_conditions.target_period.value,
         10,
      ),
      referencePeriodDays: parseInt(
         alert.alert_conditions.reference_period.value,
         10,
      ),
      kpiRules: alert.alert_conditions.kpi_rules.map((rule) => ({
         metric: rule.metric,
         trend: TRENDS[rule.trend as keyof typeof TRENDS],
         value: rule.value,
         value_type: rule.value_type,
         comparison: COMPARISON[rule.comparison as keyof typeof COMPARISON],
      })),
      questionText,
   };
};
