import { Config } from '../config';
import { google } from 'googleapis';

const oauth2Client = new google.auth.OAuth2(
   Config.GOOGLE_CLIENT_ID,
   Config.GOOGLE_CLIENT_SECRET,
   Config.GOOGLE_REDIRECT_URI,
);

export const getAuthUrl = (): string => {
   return oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: ['https://www.googleapis.com/auth/webmasters.readonly'],
      prompt: 'consent',
   });
};

export const getAccessToken = async (code: string) => {
   const { tokens } = await oauth2Client.getToken(code);
   return {
      access: tokens.access_token,
      refresh: tokens.refresh_token,
   };
};
