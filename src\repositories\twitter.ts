import axios from 'axios';
import OAuth from 'oauth-1.0a';

async function postTweet(
   tweets: string,
   authHeader: OAuth.Header,
   mediaId?: string,
) {
   const endpointURL = `https://api.twitter.com/2/tweets`;
   const data: {
      text: string;
      media?: { media_ids: string[] };
   } = {
      text: tweets,
   };

   if (mediaId) {
      data.media = { media_ids: mediaId.split(',') };
   }
   try {
      await axios.post(endpointURL, data, {
         responseType: 'json',
         headers: {
            Authorization: authHeader['Authorization'],
            'user-agent': 'v2CreateTweetJS',
            'content-type': 'application/json',
            accept: 'application/json',
         },
      });
   } catch (err) {
      const {
         response: {
            data: { title, status },
         },
      } = err as { response: { data: { title: string; status: number } } };
      const msg = `${title} with status code ${status}`;
      throw new Error(msg);
   }
}

export { postTweet };
