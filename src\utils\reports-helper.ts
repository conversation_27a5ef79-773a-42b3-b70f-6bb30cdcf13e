import { MetricReport } from '@/types';
import { FinalAgg, <PERSON><PERSON><PERSON>gg, K<PERSON>Details } from '@/types/kpi';
import MarkdownIt from 'markdown-it';

import { format, subDays } from 'date-fns';
import { Config } from '../config';
const md = new MarkdownIt();

export function convertAgentMarkdownToHtml(rawMarkdown: string): string {
   const cleanMarkdown = rawMarkdown.replace(/\\n/g, '\n');
   const rawHtml = md.render(cleanMarkdown);
   return `
 <html lang="en">
 <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
       body { font-family: Arial, sans-serif; color: #333; padding: 20px; }
       table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
       th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
       th { background-color: #f2f2f2; }
       h2, h3 { color: #444; }
       ul { padding-left: 20px; }
       hr { margin: 30px 0; border: 0; border-top: 1px solid #eee; }
       blockquote { background: #f9f9f9; padding: 10px 20px; margin: 10px 0; border-left: 5px solid #ccc; }
     </style>
 </head>
 <body>
 <div style="font-family: Arial, sans-serif; color: #333;">
   <p>Hi Flable user,</p>
 ${rawHtml}
<div style="display: flex; align-items: center; margin-top: 30px;">
  <h2 style="margin: 0 16px 0 0;">Uncover More:</h2>
  <a href="${Config.FRONTEND_DOMAIN}/dashboard"
     style="padding: 10px 15px; background: #007bff; color: #fff; text-decoration: none; border-radius: 5px; margin-right: 10px;"
     target="_blank">
    Go to Dashboard
  </a>
  <a href="${Config.FRONTEND_DOMAIN}/marco/analytics-agent"
     style="padding: 10px 15px; background: #007bff; color: #fff; text-decoration: none; border-radius: 5px;"
     target="_blank">
    AI CMO Agent
  </a>
</div>
 </div>
 </body>
 </html>
 `;
}

export function generateXiInput(
   endDateStr: string,
   activeCategories: string[],
) {
   const endDate = new Date(endDateStr);

   const currentWeekStart = subDays(endDate, 6);
   const prevWeekEnd = subDays(currentWeekStart, 1);
   const prevWeekStart = subDays(prevWeekEnd, 6);

   const formatDate = (date: Date) => format(date, 'd MMM');
   const formatFull = (date: Date) => format(date, 'd MMM yyyy');

   const currentWeekRange = `${formatDate(currentWeekStart)} – ${formatDate(endDate)}`;
   const prevWeekRange = `${formatDate(prevWeekStart)} – ${formatDate(prevWeekEnd)}`;
   const formattedCategories = activeCategories
      .map((cat) => `- ${cat}`)
      .join('\n');

   const xiInput = `You are an AI CMO Agent specialized in generating Weekly Performance Business Review Reports using real metric values from available marketing and analytics data. You must query actual performance data from the client database to ensure accurate and verifiable KPI comparisons. Do not assume, guess, or hallucinate any metric values. All numbers must come directly from the available structured data.

---

🎯 **Goal:**
Generate a comprehensive **channel-wise weekly performance report** comparing:
- **Current Week ${currentWeekRange}** vs **Previous Week ${prevWeekRange}**
- Include **8-week rolling average** as reference baseline
- Provide **exact, numeric KPIs** for each week and compare them in table format

---

📊 **Active Integrated Channels to Analyze:**
${formattedCategories}
deeply analyse and fetch all data for the active channels listed above, take time to fetch all data and don’t skip any data of above channels

⚠️ Only analyze and query the channels listed above. If a channel is not in the above list, completely skip it from insights, campaign breakdowns, and recommendations. Do not fetch or mention any unlisted channel in this run.

---

🔍 **Tasks:**

### 1. Insights Summary (per Channel)
Create a table like this for **each active channel listed above only**:

**🔑 Insights Summary – {Channel Name}**
| KPI       | Current Week ${currentWeekRange} | Previous Week ${prevWeekRange} | 8-Week Avg | Key Segment / Driver | Root Cause / Insight |
|-----------|-----------------------------|------------------------------|------------|-----------------------|------------------------|
| ROAS      | [actual]                    | [actual]                     | [avg]      | [campaign/segment]    | [reason]               |
| Spend     | [₹actual]                   | [₹actual]                    | [₹avg]     | [driver]              | [reason]               |
| Purchases | [actual]                    | [actual]                     | [avg]      | [SKU/category]        | [reason]               |
| CTR (%)   | [actual]                    | [actual]                     | [avg]      | [audience]            | [reason]               |
| Clicks    | [actual]                    | [actual]                     | [avg]      | [placement]           | [reason]               |

Repeat this only for the active channels with important KPIs for category respectively.

---

### 2. Top 3 and Bottom 3 Campaigns (for paid channels only)
List the **Top 3 Performing** and **Bottom 3 Underperforming** campaigns **only for paid channels** in the active list: Meta Ads, Google Ads (if included).

**Top 3 Campaigns – {Channel Name}**
| Campaign         | ROAS | Spend  | Purchases | CTR  | Root Cause |
|------------------|------|--------|-----------|------|------------|
| [Name]           | [#]  | ₹[#]   | [#]       | [%]  | [reason]   |

**Bottom 3 Campaigns – {Channel Name}**
| Campaign         | ROAS | Spend  | Purchases | CTR  | Root Cause |
|------------------|------|--------|-----------|------|------------|
| [Name]           | [#]  | ₹[#]   | [#]       | [%]  | [reason]   |

Repeat only for each paid channel that is active. Skip non-paid or inactive channels.

---

### 3. Root Cause Analysis (Channel-wise)
Write a **paragraph explanation** per active channel explaining:
- What caused the KPI shifts
- Role of creative fatigue, budget changes, CTR/Impression/CPC
- Highlight if it’s a seasonal spike, auction shift, or fatigue

---

### 4. Tactical Recommendations
Give 3–6 **actionable, realistic, data-backed** recommendations based on active channels only:

| Recommendation                                     | Rooted In                    | Estimated Uplift | Impacted KPI | Priority |
|---------------------------------------------------|------------------------------|------------------|--------------|----------|
| [Action Item]                                     | [CTR drop, CPC rise, etc.]  | [ROAS +0.4]      | [ROAS]       | [High]   |

---

### 5. Next Week Strategic Focus
List 3–6 **focus areas** for upcoming week, based on the active channels only:
- Retargeting improvement
- New SKU creative testing
- Expand geo audiences
- Funnel improvements
- Underperforming channel recovery

---

### 6. Data Gaps and Issues
Mention missing or incomplete data only for the active channels:
- “Shopify checkout events missing for returning users.”
- “Google Ads data not found for current week.”

---

🧠 **Important Agent Instructions:**
- DO NOT hallucinate any value
- Query real KPIs only for channels listed as active
- Match date ranges exactly: ${prevWeekRange} vs ${currentWeekRange}
- Use rolling 8-week averages as baseline comparison
- If data is missing, fallback to last available two weeks
- All outputs must be factual, structured, and segmented channel-wise
- Follow the format exactly for consistency across all runs
- ❌ Skip entirely any channel not in the active list
don’t give follow-up question in this run`;

   return xiInput;
}
export const getEmailTemplate = (
   kpis: FinalAgg,
   prevKpis: FinalAgg,
   payload: MetricReport,
   startRange: string,
   endRange: string,
) => {
   return `<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap" rel="stylesheet">
    <style>
        * {
            font-family:  "Poppins", sans-serif;
        }
        body > div {
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding: 100px 200px;
        }
        section {
            border-top: 1px solid #E5E5E5;
            padding-top: 30px;
            margin-top: 60px;
        }
        section h2 {
            font-weight: 600;
        }
        section table {
            width: 100%;
            border: 1px solid #E5E5E5;
            border-collapse: collapse;
            border-radius: 10px;
        }
        section table th {
            font-weight: 600;
            padding: 5px 10px;
            font-size: 14px;
            background-color: #E5E5E5AA;
            text-align: left;
             border: 1px solid #E5E5E5;
        }
        section table td {
            padding: 5px 10px;
            font-size: 14px;
             border: 1px solid #E5E5E5;
        }
    </style>
</head>
<body>
    <div>
        <p>Hi ${payload.username},</p>
        <p>Here's your ${GROUP_BY_RANGE[payload.frequency] || payload.frequency} perfomance update.</p>
        
        ${
           Object.entries(kpis).length
              ? Object.entries(kpis)
                   .map(([category, kpis]) => {
                      return Object.entries(kpis).length
                         ? ` <section>
            <h2>${KPICategory[category] || category} Perfomance</h2>
            <div>

                <table>
                    <thead>
                        <tr>
                            <th>
                                Metric
                            </th>
                            <th>
                                Value (${startRange})
                            </th>
                            <th>
                                Previous period (${endRange})
                            </th>
                            <th>
                                % Change
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                    ${Object.entries(kpis as KPIAgg)
                       .map(([kpi, details]) => {
                          const prevVal =
                             (prevKpis?.[category] as KPIAgg)?.[kpi]
                                ?.totalVal || 0;
                          const val = details?.totalVal || 0;
                          const [percent, up] = getPercent(val, prevVal);
                          let showVal =
                             details?.unit == 'time'
                                ? toHHMMSS(val)
                                : getFormattedVal(Math.round(val * 100) / 100);
                          if (showVal == '0' && noZeroKPI.includes(kpi))
                             showVal = 'N/A';
                          let showPrevVal =
                             details?.unit == 'time'
                                ? toHHMMSS(prevVal)
                                : getFormattedVal(
                                     Math.round(prevVal * 100) / 100,
                                  );
                          if (showPrevVal == '0' && noZeroKPI.includes(kpi))
                             showPrevVal = 'N/A';
                          const showPercent =
                             percent != '0'
                                ? ` <td style="color: ${up ? 'green' : 'red'}"> ${up ? '&uarr;' : '&darr;'} ${percent} %</td>`
                                : '<td>0</td>';
                          return ` <tr>
                            <td>${details?.displayName}</td>
                            <td>${showVal != 'N/A' ? startEdornment[details.allData[0].kpi_type] || '' : ''} ${showVal} ${showVal != 'N/A' ? endEdornment[details.allData[0].kpi_type] || '' : ''}</td>
                            <td>${showPrevVal != 'N/A' ? startEdornment[details.allData[0].kpi_type] || '' : ''} ${showPrevVal} ${showPrevVal != 'N/A' ? endEdornment[details.allData[0].kpi_type] || '' : ''}</td>
                           ${showPercent}
                        </tr>`;
                       })
                       .join('')}
                       
                    </tbody>
                </table>
            </div>
        </section>`
                         : `<section><h2>No data available for ${KPICategory[category] || category} Metrics.</h4></section>`;
                   })
                   .join('')
              : 'No data available for selected Metrics in specified date range.'
        }
       
<footer><p>
Thank you<br>Flable AI</p></footer>
    </div>
</body>
</html>`;
};
export const noZeroKPI = [
   'cpp',
   'roas',
   'cpc',
   'cpm',
   'google_roas',
   'google_cpc',
   'google_cpm',
   'cpv',
   'cost_per_lead',
];
export const startEdornment: Edorment = {
   inr: '&#8377;',
   eur: '&#8364;',
};
export const endEdornment: Edorment = {
   percentage: '&#37;',
};
export interface Edorment {
   [key: string]: string;
}
export const getStartDate = (
   end: string,
   frequency: string,
   addDays?: number,
) => {
   const endDate = new Date(new Date(end).setHours(0, 0, 0, 0));
   endDate.setDate(
      endDate.getDate() - (GROUP_BY_DAYS[frequency] || 0) + (addDays || 0),
   );
   return endDate.toISOString();
};
export const getPercent = (val1: number, val2: number): [string, boolean] => {
   val1 = Number(val1);
   val2 = Number(val2);
   if (val1 == val2 && val1 == 0) {
      return ['0', true];
   }
   let diff = val1 - val2;
   let percentDiff;
   if (diff == 0) diff = 1;
   if (val2 <= 0) val2 = 1;
   if (diff > 0) {
      percentDiff = (diff / val2) * 100;
   } else percentDiff = ((diff * -1) / val2) * 100;
   return [
      (Math.ceil(percentDiff * 100) / 100).toLocaleString('en-IN'),
      diff > 0,
   ];
};
export const getFormattedVal = (val: number) => {
   return val.toLocaleString('en-IN');
};
export const toHHMMSS = (secs: number) => {
   const hours = Math.floor(secs / 3600);
   const minutes = Math.floor(secs / 60) % 60;
   const seconds = secs % 60;

   return [hours, minutes, seconds]
      .map((v) => (v < 10 ? '0' + v : v))
      .map((v) => v.toString().substring(0, 2))
      .join(':');
};
export const GROUP_BY_RANGE: {
   [key: string]: string;
} = {
   day: 'Daily',
   week: 'Weekly',
   month: 'Monthly',
   quarter: 'Quarterly',
};
export const GROUP_BY_DAYS: {
   [key: string]: number;
} = {
   day: 0,
   week: 6,
   month: 29,
   quarter: 89,
};
export const KPICategory: {
   [key: string]: string;
} = {
   facebookads: 'Meta Ads',
   store: 'Shopify Store',
   web: 'Web Analytics',
   pinned: 'Pinned',
   googleads: 'Google Ads',
   amazon_selling_partner: 'Amazon Selling Partner',
   amazon_ads: 'Amazon Ads',
   overall_metrics: 'Overall Metrics',
};
export interface TimeRange {
   endDate: Date;
   startDate: Date;
}
export const getLabel = (dateRange: TimeRange): string => {
   const isYearReq =
      dateRange.endDate.getFullYear() - dateRange.startDate.getFullYear();
   return `${getUserDate(dateRange.startDate, !!isYearReq)} - ${getUserDate(dateRange.endDate, !!isYearReq)}`;
};
export const getUserDate = (date: Date, isYearReq: boolean): string => {
   const month = date.toLocaleString('en-EN', { month: 'short' });
   const dateNum = date.getDate();
   const year = date.getFullYear();
   return `${dateNum} ${month}${isYearReq ? ', ' + year : ''}`;
};
export type KPI_CATEGORY =
   | 'web'
   | 'facebookads'
   | 'amazon_ads'
   | 'googleads'
   | 'amazon_selling_partner'
   | 'overall_metrics'
   | 'store';

export interface KPI {
   kpi: string;
   category: KPI_CATEGORY;
   kpi_display_name: string;
}

export const TOP_KPIS: Record<KPI_CATEGORY, KPI[]> = {
   web: [
      {
         kpi: 'conversion_rate',
         category: 'web',
         kpi_display_name: 'Conversion Rate',
      },
      { kpi: 'bounce_rate', category: 'web', kpi_display_name: 'Bounce Rate' },
      {
         kpi: 'avg_session_duration',
         category: 'web',
         kpi_display_name: 'Average Session Duration',
      },
      {
         kpi: 'avg_pages_per_session',
         category: 'web',
         kpi_display_name: 'Average Pages Per Session',
      },
      { kpi: 'new_users', category: 'web', kpi_display_name: 'New Users' },
      {
         kpi: 'total_sessions',
         category: 'web',
         kpi_display_name: 'Total Sessions',
      },
      { kpi: 'total_users', category: 'web', kpi_display_name: 'Users' },
      {
         kpi: 'cost_per_session',
         category: 'web',
         kpi_display_name: 'Cost per Session',
      },
   ],

   amazon_ads: [
      {
         kpi: 'amazon_roas',
         category: 'amazon_ads',
         kpi_display_name: 'Amazon ROAS',
      },
      {
         kpi: 'amazon_ads_gross_sales',
         category: 'amazon_ads',
         kpi_display_name: 'Amazon Ads Gross Sales',
      },
      {
         kpi: 'amazon_ads_spent',
         category: 'amazon_ads',
         kpi_display_name: 'Amazon Ads Spent',
      },
      {
         kpi: 'amazon_ads_conversion_rate',
         category: 'amazon_ads',
         kpi_display_name: 'Amazon Ads Conversion Rate',
      },
      {
         kpi: 'amazon_cpc',
         category: 'amazon_ads',
         kpi_display_name: 'Amazon CPC',
      },
      {
         kpi: 'amazon_ctr',
         category: 'amazon_ads',
         kpi_display_name: 'Amazon CTR',
      },
      {
         kpi: 'amazon_total_clicks',
         category: 'amazon_ads',
         kpi_display_name: 'Amazon Clicks',
      },
      {
         kpi: 'amazon_total_impressions',
         category: 'amazon_ads',
         kpi_display_name: 'Amazon Impressions',
      },
   ],

   amazon_selling_partner: [
      {
         kpi: 'amazon_net_sales',
         category: 'amazon_selling_partner',
         kpi_display_name: 'Amazon Net Sales',
      },
      {
         kpi: 'amazon_gross_sales',
         category: 'amazon_selling_partner',
         kpi_display_name: 'Amazon Gross Sales',
      },
      {
         kpi: 'amazon_average_order_value',
         category: 'amazon_selling_partner',
         kpi_display_name: 'Amazon Average Order Value',
      },
      {
         kpi: 'amazon_new_customers',
         category: 'amazon_selling_partner',
         kpi_display_name: 'Amazon New Customers',
      },
      {
         kpi: 'amazon_return_rate',
         category: 'amazon_selling_partner',
         kpi_display_name: 'Amazon Return Rate',
      },
      {
         kpi: 'amazon_canceled_orders',
         category: 'amazon_selling_partner',
         kpi_display_name: 'Amazon Canceled Orders',
      },
      {
         kpi: 'amazon_total_orders',
         category: 'amazon_selling_partner',
         kpi_display_name: 'Amazon Total Orders',
      },
      {
         kpi: 'amazon_total_sales',
         category: 'amazon_selling_partner',
         kpi_display_name: 'Amazon Total Sales',
      },
   ],

   facebookads: [
      {
         kpi: 'roas',
         category: 'facebookads',
         kpi_display_name: 'Facebook ROAS',
      },
      {
         kpi: 'total_spent',
         category: 'facebookads',
         kpi_display_name: 'Facebook Ads Spent',
      },
      { kpi: 'ctr', category: 'facebookads', kpi_display_name: 'Facebook CTR' },
      { kpi: 'cpc', category: 'facebookads', kpi_display_name: 'Facebook CPC' },
      {
         kpi: 'cost_per_lead',
         category: 'facebookads',
         kpi_display_name: 'Facebook CPL',
      },
      {
         kpi: 'leads',
         category: 'facebookads',
         kpi_display_name: 'Facebook Leads',
      },
      {
         kpi: 'total_impressions',
         category: 'facebookads',
         kpi_display_name: 'Impressions',
      },
      {
         kpi: 'cpp',
         category: 'facebookads',
         kpi_display_name: 'Facebook CPP',
      },
   ],

   overall_metrics: [
      {
         kpi: 'blended_roas',
         category: 'overall_metrics',
         kpi_display_name: 'Blended Roas',
      },
      {
         kpi: 'blended_net_revenue',
         category: 'overall_metrics',
         kpi_display_name: 'Net Revenue',
      },
      {
         kpi: 'blended_ad_spend',
         category: 'overall_metrics',
         kpi_display_name: 'Blended Ad Spend',
      },
      { kpi: 'mer', category: 'overall_metrics', kpi_display_name: 'MER' },
      {
         kpi: 'blended_total_orders',
         category: 'overall_metrics',
         kpi_display_name: 'Total Orders',
      },
      {
         kpi: 'blended_total_revenue',
         category: 'overall_metrics',
         kpi_display_name: 'Total Revenue',
      },
   ],

   store: [
      { kpi: 'net_sales', category: 'store', kpi_display_name: 'net_sales' },
      {
         kpi: 'total_orders',
         category: 'store',
         kpi_display_name: 'Total Orders',
      },
      {
         kpi: 'total_sales',
         category: 'store',
         kpi_display_name: 'Total Store Sales',
      },
      {
         kpi: 'average_order_value',
         category: 'store',
         kpi_display_name: 'Average Order Value',
      },
      {
         kpi: 'new_customers',
         category: 'store',
         kpi_display_name: 'New Customers Order',
      },
      {
         kpi: 'returning_customers',
         category: 'store',
         kpi_display_name: 'Returning Customers Order',
      },
      {
         kpi: 'abandoned_checkout',
         category: 'store',
         kpi_display_name: 'Abandoned Checkout',
      },
      { kpi: 'returns', category: 'store', kpi_display_name: 'returns' },
   ],
   googleads: [
      {
         kpi: 'google_ads_spent',
         category: 'googleads',
         kpi_display_name: 'Google Ads Spent',
      },
      {
         kpi: 'google_conversion_rate',
         category: 'googleads',
         kpi_display_name: 'Google Conversion Rate',
      },
      {
         kpi: 'google_cost_per_conversion',
         category: 'googleads',
         kpi_display_name: 'Google Cost Per Conversion',
      },
      {
         kpi: 'google_cpc',
         category: 'googleads',
         kpi_display_name: 'Google CPC',
      },
      {
         kpi: 'google_ctr',
         category: 'googleads',
         kpi_display_name: 'Google CTR',
      },
      {
         kpi: 'google_roas',
         category: 'googleads',
         kpi_display_name: 'Google ROAS',
      },
      {
         kpi: 'google_total_clicks',
         category: 'googleads',
         kpi_display_name: 'Google Total Clicks',
      },
      {
         kpi: 'google_total_conversions',
         category: 'googleads',
         kpi_display_name: 'Google Total Conversions',
      },
   ],
};
