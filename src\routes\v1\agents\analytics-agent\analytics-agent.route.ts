import express from 'express';
import { logger } from '../../../../config/logger';
import { AppDataSource } from '../../../../config/data-source';
import asyncHandler from '../../../../midddleware/async-handler';
import { AnalyticsAgentService } from '../../../../services/agents/analytics-agent/analytics-agent.service';
import { AnalyticsAgentController } from '../../../../controllers/agents/analytics-agent/analytics-agent.controller';
import { AnalyticsAgentModel } from '../../../../models/agents/analytics-agent/analytics-agent.model';

const router = express.Router();

const analyticsAgentModel = new AnalyticsAgentModel(AppDataSource.manager);
const analyticsAgentService = new AnalyticsAgentService(analyticsAgentModel);
const analyticsAgentController = new AnalyticsAgentController(
   logger,
   analyticsAgentService,
);

router
   .route('/:client_id/:user_id/session')
   .get(
      asyncHandler(
         analyticsAgentController.fetchAllSessionsByUserID.bind(
            analyticsAgentController,
         ),
      ),
   );

router
   .route('/:client_id/:user_id/session/:session_id')
   .get(
      asyncHandler(
         analyticsAgentController.fetchHistoryBySessionID.bind(
            analyticsAgentController,
         ),
      ),
   )
   .post(
      asyncHandler(
         analyticsAgentController.addChatToSessionHistory.bind(
            analyticsAgentController,
         ),
      ),
   );

router
   .route('/:client_id/:user_id/feature-usage')
   .get(
      asyncHandler(
         analyticsAgentController.fetchFeatureUsage.bind(
            analyticsAgentController,
         ),
      ),
   )
   .post(
      asyncHandler(
         analyticsAgentController.trackFeatureUsage.bind(
            analyticsAgentController,
         ),
      ),
   );

router
   .route('/:client_id/:user_id/session/:session_id/insights')
   .get(
      asyncHandler(
         analyticsAgentController.fetchSessionInsightsByID.bind(
            analyticsAgentController,
         ),
      ),
   )
   .post(
      asyncHandler(
         analyticsAgentController.addInsightsToSessionHistory.bind(
            analyticsAgentController,
         ),
      ),
   );

router
   .route('/:client_id/:user_id/session/:session_id/chat/:chat_id/like-dislike')
   .put(
      asyncHandler(
         analyticsAgentController.likeDislikeChat.bind(
            analyticsAgentController,
         ),
      ),
   );

router
   .route('/:client_id/:user_id/session/:session_id/chat/:chat_id/rewrite')
   .put(
      asyncHandler(
         analyticsAgentController.updateChatRewritten.bind(
            analyticsAgentController,
         ),
      ),
   );

router
   .route('/:client_id/:user_id/session/:session_id/chat/:chat_id/copied')
   .put(
      asyncHandler(
         analyticsAgentController.updateChatCopied.bind(
            analyticsAgentController,
         ),
      ),
   );

router
   .route('/:client_id/:user_id/session/:session_id/chat/:chat_id/feedback')
   .put(
      asyncHandler(
         analyticsAgentController.updateChatFeedback.bind(
            analyticsAgentController,
         ),
      ),
   );

export { router as analyticsAgentRouter };
