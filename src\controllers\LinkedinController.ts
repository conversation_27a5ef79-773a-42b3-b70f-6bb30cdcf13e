import { LinkedinService } from '../services/LinkedinService';
import { Request, Response } from 'express';
import { Logger } from 'winston';
import * as types from '../types';

export class LinkedInController {
   constructor(
      private linkedinService: LinkedinService,
      private logger: Logger,
   ) {}

   async handleFetchUserDetails(req: Request, res: Response) {
      const token = (req.body as { token: string }).token;
      const linkedinUser = await this.linkedinService.getSelfDetails(token);

      res.send({ linkedinUser });
   }
   async getUserPages(req: Request, res: Response) {
      const token = (req.body as { token: string }).token;
      const data = await this.linkedinService.getUserPages(token);

      res.send(data);
   }
   async handlePost(req: Request, res: Response) {
      const postId = await this.linkedinService.postOnLinkedin(
         req.body as types.PostOnLinkedinPayload,
      );
      this.logger.info('Posted content on Linkiedin Successfully');
      res.send({ postId });
   }
   async handleImageUpload(req: Request, res: Response) {
      const { token, userId } = req.body as { token: string; userId: string };

      if (!req.file) throw new Error('No file provided');

      const assetId = await this.linkedinService.uploadImage({
         token,
         userId,
         imageBuffer: req.file,
      });

      res.json({ assetId });
   }
}
