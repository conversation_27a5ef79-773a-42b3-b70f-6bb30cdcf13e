import { FlableService } from "../../services/Flable/FlableService";
import { Request, Response } from "express";
import { Logger } from "winston";

export class FlableController {
   constructor(
      private flableService: FlableService,
      private logger: Logger,
   ) { }

   async sendLeadEmail(req: Request, res: Response) {
      const { name, country, email } = req.body as { name: string; country: string; email: string; }
      const result = await this.flableService.sendLeadEmail(
         name, country, email
      );
      this.logger.info('Sent lead email to team');
      res.status(200).send(result);
   }
}