import { AppDataSource } from "../../config/data-source";
import { Router } from "express";
import { SubscriptionController } from "./subs.controller";
import { SubscriptionService } from "./subs.service";
import { logger } from "../../config/logger";
import asyncHandler from "../../midddleware/async-handler";
import { AzureService } from "../../services/AzureService";
import { genPDFService } from "../global/genPDFBuffer";


const router = Router()

const entityManager = AppDataSource.manager
const azureService = new AzureService(logger)
const genpdfservice = new genPDFService()


const subscriptionService = new SubscriptionService(entityManager, logger, azureService, genpdfservice)

const subscriptionController = new SubscriptionController(
    subscriptionService
)

router
    .route('/plans-info')
    .get(
        asyncHandler(
            subscriptionController.fetchPlans.bind(
                subscriptionController
            )
        )
    )
router
    .route('/agents-info')
    .get(
        asyncHandler(
            subscriptionController.fetchAgents.bind(
                subscriptionController
            )
        )
    )

router.route('/razorpay/verify-signature').post(
    asyncHandler(subscriptionController.verifyRazorpaySignature.bind(subscriptionController))
);

router.route('/razorpay/customers').post(
    asyncHandler(subscriptionController.createRazorpayCustomer.bind(subscriptionController))
);

router.route('/razorpay/subscriptions').post(
    asyncHandler(subscriptionController.createRazorpaySubscription.bind(subscriptionController))
);

router.route('/razorpay/subscriptions/cancel').post(
    asyncHandler(subscriptionController.cancelRazorpaySubscription.bind(subscriptionController))
);

router.route('/subscriptions').get(
    asyncHandler(subscriptionController.getSubscriptionContracts.bind(subscriptionController))
);
router.route('/subscriptions-history').get(
    asyncHandler(subscriptionController.getSubscriptionHistory.bind(subscriptionController))
);

router.route('/razorpay/orders').post(
    asyncHandler(subscriptionController.createRazorpayOrder.bind(subscriptionController))
);

// router.route('/orders').get(
//     asyncHandler(subscriptionController.getOrderRecords.bind(subscriptionController))
// );

router.route('/payments').get(
    asyncHandler(subscriptionController.getPaymentRecords.bind(subscriptionController))
);

router.route('/topups').get(
    asyncHandler(subscriptionController.getTopupRecords.bind(subscriptionController))
);

router.get(
    '/agent-usages',
    asyncHandler(subscriptionController.getAgentUsages.bind(subscriptionController))
);

router.get(
    '/invoices',
    asyncHandler(subscriptionController.getClientInvoices.bind(subscriptionController))
);

// router.route('/purchase-topup')
// .post(
//     asyncHandler(
//         subscriptionController.purchasTopup.bind(
//             subscriptionController
//         )
//     )
// )
router.route('/webhook')
    .post(
        asyncHandler(
            subscriptionController.handleSubscriptionWebhook.bind(
                subscriptionController
            )
        )
    )

export { router as subscriptionRouter }