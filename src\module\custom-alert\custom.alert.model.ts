import { DatabaseError } from 'pg';
import { EntityManager } from 'typeorm';
import { Config } from '../../config';
import * as types from './custom-alert-types';
import { getKeysAverage } from './custom-alert-helpers';

export class CustomAlertModel {
   constructor(private entityManager: EntityManager) {}

   async getAllAlerts(
      clientId: string,
      userId: string,
   ): Promise<types.CustomAlert[]> {
      try {
         const query = `
            SELECT * FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.custom_alerts
            WHERE client_id = $1 AND user_id = $2
            ORDER BY alert_updated_at DESC;
         `;

         return await this.entityManager.query(query, [clientId, userId]);
      } catch (error) {
         if (error instanceof DatabaseError) {
            throw new Error(`Database error: ${error.message}`);
         }
         throw error;
      }
   }

   async getAlertById(alertId: string): Promise<types.CustomAlert | null> {
      try {
         const query = `SELECT * FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.custom_alerts WHERE alert_id = $1;`;

         const result: types.CustomAlert[] = await this.entityManager.query(
            query,
            [alertId],
         );

         return result.length > 0 ? result[0] : null;
      } catch (error) {
         if (error instanceof DatabaseError) {
            throw new Error(`Database error: ${error.message}`);
         }
         throw error;
      }
   }

   async createOrUpdateAlert(payload: types.CustomAlert): Promise<boolean> {
      try {
         const {
            alert_id,
            client_id,
            user_id,
            alert_name,
            alert_description,
            email_recipients,
            alert_time,
            alert_status,
            alert_conditions,
            user_timezone,
         } = payload;

         const query = `
            SELECT ${Config.DB_Postgres_AGENTS_SCHEMA}.fn_upsert_custom_alert(
               $1,
               $2::VARCHAR,
               $3::VARCHAR,
               $4::VARCHAR,
               $5::TEXT,
               $6::TIME,
               $7::TEXT[],
               $8::VARCHAR,
               $9::JSONB,
               $10::VARCHAR
            );
         `;

         await this.entityManager.query(query, [
            alert_id || null,
            client_id,
            user_id,
            alert_name,
            alert_description,
            alert_time,
            email_recipients,
            alert_status,
            JSON.stringify(alert_conditions),
            user_timezone,
         ]);

         return true;
      } catch (error) {
         if (error instanceof DatabaseError) {
            throw new Error(`Database error: ${error.message}`);
         }
         throw error;
      }
   }

   async deleteAlert(alertId: string): Promise<void> {
      try {
         const query = `DELETE FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.custom_alerts WHERE alert_id = $1;`;

         await this.entityManager.query(query, [alertId]);
      } catch (error) {
         if (error instanceof DatabaseError) {
            throw new Error(`Database error: ${error.message}`);
         }
         throw error;
      }
   }

   async deleteMultipleAlerts(alertIds: number[]): Promise<void> {
      try {
         if (alertIds.length === 0) return;

         const query = `
            DELETE FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.custom_alerts
            WHERE alert_id = ANY($1::INTEGER[]);
         `;

         await this.entityManager.query(query, [alertIds]);
      } catch (error) {
         if (error instanceof DatabaseError) {
            throw new Error(`Database error: ${error.message}`);
         }
         throw error;
      }
   }

   async fetchOptions(
      clientId: string,
   ): Promise<types.CustomAlertOptions | null> {
      try {
         const channelsQuery = `SELECT DISTINCT(category) FROM ${Config.DB_Postgres_SCHEMA}.dashboard_kpi WHERE client_id = $1;`;
         let channels = await this.entityManager.query(channelsQuery, [
            clientId,
         ]);
         channels = channels.filter(
            (channel: { channel: string }) =>
               channel.channel !== 'overall_metrics',
         );

         if (!channels || channels.length === 0) {
            return null;
         }

         const dashboardMetricsQuery = `SELECT DISTINCT(category) category, kpi_names FROM ${Config.DB_Postgres_SCHEMA}.dashboard_kpi WHERE client_id = $1;`;
         const dashboardMetrics = await this.entityManager.query(
            dashboardMetricsQuery,
            [clientId],
         );

         const metaCampaignsQuery = `
            SELECT DISTINCT ON (campaign_id)
                  campaign_id,
                  campaign_name,
                  objective
               FROM ${Config.DB_Postgres_SCHEMA}.m_campaign_daywise
               WHERE client_id = $1
               AND LOWER(campaign_status) = 'active'
               AND DATE(date_time) = CURRENT_DATE - INTERVAL '1 day';
            `;
         const metaCampaigns = await this.entityManager.query(
            metaCampaignsQuery,
            [clientId],
         );

         const googleCampaignsQuery = `
            SELECT DISTINCT ON (campaign_id) campaign_id, campaign_name, channel_type AS objective
            FROM ${Config.DB_Postgres_SCHEMA}.m_campaign_table_googleads
            WHERE client_id = $1
            AND LOWER(campaign_status) = 'enabled'
            AND DATE(date) = CURRENT_DATE - INTERVAL '1 day';
         `;
         const googleCampaigns = await this.entityManager.query(
            googleCampaignsQuery,
            [clientId],
         );

         if (
            (!metaCampaigns || metaCampaigns.length === 0) &&
            (!googleCampaigns || googleCampaigns.length === 0)
         ) {
            return {
               channels: channels.map(
                  (row: { category: string }) => row.category,
               ),
               campaigns: {
                  meta_ads_campaigns: [],
                  google_ads_campaigns: [],
               },
               metrics: {
                  dashboard_metrics: dashboardMetrics.reduce(
                     (
                        acc: Record<
                           | 'facebookads'
                           | 'googleads'
                           | 'amazon_ads'
                           | 'store'
                           | 'web'
                           | 'amazon_selling_partner',
                           string[]
                        >,
                        row: {
                           category:
                              | 'facebookads'
                              | 'googleads'
                              | 'amazon_ads'
                              | 'store'
                              | 'web'
                              | 'amazon_selling_partner';
                           kpi_names: string;
                        },
                     ) => {
                        acc[row.category] = acc[row.category] || [];
                        acc[row.category].push(row.kpi_names);
                        return acc;
                     },
                     {},
                  ),
                  meta_campaigns_metrics: [],
                  google_campaigns_metrics: [],
               },
            };
         }

         const metaCampaignsMetricsQuery = `
            SELECT DISTINCT kpi_name
            FROM ${Config.DB_Postgres_SCHEMA}.m_campaign_daywise
            WHERE client_id = $1
            AND LOWER(campaign_status) = 'active'
            AND DATE(date_time) = CURRENT_DATE - INTERVAL '1 day';
         `;
         const metaCampaignsMetrics = await this.entityManager.query(
            metaCampaignsMetricsQuery,
            [clientId],
         );

         const googleCampaignsMetricsQuery = `
            SELECT DISTINCT kpi_name
            FROM ${Config.DB_Postgres_SCHEMA}.m_campaign_table_googleads
            WHERE client_id = $1
            AND LOWER(campaign_status) = 'enabled'
            AND DATE(date) = CURRENT_DATE - INTERVAL '1 day'`;
         const googleCampaignsMetrics = await this.entityManager.query(
            googleCampaignsMetricsQuery,
            [clientId],
         );

         return {
            channels: channels.map((row: { category: string }) => row.category),
            campaigns: {
               meta_ads_campaigns: metaCampaigns.map(
                  (row: {
                     campaign_id: string;
                     campaign_name: string;
                     objective: string;
                  }) => ({
                     id: row.campaign_id,
                     name: row.campaign_name,
                     objective: row.objective,
                  }),
               ),
               google_ads_campaigns: googleCampaigns.map(
                  (row: {
                     campaign_id: string;
                     campaign_name: string;
                     objective: string;
                  }) => ({
                     id: row.campaign_id,
                     name: row.campaign_name,
                     objective: row.objective,
                  }),
               ),
            },
            metrics: {
               dashboard_metrics: dashboardMetrics.reduce(
                  (
                     acc: Record<
                        | 'facebookads'
                        | 'googleads'
                        | 'amazon_ads'
                        | 'store'
                        | 'web'
                        | 'amazon_selling_partner',
                        string[]
                     >,
                     row: {
                        category:
                           | 'facebookads'
                           | 'googleads'
                           | 'amazon_ads'
                           | 'store'
                           | 'web'
                           | 'amazon_selling_partner';
                        kpi_names: string;
                     },
                  ) => {
                     acc[row.category] = acc[row.category] || [];
                     acc[row.category].push(row.kpi_names);
                     return acc;
                  },
                  {},
               ),
               meta_campaigns_metrics: metaCampaignsMetrics.map(
                  (row: { kpi_name: string }) => row.kpi_name,
               ),
               google_campaigns_metrics: googleCampaignsMetrics.map(
                  (row: { kpi_name: string }) => row.kpi_name,
               ),
            },
         };
      } catch (error) {
         if (error instanceof DatabaseError) {
            throw new Error(`Database error: ${error.message}`);
         }
         throw error;
      }
   }

   async fetchDashboardMetrics(
      payload: types.FetchDashboardMetricsPayload,
   ): Promise<Record<string, number> | null> {
      try {
         const { clientId, channel, metrics, period } = payload;

         if (!clientId) {
            throw new Error('Client ID is required to fetch dashboard metrics');
         }

         const query = `SELECT ${Config.DB_Postgres_AGENTS_SCHEMA}.fn_get_dashboard_kpis($1, $2, $3, $4);`;

         const dashboardMetrics = await this.entityManager.query(query, [
            clientId,
            channel,
            metrics,
            period,
         ]);

         return getKeysAverage(dashboardMetrics[0].fn_get_dashboard_kpis);
      } catch (error) {
         if (error instanceof Error) {
            throw new Error(
               `Error fetching dashboard metrics: ${error.message}`,
            );
         }

         return null;
      }
   }

   async fetchMetaCampaignMetrics(
      payload: types.FetchCampaignMetricsPayload,
   ): Promise<Record<string, number> | null> {
      try {
         const { clientId, campaign, metrics, period } = payload;

         if (!clientId) {
            throw new Error('Client ID is required to fetch dashboard metrics');
         }

         const query = `SELECT ${Config.DB_Postgres_AGENTS_SCHEMA}.fn_get_meta_campaign_kpis($1, $2, $3, $4);`;

         const metaCampaignMetrics = await this.entityManager.query(query, [
            clientId,
            campaign,
            metrics,
            period,
         ]);

         return getKeysAverage(
            metaCampaignMetrics[0].fn_get_meta_campaign_kpis,
         );
      } catch (error) {
         if (error instanceof Error) {
            throw new Error(
               `Error fetching dashboard metrics: ${error.message}`,
            );
         }

         return null;
      }
   }

   async fetchGoogleCampaignMetrics(
      payload: types.FetchCampaignMetricsPayload,
   ): Promise<Record<string, number> | null> {
      try {
         const { clientId, campaign, metrics, period } = payload;

         if (!clientId) {
            throw new Error('Client ID is required to fetch dashboard metrics');
         }

         const query = `SELECT ${Config.DB_Postgres_AGENTS_SCHEMA}.fn_get_google_campaign_kpis($1, $2, $3, $4);`;

         const googleCampaignMetrics = await this.entityManager.query(query, [
            clientId,
            campaign,
            metrics,
            period,
         ]);

         return getKeysAverage(
            googleCampaignMetrics[0].fn_get_google_campaign_kpis,
         );
      } catch (error) {
         if (error instanceof Error) {
            throw new Error(
               `Error fetching dashboard metrics: ${error.message}`,
            );
         }

         return null;
      }
   }
}
