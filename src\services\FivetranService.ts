import axios from 'axios';

import { Logger } from 'winston';
import { Config } from '../config';
import { AllSources, ConnectCard, CreateSource } from '../types/fivetran';
import { getFrontendRedirectUrl, throwCustomError } from '../utils/common';

interface FivetranErrorObject {
   response: {
      data: {
         code: string;
         message: string;
      };
   };
}

function getConnectorOptions(
   sourceType: string,
   clientId: string,
   metaData?: { isVendor?: boolean },
) {
   const sourceMapping: Record<
      string,
      Record<string, number | string | boolean | never[]>
   > = {
      google_ads: {
         schema: `${clientId}_google_ads`,
         paused: false,
         sync_mode: 'AllAccounts',
         timeframe_months: 'ALL_TIME',
         accounts: [],
         manager_accounts: [],
         reports: [],
         conversion_window_size: 30,
      },
      amazon_selling_partner: {
         schema: `${clientId}_amazon_selling_partner`,
         region: 'EUROPE',
         merchant_id: Config.AMAZON_SELLER_PARTNER_MERCHANT_ID!,
         is_vendor: metaData?.isVendor ? metaData?.isVendor : false,
      },
      amazon_ads: {
         schema: `${clientId}_amazon_ads`,
         region: 'EUROPE',
         sync_mode: 'AllProfiles',
         attribution_window: 'DAY_30',
      },
      hubspot: {
         schema: `${clientId}_hubspot`,
         historical_sync_time_frame: 'ALL_TIME',
      },
   };
   return sourceMapping[sourceType];
}

const agent = axios.create({
   baseURL: 'https://api.fivetran.com/v1',
   headers: {
      Authorization: `Basic ${Config.FIVETRAN_KEY}`,
   },
});

export class FivetranService {
   constructor(private logger: Logger) {}

   logError(err: unknown) {
      const error = err as FivetranErrorObject;
      const msg = error.response.data.message;
      this.logger.error(msg);
      return msg;
   }

   async getAllConnectors() {
      try {
         const {
            data: {
               data: { items },
            },
         } = await agent.get<AllSources>('/connectors');
         return items;
      } catch (err) {
         this.logError(err);
         return [];
      }
   }

   async getConnectorIdBySchemaName(schemaName: string) {
      const connectors = await this.getAllConnectors();

      return connectors.find((connector) => connector.schema === schemaName)
         ?.id;
   }

   async createSource(
      clientId: string,
      sourceType: string,
      metaData?: { isVendor?: boolean },
   ) {
      const config = getConnectorOptions(sourceType, clientId, metaData);
      const body = {
         service: sourceType,
         group_id: Config.FIVETRAN_GROUP_ID,
         paused: false,
         config,
      };

      const url = '/connectors';

      try {
         // delete connector if exists;
         const connectorId = await this.getConnectorIdBySchemaName(
            `${clientId}_${sourceType}`,
         );

         if (connectorId) {
            // delete the connector
            await this.deleteConnector(connectorId);
            this.logger.info(`Deleted connector ${connectorId}`);
         }

         const {
            data: {
               data: { id },
            },
         } = await agent.post<CreateSource>(url, body);
         this.logger.info('Source has been created ' + clientId);
         return { id, ok: true, msg: 'Successfully created source' };
      } catch (err) {
         const msg = this.logError(err);
         return { id: null, ok: false, msg };
      }
   }

   async createConnectCard(sourceId: string, redirectPathName: string) {
      const url = `/connectors/${sourceId}/connect-card`;

      try {
         const {
            data: {
               data: {
                  connect_card: { uri },
               },
            },
         } = await agent.post<ConnectCard>(url, {
            connect_card_config: {
               redirect_uri: getFrontendRedirectUrl(
                  Config.FRONTEND_DOMAIN?.split(',')[0] || '',
                  redirectPathName,
               ),
            },
         });
         return uri;
      } catch (err) {
         this.logError(err as FivetranErrorObject);
         return null;
      }
   }

   async deleteConnector(connectorId: string) {
      try {
         const url = `/connectors/${connectorId}`;
         await agent.delete(url);
      } catch (err) {
         const error = err as FivetranErrorObject;
         throwCustomError(error.response.data.message);
      }
   }
}
