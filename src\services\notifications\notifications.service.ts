import { NotificationsModel } from '../../models/notifications/notifications.model';
import * as types from '../../types/notifications/notifications.types';

export class NotificationsService {
   constructor(private notificationsModel: NotificationsModel) {}

   async fetchAllNotificationsByUserID(
      payload: types.FetchAllNotificationsByUserIDPayload,
   ): Promise<types.Notification[]> {
      const { user_id, client_id } = payload;

      if (!user_id || !client_id) {
         throw new Error('Invalid payload for fetching notifications');
      }

      const notifications =
         await this.notificationsModel.fetchAllNotificationsByUserID({
            user_id,
            client_id,
         });

      if (!notifications || notifications.length === 0) {
         return [];
      }

      return notifications;
   }

   async createNotification(
      payload: types.CreateNotificationPayload,
   ): Promise<void> {
      const {
         client_id,
         user_id,
         notification_title,
         notification_message,
         notification_type,
         notification_data,
      } = payload;

      if (
         !client_id ||
         !user_id ||
         !notification_title ||
         !notification_message ||
         !notification_type ||
         !notification_data
      ) {
         throw new Error('Invalid payload for adding notification');
      }

      await this.notificationsModel.createNotification(payload);
   }

   async markNotificationAsRead(
      payload: types.MarkNotificationAsReadPayload,
   ): Promise<void> {
      const { user_id, client_id, id } = payload;

      if (!user_id || !client_id || !id) {
         throw new Error('Invalid payload for fetching notifications');
      }

      await this.notificationsModel.markNotificationAsRead({
         user_id,
         client_id,
         id,
      });
   }
}
