export interface KPIPayload {
   clientId: string;
   startDate: string;
   endDate: string;
   prevStartDate:string;
   prevEndDate:string;
   compareBy: string;
}

export interface GPTResponse {
   response: string;
}
export interface ROAS {
   weightedRoas: number;
   totalSpent: number;
}
export interface CPC {
   totalSpent: number;
   totalClicks: number;
}
export interface CPP {
   totalSpent: number;
   totalPurchase: number;
}
export interface CPM {
   totalSpent: number;
   totalImpressions: number;
}
export interface CPV {
   totalSpent: number;
   totalVideoViews: number;
}
export interface VTR {
   totalP100Watched: number;
   totalImpressions: number;
}
export interface MER {
   totalRevenue: number;
   totalSpent: number;
}
export interface NetMargin {
   totalRevenue: number;
   totalNetProfit: number;
}
export interface CostPerLead {
   totalSpent: number;
   totalLeads: number;
}
export interface LeadConversionRate {
   totalClicks: number;
   totalLeads: number;
}
export interface ConversionRate {
   totalOrders: number;
   totalSessions: number;
}
export interface PurchaseRate {
   totalPurchase: number;
   totalClicks: number;
}
export interface CostPerSession {
   totalSpent: number;
   totalSessions: number;
}
export interface ACOS {
   totalSpent: number;
   totalRevenue: number;
}
export interface CPA {
   totalSpent: number;
   totalConversions: number;
}
export interface CTR {
   totalClicks: number;
   totalImpressions: number;
}

export interface FREQUENCY {
   totalImpressions: number;
   totalReach: number;
}
export interface AverageOrderValue {
   totalOrderValue: number;
   totalNonZeroDays: number;
}
export interface AverageSessionDuration {
   totalAverageSessions: number;
   totalNonZeroDays: number;
}

export interface BreakDown {
   [key: string]: {
      value: number;
      kpi_display_name: string;
      unit: string;
      category: string;
      up: boolean;
      sub_items: BreakDown | null;
      kpi_type: string;
   };
}

export interface AmazonConversionRate {
   totalOrders: number;
   totalSessions: number;
}
export interface AmazonAOV {
   totalSales: number;
   totalOrders: number;
}
export interface ShopifyAOV {
   orderRevenue: number;
   totalOrders: number;
}
export interface AmazonReturnRate {
   totalUnits: number;
   totalOrders: number;
}
export interface PageSessionDuration {
   totalPageSessions: number;
   totalNonZeroDays: number;
}
export interface BounceRate {
   bounceRates: number;
   totalNonZeroDays: number;
}
export interface ReturningCustomers {
   returningCustomers: number;
   totalNonZeroDays: number;
}
export interface NewCustomers {
   newCustomers: number;
   totalNonZeroDays: number;
}
export interface KPIData {
   date: string;
   category: string;
   kpi_unit: string;
   client_id: string;
   kpi_names: string;
   kpi_value: number;
   kpi_type: string;
   kpi_display_name: string;
   last_updated_date: string;
   load_date_and_time: string;
   endDate: string;
   totalVal?: number;
   daily_breakdown: BreakDown | null;
}

export interface PinPayload {
   clientId: string;
   category: string;
   kpis: string[];
   pinned: boolean;
}

export interface VisblePayload {
   clientId: string;
   category: string;
   kpis: string[];
   visible: boolean;
}
export interface KPIMeta {
   category: string;
   kpi: string;
   visible: boolean;
   pinned: boolean;
   kpi_display_name: string;
}
export interface KPIMetaPayload {
   clientId: string;
}
export interface PinVisibleOrderPayload {
   clientId: string;
   kpiOrder: {
      kpi: string;
      order: string;
   }[];
   pinOrder: boolean;
}

export interface PinVisibleResponse {
   [key: string]: string;
}

export interface KPIMetaRes {
   [key: string]: KPIMeta;
}

export interface KPISummaryPayload {
   data: GPTData[];
   kpi: string;
   category: string;
   currency: string;
}
export interface GPTData {
   xaxis: string;
   yaxis: number;
}

export type KPIKeys = 'roas' | 'cpc' | 'cpp' | 'cpm' | 'ctr';

export interface DateAgg {
   [key: string]: KPIList;
}

export interface KPIList {
   [key: string]: number;
}
export interface KPIDetails {
   allData: KPIData[];
   totalVal: number;
   unit: string;
   displayName: string;
   kpi_value?: number;
   date?: string;
   daily_breakdown?: BreakDown | null;
}

export interface KPIAgg {
   [key: string]: KPIDetails;
}
export interface CategoryAgg {
   [key: string]: KPIData[];
}

export type KPIAnomaly = {
   status: string;
   message: string;
   result: {
      [key: string]: {
         has_data: boolean;
         anomalies?: {
            [key: string]: boolean;
         };
         message?: string;
      };
   };
};

export type KPIAnomalyPayload = {
   client_id: string;
   start_date: string;
   end_date: string;
};

export type KPIAnomalyCausePayload = KPIAnomalyPayload & {
   kpi: string;
};

export interface AnomalyCause {
   [key: string]: string;
}

export interface FinalAgg {
   [key: string]: KPIAgg | KPIAnomaly;
}

export interface DaysIn {
   [key: string]: number;
}

export type KPIFormula = (
   day: string,
   daywiseAgg: Map<string, Record<string, number>>,
) => number;

export type KPIFormulaExtended = (
   daywiseAgg: Map<string, Record<string, number>>,
   keys: { numerator: string; denominator: string },
   day: string,
) => number;

// ------------------- Google Ads KPIs-------------------------/

export interface GoogleROAS {
   totalConversionValue: number;
   totalSpent: number;
}
export interface GoogleCPA {
   totalSpent: number;
   totalConversions: number;
}
export interface GoogleCPC {
   totalSpent: number;
   totalClicks: number;
}
export interface GoogleCPM {
   totalSpent: number;
   totalImpressions: number;
}
export interface GoogleCTR {
   totalClicks: number;
   totalImpressions: number;
}
export interface Googleconversion_rate {
   totalConversions: number;
   totalInteractions: number;
}
