import { Request, Response } from "express";
import { SubscriptionService } from "./subs.service";
import { HttpStatusCode } from "axios";
import { CreateCustomerPayload, CreateOrderPayload, CreateRazorpaySubscriptionPayload, SubscriptionWebhookEvent, VerificationInput } from "./types";

export class SubscriptionController {
    constructor(
        private subscriptionService: SubscriptionService,
    ) { }

    async fetchPlans(_req: Request, res: Response) {
        const result = await this.subscriptionService.getPlans()
        if (result) {
            res.status(HttpStatusCode.Ok).json(result)
        } else {
            res.status(HttpStatusCode.NotFound).json({ message: 'No Data found' })
        }
    }
    async fetchAgents(_req: Request, res: Response) {
        const result = await this.subscriptionService.getAgents()
        if (result) {
            res.status(HttpStatusCode.Ok).json(result)
        } else {
            res.status(HttpStatusCode.NotFound).json({ message: 'No Data found' })
        }
    }


    // eslint-disable-next-line @typescript-eslint/require-await
    async verifyRazorpaySignature(req: Request, res: Response) {
        const input = req.body as VerificationInput;

        const result = this.subscriptionService.verifyRazorpaySignature(input);

        if (result.success) {
            res.status(HttpStatusCode.Ok).json(result);
        } else {
            res.status(HttpStatusCode.BadRequest).json(result);
        }
    }

    async createRazorpayCustomer(req: Request, res: Response) {
        const {
            name,
            contact = null,
            gstin = null,
            client_id,
            email,
            billing_address
        } = req.body as CreateCustomerPayload;
        const result = await this.subscriptionService.createRazorPayCustomerId(
            {
                name,
                contact,
                gstin,
                client_id,
                email,
                billing_address
            }
        );

        if (result.success) {
            res.status(201).json(result);
        } else {
            res.status(400).json(result);
        }
    }

    async createRazorpaySubscription(req: Request, res: Response) {
        const {
            plan_id,
            plan_name,
            client_id,
            client_Phone,
            client_Email,
            rzrPay_PlanId,
            rzrPay_ClientId,
            isYearly,
            amount,
            attribute,
            autoRenew,
            extra_connectors,
            max_users,
            currency
        } = req.body as CreateRazorpaySubscriptionPayload;

        const result = await this.subscriptionService.createRazorPaySubscription(
            {
                plan_id,
                plan_name,
                client_id,
                client_Phone,
                client_Email,
                rzrPay_PlanId,
                rzrPay_ClientId,
                isYearly,
                amount,
                attribute,
                autoRenew,
                extra_connectors,
                max_users,
                currency
            }
        );

        if (result.success) {
            res.status(201).json(result);
        } else {
            res.status(400).json({ message: result.message });
        }
    }

    async cancelRazorpaySubscription(req: Request, res: Response) {
        const client_id = req.query.client_id as string;

        const result = await this.subscriptionService.checkCancelRazorPaySubscription(client_id);

        if (result.success) {
            res.status(200).json(result);
        } else {
            res.status(400).json(result);
        }
    }

    async getSubscriptionContracts(req: Request, res: Response) {
        const client_id = req.query.client_id as string;

        const result = await this.subscriptionService.getSubscriptionContractRecords(client_id);

        if (result.success) {
            res.status(200).json(result);
        } else {
            res.status(500).json(result);
        }
    }
    async getSubscriptionHistory(req: Request, res: Response) {
        const client_id = req.query.client_id as string;

        const result = await this.subscriptionService.getSubscriptionHistoryRecords(client_id);

        if (result.success) {
            res.status(200).json(result);
        } else {
            res.status(500).json(result);
        }
    }

    async createRazorpayOrder(req: Request, res: Response) {
        const {
            client_id,
            amount,
            currency,
            topup = false,
            agents,
        } = req.body as CreateOrderPayload;

        const result = await this.subscriptionService.createRazorPayOrder(
            {
                client_id,
                amount,
                currency,
                topup,
                agents
            }
        );

        if (result.success) {
            res.status(201).json(result);
        } else {
            res.status(500).json(result);
        }
    }

    // async getOrderRecords(req: Request, res: Response) {
    //     const client_id = req.query.client_id as string;

    //     const result = await this.subscriptionService.getOrderRecords(client_id);

    //     if (result.success) {
    //         res.status(200).json(result);
    //     } else {
    //         res.status(500).json(result);
    //     }
    // }

    async getPaymentRecords(req: Request, res: Response) {
        const client_id = req.query.client_id as string;

        const result = await this.subscriptionService.getPaymentRecords(client_id);

        if (result.success) {
            res.status(200).json(result);
        } else {
            res.status(500).json(result);
        }
    }

    async getTopupRecords(req: Request, res: Response) {
        const client_id = req.query.client_id as string;

        const result = await this.subscriptionService.getTopupRecords(client_id);

        if (result.success) {
            res.status(200).json(result);
        } else {
            res.status(500).json(result);
        }
    }

    async getAgentUsages(req: Request, res: Response) {
        const client_id = req.query.client_id as string;

        const result = await this.subscriptionService.getAgentUsages(client_id);

        if (result.success) {
            res.status(200).json(result);
        } else {
            res.status(500).json(result);
        }
    }

    async getClientInvoices(req: Request, res: Response) {
        const client_id = req.query.client_id as string;

        const result = await this.subscriptionService.getClientAllInvoiceRecord(client_id);

        if (result.success) {
            res.status(200).json(result);
        } else {
            res.status(500).json(result);
        }
    }


    async handleSubscriptionWebhook(req: Request, res: Response) {
        const event = req.body as SubscriptionWebhookEvent

        const result = await this.subscriptionService.SubscriptionWebhookEvents(event)

        if (result.success) {
            res.status(200).json({ message: 'done' });
        } else {
            res.status(500).json(result);
        }
    }

}