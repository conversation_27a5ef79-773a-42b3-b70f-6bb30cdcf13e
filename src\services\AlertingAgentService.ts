import { Logger } from 'winston';
import { Config } from '../config';
import { EntityManager } from 'typeorm';
import {
   AddToHistoryPayload,
   CheckAlertPayload,
   CreateAlertPayload,
   UpdateAlertPayload,
   UpdateChatHistoryPayload,
   ChatHistory,
   UpdateEmailRecipientsPayload,
   Alerts,
} from '../types/alerting-agent';
import {
   parseRecipients,
   getTableName,
   compareValues,
   mapChannel,
   createDescriptionText,
   createQuestionText,
} from '../utils/alerting-agent-helper';
import { EmailService } from './EmailService';
import { returnAlertEmail } from '../constants/email-templates';

export class AlertingAgentService {
   constructor(
      private logger: Logger,
      private entityManager: EntityManager,
      private emailService: EmailService,
   ) {}

   private async notifyRecipients(
      recipients: string[],
      alert: Alerts,
      fixedChannel: string,
      fixedComparison: string,
   ): Promise<void> {
      await Promise.all(
         recipients.map((recipient) =>
            this.sendAlertEmail(
               recipient,
               alert.alert_name,
               alert.trend.split('_').join(' '),
               alert.value,
               alert.value_type,
               alert.comparison,
               alert.comparison_type,
               fixedChannel,
               recipients.join(','),
               alert.campaign_name,
               alert.kpi,
               fixedComparison,
            ),
         ),
      );
   }

   private async fetchTodaysKPIValue(
      table: string,
      keys: string[],
      isCampaignLevel: boolean,
      dateColumn: string,
   ): Promise<number | null> {
      if (!isCampaignLevel) {
         const query = `SELECT * FROM ${table} WHERE client_id = $1 AND category = $2 AND kpi_names = $3 AND date = CURRENT_DATE - INTERVAL '1 day';`;
         const result = await this.entityManager.query(query, keys);
         return result[0]?.kpi_value ?? null;
      }

      const query = `SELECT * FROM ${table} WHERE client_id = $1 AND campaign_id = $2 AND kpi_name = $3 AND ${dateColumn} = CURRENT_DATE - INTERVAL '1 day';`;
      const result = await this.entityManager.query(query, keys);
      return result[0]?.kpi_value ?? null;
   }

   async fetchAverageKPIValue(
      table: string,
      keys: string[],
      timeframe: string,
      isCampaignLevel: boolean,
      dateColumn: string,
   ): Promise<number> {
      if (!isCampaignLevel) {
         const query = `
            SELECT AVG(kpi_value) AS avg_kpi
            FROM ${table}
            WHERE client_id = $1
            AND category = $2
            AND kpi_names = $3
            AND DATE(date) BETWEEN CURRENT_DATE - INTERVAL '${timeframe} days' AND CURRENT_DATE - INTERVAL '1 day';
         `;

         const result = await this.entityManager.query(query, keys);
         return result[0]?.avg_kpi ?? null;
      }

      const query = `
        SELECT AVG(kpi_value) AS avg_kpi
        FROM ${table}
        WHERE client_id = $1
        AND campaign_id = $2
        AND kpi_name = $3
        AND DATE(${dateColumn}) BETWEEN CURRENT_DATE - INTERVAL '${timeframe} days' AND CURRENT_DATE - INTERVAL '1 day';
      `;

      const result = await this.entityManager.query(query, keys);
      return parseFloat(result[0]?.avg_kpi ?? 'NaN');
   }

   async getAllAlerts(client_id: string) {
      try {
         const query = `SELECT * FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.temp_alerts WHERE client_id = $1;`;
         const result = await this.entityManager.query(query, [client_id]);
         return {
            response: {
               status: 'Success',
               message: 'Alerts fetched successfully',
               data: result,
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'getAllAlerts',
         });
         throw new Error(error.message);
      }
   }

   async createAlert(payload: CreateAlertPayload) {
      try {
         const {
            client_id,
            chat_id,
            alert_name,
            alert_description,
            alert_instruction,
            recipients,
            channel,
            campaign_name,
            campaign_id,
            kpi,
            trend,
            value,
            value_type,
            comparison,
            comparison_type,
            alert_status,
            alert_time,
            alert_timeframe,
         } = payload;

         const query = `
            INSERT INTO ${Config.DB_Postgres_AGENTS_SCHEMA}.temp_alerts 
            (client_id, chat_id, alert_name, alert_description, alert_instruction, recipients, channel, campaign_name, campaign_id, kpi, trend, value, value_type, comparison, comparison_type, alert_status, alert_time, alert_timeframe) 
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18) 
            RETURNING *;
         `;

         const result = await this.entityManager.query(query, [
            client_id,
            chat_id,
            alert_name,
            alert_description,
            alert_instruction,
            recipients,
            channel,
            campaign_name,
            campaign_id,
            kpi,
            trend,
            value,
            value_type,
            comparison,
            comparison_type,
            alert_status,
            alert_time,
            alert_timeframe,
         ]);

         return {
            response: {
               status: 'Success',
               message: 'Alert created successfully',
               data: result[0],
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'createAlert',
         });
         throw new Error(error.message);
      }
   }

   async getAlertById(alert_id: string) {
      try {
         const query = `SELECT * FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.temp_alerts WHERE alert_id = $1;`;
         const result = await this.entityManager.query(query, [
            Number(alert_id),
         ]);
         return {
            response: {
               status: 'Success',
               message: 'Alert fetched successfully',
               data: result[0],
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'getAlertById',
         });
         throw new Error(error.message);
      }
   }

   async updateAlert(payload: UpdateAlertPayload) {
      try {
         const {
            alert_id,
            client_id,
            chat_id,
            alert_name,
            alert_description,
            recipients,
            channel,
            kpi,
            trend,
            value,
            value_type,
            comparison,
            comparison_type,
            alert_status,
            alert_time,
            campaign_id,
            campaign_name,
            alert_timeframe,
         } = payload;

         const query = `
         UPDATE ${Config.DB_Postgres_AGENTS_SCHEMA}.temp_alerts
         SET 
            client_id = $2,
            chat_id = $3,
            alert_name = $4,
            alert_description = $5,
            recipients = $6,
            channel = $7,
            kpi = $8,
            trend = $9,
            value = $10,
            value_type = $11,
            comparison = $12,
            comparison_type = $13,
            alert_status = $14,
            alert_time = $15,
            campaign_id = $16,
            campaign_name = $17,
            alert_timeframe = $18,
            updated_at = CURRENT_TIMESTAMP
         WHERE alert_id = $1
         RETURNING *;
      `;

         const values = [
            alert_id,
            client_id,
            chat_id,
            alert_name,
            alert_description,
            recipients,
            channel,
            kpi,
            trend,
            value,
            value_type,
            comparison,
            comparison_type,
            alert_status,
            alert_time,
            campaign_id,
            campaign_name,
            alert_timeframe,
         ];

         const result = await this.entityManager.query(query, values);

         return {
            response: {
               status: 'Success',
               message: 'Alert updated successfully',
               data: result[0][0],
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'updateAlert',
         });
         throw new Error(error.message);
      }
   }

   async deleteAlert(alert_id: string) {
      try {
         const query = `DELETE FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.temp_alerts WHERE alert_id = $1;`;
         await this.entityManager.query(query, [alert_id]);

         return {
            response: {
               status: 'Success',
               message: 'Alert deleted successfully',
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'deleteAlert',
         });
         throw new Error(error.message);
      }
   }

   async deleteMultipleAlerts(payload: { alert_ids: number[] }) {
      try {
         const { alert_ids } = payload;

         const query = `DELETE FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.temp_alerts WHERE alert_id = ANY($1);`;
         await this.entityManager.query(query, [[alert_ids.map(Number)]]);

         return {
            response: {
               status: 'Success',
               message: 'Alerts deleted successfully',
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'deleteMultipleAlerts',
         });
         throw new Error(error.message);
      }
   }

   async pauseUnpauseAlert(alert_id: string) {
      try {
         const query = `UPDATE ${Config.DB_Postgres_AGENTS_SCHEMA}.temp_alerts 
         SET alert_status = 
             CASE 
                 WHEN alert_status = 'PAUSED' THEN 'ACTIVE' 
                 ELSE 'PAUSED' 
             END 
         WHERE alert_id = $1`;
         await this.entityManager.query(query, [alert_id]);

         return {
            response: {
               status: 'Success',
               message: 'Alert status updated successfully',
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'pauseUnpauseAlert',
         });
         throw new Error(error.message);
      }
   }

   async updateEmailRecipients(payload: UpdateEmailRecipientsPayload) {
      try {
         const { alert_id, recipients } = payload;

         const query = `UPDATE ${Config.DB_Postgres_AGENTS_SCHEMA}.temp_alerts
         SET recipients = $1
         WHERE alert_id = $2;`;
         await this.entityManager.query(query, [recipients, alert_id]);

         return {
            response: {
               status: 'Success',
               message: 'Email recipients updated successfully',
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'addEmailRecipients',
         });
         throw new Error(error.message);
      }
   }

   private async processAlert(alert: Alerts): Promise<void> {
      const {
         client_id,
         campaign_id,
         kpi,
         value,
         value_type,
         trend,
         comparison_type,
         comparison,
         channel,
         recipients,
         alert_timeframe,
      } = alert;

      const isCampaignLevel = !!campaign_id;
      const dateColumn = channel === 'facebookads' ? 'date_time' : 'date';
      const fixedRecipients = parseRecipients(recipients);
      const fixedChannel = mapChannel(channel);
      const table = getTableName(channel, isCampaignLevel);
      const fixedComparison = comparison
         .split('_')
         .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
         .join(' ');

      const keys = campaign_id
         ? [client_id, campaign_id, kpi]
         : [client_id, channel, kpi];

      const todaysValue = await this.fetchTodaysKPIValue(
         table,
         keys,
         isCampaignLevel,
         dateColumn,
      );

      if (!todaysValue || Number.isNaN(todaysValue)) {
         this.logger.info(`No data found today for alert ${alert.alert_id}`);
         return;
      }

      if (value_type === 'number') {
         switch (trend) {
            case 'More_than':
               if (todaysValue > Number(value)) {
                  await this.notifyRecipients(
                     fixedRecipients,
                     alert,
                     fixedChannel,
                     fixedComparison,
                  );
                  this.logger.info('Alert Email sent successfully');
                  return;
               }
               break;
            case 'Less_than':
               if (todaysValue < Number(value)) {
                  await this.notifyRecipients(
                     fixedRecipients,
                     alert,
                     fixedChannel,
                     fixedComparison,
                  );
                  this.logger.info('Alert Email sent successfully');
                  return;
               }
               break;
         }

         return;
      }

      let baseValue: number | null;

      if (comparison_type === 'kpi') {
         const keys = isCampaignLevel
            ? [client_id, campaign_id, comparison]
            : [client_id, channel, comparison];
         baseValue = await this.fetchTodaysKPIValue(
            table,
            keys,
            isCampaignLevel,
            dateColumn,
         );
      } else {
         baseValue = await this.fetchAverageKPIValue(
            table,
            keys,
            alert_timeframe,
            isCampaignLevel,
            dateColumn,
         );
      }

      if (!baseValue || Number.isNaN(baseValue)) {
         this.logger.info(`No data found today for alert ${alert.alert_id}`);
         return;
      }

      const threshold = parseFloat(value);
      const shouldAlert = compareValues(
         todaysValue,
         baseValue,
         trend,
         threshold,
      );

      if (shouldAlert) {
         await this.notifyRecipients(
            fixedRecipients,
            alert,
            fixedChannel,
            fixedComparison,
         );
         this.logger.info('Alert Email sent successfully');
      }
   }

   async checkAlerts(payload: CheckAlertPayload) {
      try {
         for (const alert of payload.alerts) {
            await this.processAlert(alert);
         }
         return { response: 'All alerts checked successfully' };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'checkAlerts',
         });
         throw new Error(error.message);
      }
   }

   async addToChatHistory(payload: AddToHistoryPayload) {
      try {
         const { chat_id, client_id, session_id, chat_name, chat_history } =
            payload;

         const addToHistoryQuery = chat_id
            ? `
            INSERT INTO ${Config.DB_Postgres_AGENTS_SCHEMA}.temp_alerts_history (client_id, session_id, chat_name, chat_history, chat_id) 
            VALUES ($1, $2, $3, $4, $5) ON CONFLICT (chat_id) 
            DO UPDATE SET chat_history = $4, updated_at = CURRENT_TIMESTAMP RETURNING *;`
            : `
            INSERT INTO ${Config.DB_Postgres_AGENTS_SCHEMA}.temp_alerts_history (client_id, session_id, chat_name, chat_history) 
            VALUES ($1, $2, $3, $4) RETURNING *;`;

         const result = await this.entityManager.query(
            addToHistoryQuery,
            chat_id
               ? [
                    client_id,
                    session_id,
                    chat_name,
                    JSON.stringify(chat_history),
                    chat_id,
                 ]
               : [
                    client_id,
                    session_id,
                    chat_name,
                    JSON.stringify(chat_history),
                 ],
         );

         return {
            response: {
               status: 'Success',
               message: 'Chat history updated successfully',
               data: result[0],
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'addToChatHistory',
         });
         throw new Error(error.message);
      }
   }

   async updateChatHistory(payload: UpdateChatHistoryPayload) {
      try {
         const { chat_id, chat_history } = payload;

         const updateChatHistoryQuery = `
            UPDATE ${Config.DB_Postgres_AGENTS_SCHEMA}.temp_alerts_history 
            SET chat_history = $2, updated_at = NOW()
            WHERE chat_id = $1 
            RETURNING *;`;

         const result = await this.entityManager.query(updateChatHistoryQuery, [
            chat_id,
            JSON.stringify(chat_history),
         ]);

         return {
            response: {
               status: 'Success',
               message: 'Chat history updated successfully',
               data: result[0],
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'updateChatHistory',
         });
         throw new Error(error.message);
      }
   }

   async getChatHistoryById(chat_id: string) {
      try {
         const query = `
            SELECT * FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.temp_alerts_history 
            WHERE chat_id = $1;`;

         const result = await this.entityManager.query(query, [chat_id]);

         return {
            response: {
               status: 'Success',
               message: 'Chat history fetched successfully',
               data: result[0],
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'getChatHistoryById',
         });
         throw new Error(error.message);
      }
   }

   private async sendAlertEmail(
      recipient: string,
      alert_name: string,
      trend: string,
      value: string,
      value_type: string,
      comparison: string,
      comparison_type: string,
      channel: string,
      recipients: string,
      campaign_name: string,
      kpi: string,
      fixedComparison: string,
   ) {
      const descriptionText = createDescriptionText(
         channel,
         trend,
         value,
         value_type,
         comparison,
         campaign_name,
         kpi,
      );

      const questionText = createQuestionText(
         channel,
         trend,
         value,
         value_type,
         comparison,
         campaign_name,
         kpi,
      );

      const emailInfo = {
         to: recipient,
         subject: 'Flable AI Alert',
         text: 'Flable AI Alert',
         html: returnAlertEmail(
            recipient,
            alert_name,
            trend,
            value,
            value_type,
            fixedComparison,
            comparison_type,
            channel,
            recipients,
            descriptionText,
            questionText,
         ),
      };

      await this.emailService.sendEmail(emailInfo);
      this.logger.info('Alert Email sent successfully');
   }

   async getChatHistory(client_id: string) {
      try {
         const query = `SELECT * FROM ${Config.DB_Postgres_AGENTS_SCHEMA}.temp_alerts_history WHERE client_id = $1;`;
         const result = await this.entityManager.query(query, [client_id]);

         return {
            response: {
               status: 'Success',
               message: 'Chat history fetched successfully',
               data: result,
            },
         };
      } catch (err) {
         const error = err as Error;
         this.logger.error(error.message, {
            serviceFunction: 'getChatHistory',
         });
         throw new Error(error.message);
      }
   }
}
