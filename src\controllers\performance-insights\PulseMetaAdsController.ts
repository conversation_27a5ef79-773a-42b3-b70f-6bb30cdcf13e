import { Request, Response } from 'express';
import { PulseMetaAdsService } from '../../services/performance-insights/PulseMetaAdsService';
import * as types from '../../types';
import { Logger } from 'winston';

export class PulseMetaAdsController {
   constructor(
      private pulseMetaAdsService: PulseMetaAdsService,
      private logger: Logger,
   ) {}

   async fetchObjectives(req: Request, res: Response) {
      const response = await this.pulseMetaAdsService.fetchObjectives(
         req.query as { client_id: string },
      );

      if (response) {
         this.logger.info('Meta objectives fetched successfully');
         res.status(200).json(response);
      } else {
         this.logger.error('Error while fetching meta objectives');
      }
   }

   async fetchTrackedCampaigns(req: Request, res: Response) {
      const response = await this.pulseMetaAdsService.fetchTrackedCampaigns(
         req.query as { client_id: string },
      );

      if (response) {
         this.logger.info('Data fetched successfully');
         res.status(200).json(response);
      } else {
         this.logger.error('Error while fetching data');
      }
   }

   async fetchCampaignsWithBudgetSpend(req: Request, res: Response) {
      const result =
         await this.pulseMetaAdsService.fetchCampaignsWithBudgetSpend(
            req.query as unknown as types.FetchCampaignsWithBudgetSpendPayload,
         );

      if (result) {
         res.status(200).json(result);
      } else {
         res.status(404).json({ message: 'No data found' });
      }
   }

   async fetchCampaignsDaywise(req: Request, res: Response) {
      const response = await this.pulseMetaAdsService.fetchCampaignsDaywise(
         req.query as unknown as types.FetchCampaignDaywisePayload,
      );

      if (response) {
         res.status(200).json(response);
      } else {
         res.status(404).json({ message: 'No data found' });
      }
   }

   async fetchAdsetsDaywise(req: Request, res: Response) {
      const response = await this.pulseMetaAdsService.fetchAdsetsDaywise(
         req.query as unknown as types.FetchAdsetDaywisePayload,
      );

      if (response) {
         res.status(200).json(response);
      } else {
         res.status(404).json({ message: 'No data found' });
      }
   }

   async fetchAdsDaywise(req: Request, res: Response) {
      const response = await this.pulseMetaAdsService.fetchAdsDaywise(
         req.query as unknown as types.FetchAdsDaywisePayload,
      );

      if (response) {
         res.status(200).json(response);
      } else {
         res.status(404).json({ message: 'No data found' });
      }
   }

   async fetchTargetingDaywise(req: Request, res: Response) {
      const response = await this.pulseMetaAdsService.fetchTargetingDaywise(
         req.query as unknown as types.FetchTargetingDaywisePayload,
      );

      if (response) {
         res.status(200).json(response);
      } else {
         res.status(404).json({ message: 'No data found' });
      }
   }

   async fetchChartInsights(req: Request, res: Response) {
      const response = await this.pulseMetaAdsService.fetchChartInsights(
         req.body as types.FetchChartInsightsPayload,
      );

      if (response) {
         res.status(200).json(response);
      } else {
         res.status(404).json({ message: 'No data found' });
      }
   }

   async fetchBenchmarkInsights(req: Request, res: Response) {
      const response = await this.pulseMetaAdsService.fetchBenchmarkInsights(
         req.body as types.FetchBenchmarkInsightsPayload,
      );

      if (response) {
         res.status(200).json(response);
      } else {
         res.status(404).json({ message: 'No data found' });
      }
   }

   async fetchAdsetInsights(req: Request, res: Response) {
      const response = await this.pulseMetaAdsService.fetchAdsetInsights(
         req.body as types.FetchAdsetInsightsPayload,
      );

      if (response) {
         res.status(200).json(response);
      } else {
         res.status(404).json({ message: 'No data found' });
      }
   }

   async fetchTargetingInsights(req: Request, res: Response) {
      const response = await this.pulseMetaAdsService.fetchTargetingInsights(
         req.body as types.FetchTargetingInsightsPayload,
      );

      if (response) {
         this.logger.info('Data fetched successfully');
         res.status(200).json(response);
      } else {
         this.logger.error('Error while fetching summary');
      }
   }

   async fetchTrackedCampaignKPIs(req: Request, res: Response) {
      const response = await this.pulseMetaAdsService.fetchTrackedCampaignKPIs(
         req.body as types.FetchTrackedCampaignKPIsPayload,
      );
      if (response) {
         this.logger.info('Data fetched successfully');
         res.status(200).json(response);
      } else {
         this.logger.error('Error while fetching data');
      }
   }

   async updateTrackedKpis(req: Request, res: Response) {
      const { client_id, objective, kpi_name, campaign_id, tracked, channel } =
         req.body as types.UpdateUserTrack;
      const response = await this.pulseMetaAdsService.updateUserTrack(
         client_id,
         objective,
         kpi_name,
         campaign_id,
         tracked,
         channel,
      );
      if (response) {
         this.logger.info('Updated successfully');
         res.status(200).json(response);
      } else {
         this.logger.error('Error while updating');
      }
   }

   async fetchTrackedPrevKpis(req: Request, res: Response) {
      const {
         client_id,
         channel,
         start_date,
         end_date,
         prev_start_date,
         prev_end_date,
      } = req.body as types.GetTrackedPrevKpis;
      const response = await this.pulseMetaAdsService.fetchAggregatePrevKpis(
         client_id,
         channel,
         start_date,
         end_date,
         prev_end_date,
         prev_start_date,
      );
      if (response) {
         this.logger.info('Data fetched successfully');
         res.status(200).json(response);
      } else {
         this.logger.error('Error while fetching data');
      }
   }
}
