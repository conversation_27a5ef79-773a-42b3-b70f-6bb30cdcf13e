import { Request, Response } from 'express';
import { <PERSON>gger } from 'winston';
import * as types from '../../../types/agents/alerting-agent/alerting-agent.types';
import { AlertingAgentService } from '../../../services/agents/alerting-agent/alerting-agent.service';

export class AlertingAgentController {
   constructor(
      private logger: Logger,
      private alertingAgentService: AlertingAgentService,
   ) {}

   async fetchAllSessionsByUserID(req: Request, res: Response) {
      try {
         const result =
            await this.alertingAgentService.fetchAllSessionsByUserID({
               ...req.params,
               ...req.query,
            } as unknown as types.FetchAllSessionsByUserIDPayload);

         if (!result) {
            return res
               .status(404)
               .json({ error: 'Failed to fetch all sessions' });
         }

         res.status(200).json(result);
      } catch (error) {
         this.logger.error('Error fetching sessions:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }

   async fetchHistoryBySessionID(req: Request, res: Response) {
      try {
         const result = await this.alertingAgentService.fetchHistoryBySessionID(
            req.params as unknown as types.FetchHistoryBySessionIDPayload,
         );

         if (!result) {
            return res
               .status(404)
               .json({ error: 'Failed to fetch session history' });
         }

         res.status(200).json(result);
      } catch (error) {
         this.logger.error('Error fetching session history:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }

   async addChatToSessionHistory(req: Request, res: Response) {
      try {
         await this.alertingAgentService.addChatToSessionHistory({
            ...req.params,
            ...req.body,
         } as types.AddChatToSessionHistoryPayload);

         res.status(201).send({
            message: 'Chat added to session history successfully',
         });
      } catch (error) {
         this.logger.error('Error adding chat to session history:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }

   async fetchChatByChatId(req: Request, res: Response) {
      try {
         const result = await this.alertingAgentService.fetchChatByChatId(
            req.params as unknown as types.FetchChatByChatIdPayload,
         );

         if (!result) {
            return res.status(404).json({ error: 'Failed to fetch chat' });
         }

         res.status(200).json(result);
      } catch (error) {
         this.logger.error('Error fetching chat:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }

   async fetchAlertsBySessionID(req: Request, res: Response) {
      try {
         const result = await this.alertingAgentService.fetchAlertsBySessionID(
            req.params as unknown as types.FetchAlertsBySessionIDPayload,
         );

         if (!result) {
            return res
               .status(404)
               .json({ error: 'Failed to fetch alerts for session' });
         }

         res.status(200).json(result);
      } catch (error) {
         this.logger.error('Error fetching alerts by session ID:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }

   async fetchAllAlerts(req: Request, res: Response) {
      try {
         const result = await this.alertingAgentService.fetchAllAlerts(
            req.params as unknown as types.FetchAllAlertsPayload,
         );

         if (!result) {
            return res
               .status(404)
               .json({ error: 'Failed to fetch all alerts' });
         }

         res.status(200).json(result);
      } catch (error) {
         this.logger.error('Error fetching alerts:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }

   async fetchAlertById(req: Request, res: Response) {
      try {
         const result = await this.alertingAgentService.fetchAlertByID(
            req.params as unknown as types.FetchAlertByIdPayload,
         );

         if (!result) {
            return res.status(404).json({ error: 'Failed to fetch alert' });
         }

         res.status(200).json(result);
      } catch (error) {
         this.logger.error('Error fetching alert:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }

   async createAlert(req: Request, res: Response) {
      try {
         const result = await this.alertingAgentService.createAlert({
            ...req.params,
            ...req.body,
         } as types.CreateAlertPayload);

         res.status(201).send(result);
      } catch (error) {
         this.logger.error('Error creating alert:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }

   async updateAlert(req: Request, res: Response) {
      try {
         const result = await this.alertingAgentService.updateAlert({
            ...req.params,
            ...req.body,
         } as types.UpdateAlertPayload);

         res.status(204).send(result);
      } catch (error) {
         this.logger.error('Error updating alert:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }

   async deleteAlert(req: Request, res: Response) {
      try {
         await this.alertingAgentService.deleteAlert(
            req.params as unknown as types.DeleteAlertPayload,
         );

         res.status(204).send({
            message: 'Alert deleted successfully',
         });
      } catch (error) {
         this.logger.error('Error deleting alert:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }

   async deleteMultipleAlerts(req: Request, res: Response) {
      try {
         await this.alertingAgentService.deleteMultipleAlerts({
            ...req.params,
            ...req.body,
         } as types.DeleteMultipleAlertsPayload);

         res.status(204).send({
            message: 'Alerts deleted successfully',
         });
      } catch (error) {
         this.logger.error('Error deleting multiple alerts:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }

   async pauseUnpauseAlert(req: Request, res: Response) {
      try {
         await this.alertingAgentService.pauseUnpauseAlert(
            req.params as unknown as types.PauseUnpauseAlertPayload,
         );

         res.status(204).send({
            message: 'Alert paused/unpaused successfully',
         });
      } catch (error) {
         this.logger.error('Error pausing/unpausing alert:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }

   async updateEmailRecipients(req: Request, res: Response) {
      try {
         await this.alertingAgentService.updateEmailRecipients({
            ...req.params,
            ...req.body,
         } as types.UpdateEmailRecipientsPayload);

         res.status(204).send({
            message: 'Email recipients updated successfully',
         });
      } catch (error) {
         this.logger.error('Error updating email recipients:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }

   async checkAlertCriteria(req: Request, res: Response) {
      try {
         await this.alertingAgentService.checkAlertCriteria(
            req.body as { alerts: types.AlertingAgentAlert[] },
         );

         res.status(200).json({
            message: 'Alert criteria checked successfully',
         });
      } catch (error) {
         this.logger.error('Error checking alert criteria:', error);
         res.status(500).json({ error: 'Internal Server Error' });
      }
   }
}
