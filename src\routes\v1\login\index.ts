import express from 'express';
import asyncHandler from '../../../midddleware/async-handler';

import { LoginService } from '../../../services/LoginService';
import { AppDataSource } from '../../../config/data-source';
import { Login } from '../../../entity/Login';
import { logger } from '../../../config/logger';
import { LoginController } from '../../../controllers/LoginController';

const router = express.Router();

const loginRepository = AppDataSource.getRepository(Login);
const loginService = new LoginService(loginRepository);
const loginController = new LoginController(loginService, logger);

router
   .route('/')
   .post(asyncHandler(loginController.handleLogin.bind(loginController)));

export { router as loginRouter };
