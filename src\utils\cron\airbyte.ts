import { Config } from '../../config';
import { logger } from '../../config/logger';
import { Pool } from 'pg';
import mongoose from 'mongoose';

const getPostgreConfig = () => {
   return {
      user: Config.DB_Postgres_USER,
      host: Config.DB_Postgres_HOST,
      database: Config.DB_Postgres_DATABASE,
      password: Config.DB_Postgres_PASSWORD,
      port: Number(Config.DB_Postgres_PORT),
   };
};

// const connections = [
//    {
//       clientId: 'A01002',
//       connectionId: '123',
//       accountId: '***************',
//    },
// ];

interface Connection {
   account_ids: string;
   client_id: string;
}

const pool = new Pool(getPostgreConfig());

async function getConnections() {
   try {
      const client = await pool.connect();
      if (!client) throw new Error('PG cannot be connected');
      const query = `SELECT
      account_ids, client_id
    FROM
      ${Config.DB_Postgres_SCHEMA}"."social_analytics" where channel_name = 'facebookads'`;
      const response = await client.query(query);

      if (!response || response.rowCount === 0) {
         return [];
      }

      return response.rows;
   } catch (err) {
      const error = err as Error;
      logger.error(error.message);
      return [];
   }
}

async function shallIncludeDateQueryInMongo(tableName: string) {
   try {
      const client = await pool.connect();
      if (!client) throw new Error('PG cannot be connected');
      const query = `SELECT *
    FROM
      "public"."${tableName}"`;
      const response = await client.query(query);

      if (!response || response.rowCount === 0) {
         logger.info(
            'Data does not exist hence no need to include the date in the query',
         );
         return false;
      }
      logger.info('Data exists hence need to include the date in the query');
      return true;
   } catch (err) {
      const error = err as Error;
      logger.error(error.message);
      return false;
   }
}

async function insertIntoPostgres(
   results: Array<{ tableName: string; data: any[] }>,
): Promise<void> {
   try {
      // Create table if not exists
      const client = await pool.connect();

      if (!client) throw new Error('PG cannot be connected');

      await client.query('BEGIN');

      for (const result of results) {
         const { tableName, data } = result;

         // Create table if not exists
         const createTableQuery = `
          CREATE TABLE IF NOT EXISTS ${tableName} (
            id SERIAL PRIMARY KEY,
            data JSONB
          );
        `;
         await client.query(createTableQuery);

         // Insert data using batch insert
         const insertQuery = `INSERT INTO ${tableName} (data) VALUES ($1)`;
         const insertPromises = data.map((item) =>
            client.query(insertQuery, [item]),
         );
         await Promise.all(insertPromises);
      }

      await client.query('COMMIT');

      client.release();
   } catch (error) {
      logger.error('Error inserting data into PostgreSQL:', error);
      throw error;
   }
}

const fetchAndProcessData = async () => {
   try {
      const db = mongoose.connection.db;

      if (!db) throw new Error('Database connection not established');

      const connections = await getConnections();
      const collections = await getFilteredCollections(db);

      const today = new Date();
      const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);

      await processConnections(connections, collections, today, tomorrow);
   } catch (error) {
      logger.error('Error fetching and processing data:', error);
      throw error;
   }
};

const getFilteredCollections = async (db: mongoose.mongo.Db) => {
   const collections = await db.listCollections().toArray();
   return collections.filter((collection) =>
      collection.name.startsWith('airbyte'),
   );
   // .slice(0, 1);
};

const getQuery = async (
   accountId: string,
   tableName: string,
   today: Date,
   tomorrow: Date,
) => {
   // const shallInclude = await shallIncludeDateQueryInMongo(tableName);
   const query: Record<string, any> = {
      '_airbyte_data.account_id': accountId,
   };
   // if (shallInclude) {
   //    query._airbyte_emitted_at = {
   //       $gte: today.toISOString(),
   //       $lt: tomorrow.toISOString(),
   //    };
   // }
   return query;
};

const fetchCollectionData = async (
   db: mongoose.mongo.Db,
   collection:
      | mongoose.mongo.CollectionInfo
      | Pick<mongoose.mongo.CollectionInfo, 'name' | 'type'>,
   accountId: string,
   clientId: string,
   today: Date,
   tomorrow: Date,
) => {
   const query = await getQuery(accountId, collection.name, today, tomorrow);
   const collectionData = await db
      .collection(collection.name)
      .find(query)
      .toArray();
   return {
      tableName: collection.name,
      data: collectionData.map((item) => ({
         ...item,
         clientId,
      })),
   };
};

const processConnections = async (
   connections: Connection[],
   collections: (
      | mongoose.mongo.CollectionInfo
      | Pick<mongoose.mongo.CollectionInfo, 'name' | 'type'>
   )[],
   today: Date,
   tomorrow: Date,
) => {
   for (const connection of connections) {
      const { client_id, account_ids } = connection;
      const fetchPromises = collections.map((collection) =>
         fetchCollectionData(
            mongoose.connection.db,
            collection,
            account_ids,
            client_id,
            today,
            tomorrow,
         ),
      );
      const results = await Promise.all(fetchPromises);
      await insertIntoPostgres(results);
   }
};

export { fetchAndProcessData };
