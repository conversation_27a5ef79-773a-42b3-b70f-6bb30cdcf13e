import axios from 'axios';
import { Config } from '../config/index';

interface WhatsappPayload {
   apiKey: string;
   campaignName: string;
   destination: string;
   userName: string;
   templateParams: string[];
   source: string;
   media?: object;
   buttons?: any[];
   carouselCards?: any[];
   location?: object;
   attributes?: object;
   paramsFallbackValue?: object;
}

export class WhatsappService {
   async sendWhatsapp(payload: WhatsappPayload): Promise<unknown> {
      try {
         const response = await axios.post(
            'https://backend.aisensy.com/campaign/t1/api/v2',
            payload,
            {
               headers: {
                  'Content-Type': 'application/json',
               },
            },
         );
         return response.data;
      } catch (error: unknown) {
         console.log(error, 'whatsapp error');
         if (axios.isAxiosError(error)) {
            throw new Error(
               error.response?.data?.message ||
                  error.message ||
                  'Failed to send WhatsApp alert',
            );
         }
         if (error instanceof Error) {
            throw new Error(error.message);
         }
         throw new Error('Failed to send WhatsApp alert');
      }
   }
}
