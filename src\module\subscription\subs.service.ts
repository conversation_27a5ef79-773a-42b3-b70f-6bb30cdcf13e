import { Config } from "../../config";
import { EntityManager } from "typeorm";
import { Logger } from "winston";
import razorpayApi from "../global/razorpayApi";
import * as crypto from 'crypto';
import { AzureService } from "../../services/AzureService";
import { genPDFService } from "../global/genPDFBuffer";
import { GenerateInvoiceParams } from "../global/Interface/genPDFBuffer";
import { buildInvoiceItems, buildInvoiceNotes, getAddressBlock, getCompanyContact, getCompanyInfo, getInvoiceInfo, getInvoiceTotals } from "./helper";
import { Agents, AgentUsageRecordsDTO, ClientDTO, CreateCustomerPayload, CreateInvocieRecordPayload, CreateOrderPayload, CreateRazorpaySubscriptionPayload, InvoiceDTO, InvoiceRecordsDTO, InvoiceRecordsResponseDTO, Notes, PaymentRecordsDTO, Plans, RazorpayCustomerApiResponse, RazorpayOrderApiResponse, RazorpaySubscriptionApiResponse, SubscriptionHistoryRecordDTO, SubscriptionRecordDTO, SubscriptionWebhookEvent, SubsIntsertPayload, TopupItemsDTO, TopupRecordsDTO, UpdateClientOrderStatusPayload, UpdateClientPaymentStatusPayload, UpdateClientTopupPayload, UpdateSubscriptionContractPayload, VerificationInput } from "./types";

export class SubscriptionService {
    constructor(
        private entityManager: EntityManager,
        private logger: Logger,
        private azureService: AzureService,
        private genpdfservice: genPDFService,

    ) { }

    //#region GLOBAL
    async getPlans(): Promise<Plans[]> {
        const query = `
        SELECT * FROM ${Config.DB_Postgres_SUBS_SCHEMA}.plans
        ORDER BY tier
        `
        const plans: Plans[] = await this.entityManager.query(query)
        return plans
    }

    async getAgents(): Promise<Agents[]> {
        const query = `
        SELECT * FROM ${Config.DB_Postgres_SUBS_SCHEMA}.agent
        `
        const agents: Agents[] = await this.entityManager.query(query)
        return agents
    }

    verifyRazorpaySignature(input: VerificationInput): { success: boolean; message: string } {
        try {
            let dataToHash = '';
            let context = {};
            if (input.type === 'order') {
                dataToHash = `${input.razorpay_order_id}|${input.razorpay_payment_id}`;
                context = {
                    razorpay_order_id: input.razorpay_order_id,
                    razorpay_payment_id: input.razorpay_payment_id,
                };
            } else {
                dataToHash = `${input.razorpay_payment_id}|${input.razorpay_subscription_id}`;
                context = {
                    razorpay_subscription_id: input.razorpay_subscription_id,
                    razorpay_payment_id: input.razorpay_payment_id,
                };
            }

            const expectedSignature = crypto
                .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET!)
                .update(dataToHash)
                .digest('hex');

            const isValid = expectedSignature === input.razorpay_signature;

            if (isValid) {
                this.logger.info(`Razorpay ${input.type} verification successful.`, context);

                return {
                    success: true,
                    message: `${input.type.charAt(0).toUpperCase() + input.type.slice(1)} verification successful.`,
                };
            } else {
                this.logger.warn(`Razorpay ${input.type} verification failed: Invalid signature.`, context);

                return {
                    success: false,
                    message: `Invalid signature. ${input.type.charAt(0).toUpperCase() + input.type.slice(1)} verification failed.`,
                };
            }
        } catch (error) {
            this.logger.error(`Error during Razorpay ${input.type} verification.`, {
                error: error instanceof Error ? error.message : error,
            });

            return {
                success: false,
                message: `Internal error during Razorpay ${input.type} verification.`,
            };
        }
    }

    // #region SUBSCRIPTION
    async createRazorPayCustomerId({ name, contact, gstin, client_id, email, billing_address }: CreateCustomerPayload): Promise<{ success: boolean; message: string; customer_id?: string }> {
        try {
            const notes = {
                client_id,
                email,
                billing_address,
                name
            }
            const customerPayload: Record<string, string | number | boolean | Notes | undefined> = {
                name,
                email,
                fail_existing: "0",
                notes,
            };

            if (contact) customerPayload.contact = contact;
            if (gstin) customerPayload.gstin = gstin;

            this.logger.debug('Creating Razorpay customer with payload:', customerPayload);


            const customerResponse : RazorpayCustomerApiResponse = await razorpayApi.post('/customers', customerPayload);
            const customerId :string= customerResponse?.data?.id;
            if (customerId) {
                await this.updateUserTablewithdetails(customerId, gstin, billing_address, client_id)

                this.logger.info('Razorpay customer created successfully.', {
                    client_id,
                    name,
                    email,
                    razorpay_order_id: customerId,
                });

                return {
                    success: true,
                    message: 'Razorpay customer created successfully.',
                    customer_id: customerId,
                };
            } else {
                this.logger.error('No Razorpay customer ID returned in response.', {
                    client_id,

                });

                return {
                    success: false,
                    message: 'Razorpay did not return an customer ID.',
                };
            }
        } catch (error) {
            this.logger.error('Error creating Razorpay customer', {
                client_id,
                error: error instanceof Error ? error.message : error,
            });

            return {
                success: false,
                message: 'Internal error while creating Razorpay customer.',
            };
        }
    }

    private async updateUserTablewithdetails(customerId: string, gstin: string | null = null, billing_address: string, client_id: string) {
        const query = `
                        UPDATE config.user_account
                        SET
                            razorpay_customerid = $1,
                            gstin = $2,
                            billing_address = $3
                        WHERE client_id = $4
                    `

        await this.entityManager.query(query, [customerId, gstin, billing_address, client_id])
    }

    async createRazorPaySubscription({
        plan_id,
        plan_name,
        client_id,
        client_Phone,
        client_Email,
        rzrPay_PlanId,
        rzrPay_ClientId,
        isYearly,
        amount,
        attribute,
        autoRenew,
        extra_connectors,
        max_users,
        currency
    }: CreateRazorpaySubscriptionPayload): Promise<{ success: boolean; message: string; subs_id?: string; }> {
        try {
            await this.checkCancelRazorPaySubscription(client_id)
            const end_dt = new Date();

            if (isYearly) {
                end_dt.setFullYear(end_dt.getFullYear() + 1);
            } else {
                end_dt.setMonth(end_dt.getMonth() + 1);
            }

            end_dt.setHours(23, 59, 59, 999);

            const subscriptionRes : RazorpaySubscriptionApiResponse = await razorpayApi.post('/subscriptions', {
                plan_id: rzrPay_PlanId,
                customer_id: rzrPay_ClientId,
                total_count: 12,
                quantity: 1,
                customer_notify: 1,
                notes: {
                    plan_id,
                    plan_name,
                    client_id,
                    autoRenew,
                    extra_connectors,
                    max_users,
                },
                notify_info: {
                    notify_phone: client_Phone,
                    notify_email: client_Email,
                },
            });
            
            const razorpaySubscriptionId: string = subscriptionRes?.data?.id;

            if (razorpaySubscriptionId) {
                const insertPayload: SubsIntsertPayload = {
                    client_id,
                    plan_id,
                    plan_name,
                    razorpay_plan_id: rzrPay_PlanId,
                    razorpay_subscription_id: razorpaySubscriptionId,
                    currency,
                    amount,
                    extra_connectors,
                    max_users,
                    auto_renew: true,
                    is_yearly: isYearly ?? false,
                    attribute,
                    notes: subscriptionRes.data.notes,
                    subs_end_dt: end_dt.toISOString(),
                };

                const creationResult = await this.createSubsciptionContract(insertPayload);

                if (creationResult.success) {
                    this.logger.info('Razorpay subscription created and saved successfully.', {
                        client_id,
                        razorpay_subscription_id: creationResult.razorpay_subscription_id,
                    });

                    return {
                        success: true,
                        message: 'Razorpay subscription created and saved successfully.',
                        subs_id: creationResult.razorpay_subscription_id,
                    };
                } else {
                    this.logger.error('Failed to save Razorpay subscription to database', {
                        client_id,
                        razorpay_subscription_id: razorpaySubscriptionId,
                    });

                    return {
                        success: false,
                        message: 'Failed to save Razorpay subscription to database.',
                    };
                }
            } else {
                this.logger.error('No Razorpay subscription ID returned in response.', {
                    client_id,
                    rzrPay_ClientId,
                });

                return {
                    success: false,
                    message: 'Razorpay did not return a subscription ID.',
                };
            }
        } catch (error) {
            this.logger.error('Error creating Razorpay subscription', {
                client_id,
                rzrPay_ClientId,
                error: error instanceof Error ? error.message : error,
            });

            return {
                success: false,
                message: 'Internal error while creating Razorpay subscription.',
            };
        }
    }

    async checkCancelRazorPaySubscription(client_id: string): Promise<{ success: boolean; message: string; }> {
        try {
            const cancelQuery = `
                    SELECT razorpay_subscription_id FROM subscription.subscription
                    WHERE client_id = $1
                                `;

            const existingSubscription : {razorpay_subscription_id: string}[] | [] = await this.entityManager.query(cancelQuery, [client_id]);
            const existingID: string = existingSubscription?.[0]?.['razorpay_subscription_id'];

            if (existingID) {
                await razorpayApi.post(`/subscriptions/${existingID}/cancel`, {
                    cancel_at_cycle_end: 0,
                });

                await this.updateSubscriptionContract({ clientId: client_id, status: 'cancelled', remarks: 'User upgraded to a new plan' })
                this.logger.info(`Subscription ${existingID} cancelled successfully.`);

                return {
                    success: true,
                    message: 'Subscription cancelled successfully.',
                };
            } else {
                this.logger.warn(`No Razorpay subscription found for client_id: ${client_id}`);
                return {
                    success: false,
                    message: 'No active subscription found to cancel.',
                };
            }
        } catch (error) {
            this.logger.error('Error while cancelling Razorpay subscription', {
                client_id,
                error: error instanceof Error ? error.message : error,
            });

            return {
                success: false,
                message: 'Cancellation of existing subscription failed due to an internal error.',
            };
        }
    }

    private async createSubsciptionContract(insertPayload: SubsIntsertPayload): Promise<{ success: boolean; message: string; razorpay_subscription_id: string }> {
        try {
            const query = `CALL subscription.pr_insert_subscription_record($1::jsonb)`
            await this.entityManager.query(query, [JSON.stringify(insertPayload)]);

            return {
                success: true,
                message: 'Subscription purchased successfully.',
                razorpay_subscription_id: insertPayload?.razorpay_subscription_id,
            };
        } catch (error) {
            this.logger.error('Error while inserting subscription contract', {
                client_id: insertPayload.client_id,
                razorpay_subscription_id: insertPayload?.razorpay_subscription_id,
                error: error instanceof Error ? error.message : error,
            });


            return {
                success: false,
                message: 'Subscription contract creation failed due to an internal error.',
                razorpay_subscription_id: insertPayload?.razorpay_subscription_id,
            };
        }
    }

    private async activateSubscriptionContract(clientId: string): Promise<{ success: boolean; message: string; }> {

        try {
            const query = `
                            UPDATE subscription.subscription
                            SET
                                status = 'active',
                                is_active = true
                            WHERE client_id = $1
                                AND is_Active = false;
                        `;

            await this.entityManager.query(query, [clientId]);

            this.logger.info(`Activated subscription for client ${clientId}`);

            return {
                success: true,
                message: 'Subscription activated successfully',
            };
        } catch (error) {
            this.logger.error('Failed to activate subscription: ${error.message}', { clientId, error: error instanceof Error ? error.message : error });
            return {
                success: false,
                message: 'Subscription activation failed',
            };
        }
    }

    // async pauseRazorPaySubscription(client_id: string): Promise<{ success: boolean; message: string; }> {
    //     try {
    //         const cancelQuery = `
    //                 SELECT razorpay_subscription_id FROM subscription.subscription
    //                 WHERE client_id = $1
    //                             `;

    //         const existingSubscription = await this.entityManager.query(cancelQuery, [client_id]);
    //         const existingID = existingSubscription?.[0]?.['razorpay_subscription_id'];

    //         if (existingID) {
    //             await razorpayApi.post(`/subscriptions/${existingID}/pause`, {
    //                 pause_at: "now"
    //             });

    //             this.logger.info(`Subscription ${existingID} paused successfully.`);

    //             return {
    //                 success: true,
    //                 message: 'Subscription paused successfully.',
    //             };
    //         } else {
    //             this.logger.warn(`No Razorpay subscription found for client_id: ${client_id}`);
    //             return {
    //                 success: false,
    //                 message: 'No active subscription found to pause.',
    //             };
    //         }
    //     } catch (error) {
    //         this.logger.error('Error while pausing Razorpay subscription', {
    //             client_id,
    //             error: error instanceof Error ? error.message : error,
    //         });

    //         return {
    //             success: false,
    //             message: 'Pausing of existing subscription failed due to an internal error.',
    //         };
    //     }
    // }

    // async resumeRazorPaySubscription(client_id: string): Promise<{ success: boolean; message: string; }> {
    //     try {
    //         const cancelQuery = `
    //                 SELECT razorpay_subscription_id FROM subscription.subscription
    //                 WHERE client_id = $1
    //                             `;

    //         const existingSubscription = await this.entityManager.query(cancelQuery, [client_id]);
    //         const existingID = existingSubscription?.[0]?.['razorpay_subscription_id'];

    //         if (existingID) {
    //             await razorpayApi.post(`/subscriptions/${existingID}/resume`, {
    //                 resume_at: "now"
    //             });

    //             this.logger.info(`Subscription ${existingID} resumed successfully.`);

    //             return {
    //                 success: true,
    //                 message: 'Subscription resumed successfully.',
    //             };
    //         } else {
    //             this.logger.warn(`No Razorpay subscription found for client_id: ${client_id}`);
    //             return {
    //                 success: false,
    //                 message: 'No active subscription found to resume.',
    //             };
    //         }
    //     } catch (error) {
    //         this.logger.error('Error while resuming Razorpay subscription', {
    //             client_id,
    //             error: error instanceof Error ? error.message : error,
    //         });

    //         return {
    //             success: false,
    //             message: 'Resuming of existing subscription failed due to an internal error.',
    //         };
    //     }
    // }

    // async haltedSubscriptionContract(rzrPaySubId: string, clientId: string) {
    //     try {
    //         const query = `
    //         UPDATE subscription.subscription
    //         SET 
    //             status = 'expired',
    //             is_active = false,
    //             auto_renew = false,
    //             subs_endt_dt = NOW(),
    //             razorpay_subscription_id = NULL
    //         WHERE razorpay_subscription_id = $1
    //           AND client_id = $2
    //     `;

    //         // TODO neds to put it in history 
    //         const result = await this.entityManager.query(query, [rzrPaySubId, clientId]);

    //         this.logger.info(`Marked subscription as expired: ${rzrPaySubId}, client: ${clientId}`);

    //         return {
    //             success: true,
    //             message: 'Subscription marked as expired',
    //             result,
    //         };
    //     } catch (error) {
    //         this.logger.error('Failed to mark subscription as expired', { rzrPaySubId, clientId });
    //     }
    // }

    // async cancelledSubscriptionContract(rzrPaySubId: string, clientId: string) {
    //     try {
    //         const query = `
    //     UPDATE subscription.subscription
    //     SET 
    //         status = 'cancelled',
    //         razorpay_subscription_id = NULL,
    //         subs_end_dt = NOW(),
    //         is_active = false,
    //         auto_renew = false
    //     WHERE razorpay_subscription_id = $1
    // `;
    //         // TODO need to put all things in history
    //         await this.entityManager.query(query, [rzrPaySubId, clientId])
    //         this.logger.info(`Marked subscription as cancelled: ${rzrPaySubId}, client: ${clientId}`);

    //         return {
    //             success: true,
    //             message: 'Subscription marked as canceled',
    //         };
    //     } catch (error) {
    //         this.logger.error('Failed to mark subscription as cancelled', { rzrPaySubId, clientId });
    //     }

    // }
    // async completedSubscriptionContract(rzrPaySubId: string, clientId: string) {
    //     try {
    //         const query = `
    //     UPDATE subscription.subscription
    //     SET 
    //         status = 'cancelled',
    //         razorpay_subscription_id = NULL,
    //         subs_end_dt = NOW(),
    //         is_active = false,
    //         auto_renew = false
    //     WHERE razorpay_subscription_id = $1
    // `;
    //         // TODO need to put all things in history
    //         await this.entityManager.query(query, [rzrPaySubId, clientId])
    //         this.logger.info(`Marked subscription as completed: ${rzrPaySubId}, client: ${clientId}`);

    //         return {
    //             success: true,
    //             message: 'Subscription marked as completed',
    //         };
    //     } catch (error) {
    //         this.logger.error('Failed to mark subscription as completed', { rzrPaySubId, clientId });
    //     }
    // }

    private async updateSubscriptionContract({
        clientId,
        status,
        remarks
    }: UpdateSubscriptionContractPayload): Promise<{ success: boolean; message: string; }> {
        try {
            const query = `
        CALL subscription.pr_end_subscription_contract($1, $2, $3)
    `;

            await this.entityManager.query(query, [clientId, status, remarks]);

            this.logger.info(`Marked subscription as ${status}: client: ${clientId}`);

            return {
                success: true,
                message: `Subscription marked as ${status}`,
            };
        } catch (error) {
            this.logger.error(`Failed to mark subscription as ${status}`, { clientId, error: error instanceof Error ? error.message : error });
            return {
                success: false,
                message: `Failed to mark subscription as ${status}`,
            };
        }
    }


    async getSubscriptionContractRecords(client_id: string): Promise<{ success: boolean; message: string; subscription?: SubscriptionRecordDTO[]; }> {
        const query = `
                        SELECT * FROM subscription.subscription
                        WHERE client_id = $1
                    `;

        try {
            const subscription: SubscriptionRecordDTO[] | [] = await this.entityManager.query(query, [client_id]);

            this.logger.info('Fetched current user subscription details', { client_id });

            if (subscription.length) {
                return {
                    success: true,
                    message: 'Subscription records fetched successfully.',
                    subscription,
                };
            } else {
                return {
                    success: true,
                    message: 'No Subscription records found.',
                };
            }
        } catch (error) {
            this.logger.error('Failed to fetch subscription records', {
                client_id,
                error: error instanceof Error ? error.message : error,
            });

            return {
                success: false,
                message: 'Failed to fetch subscription records.',
            };
        }
    }
    async getSubscriptionHistoryRecords(client_id: string): Promise<{ success: boolean; message: string; history?: SubscriptionHistoryRecordDTO[]; }> {
        const query = `
                        SELECT * FROM subscription.subscription_history
                        WHERE client_id = $1
                    `;

        try {
            const history: SubscriptionHistoryRecordDTO[] | [] = await this.entityManager.query(query, [client_id]);

            this.logger.info('Fetched current user subscription history details', { client_id });

            if (history.length) {
                return {
                    success: true,
                    message: 'Subscription history fetched successfully.',
                    history,
                };
            } else {
                return {
                    success: true,
                    message: 'No Subscription history found.',
                };
            }
        } catch (error) {
            this.logger.error('Failed to fetch subscription history', {
                client_id,
                error: error instanceof Error ? error.message : error,
            });

            return {
                success: false,
                message: 'Failed to fetch subscription history.',
            };
        }
    }


    // #region ORDER

    async createRazorPayOrder({ client_id, amount, currency, topup = false, agents }: CreateOrderPayload): Promise<{ success: boolean; message: string; order_id?: string }> {
        try {
            const notes = {
                client_id,
                topup: topup,
            }
            const orderPlayload = {
                amount: amount * 100,
                currency: currency,
                receipt: `Topup created for ${client_id} for ${currency}:${amount}`,
                notes: notes
            }
            const orderResponse : RazorpayOrderApiResponse = await razorpayApi.post('/orders', orderPlayload)

            const razorpayOrderId: string = orderResponse?.data?.id;

            if (razorpayOrderId) {
                this.logger.info('Razorpay order created successfully.', {
                    client_id,
                    razorpay_order_id: razorpayOrderId,
                    amount,
                    currency,
                    topup,
                });
                await this.updateClientOrderStatus({
                    client_id: client_id,
                    currency,
                    amount,
                    razorPay_OrderId: razorpayOrderId,
                    status: 'created',
                    notes
                })
                if (agents) {
                    await this.updateClientTopup({
                        client_id,
                        currency,
                        amount,
                        razorPay_OrderId: razorpayOrderId,
                        status: 'created',
                        agents
                    })
                }

                return {
                    success: true,
                    message: 'Razorpay order created successfully.',
                    order_id: razorpayOrderId,
                };
            } else {
                this.logger.error('No Razorpay order ID returned in response.', {
                    client_id,
                    amount,
                    currency,
                });

                return {
                    success: false,
                    message: 'Razorpay did not return an order ID.',
                };
            }
        } catch (error) {
            this.logger.error('Error creating Razorpay order', {
                client_id,
                amount,
                currency,
                error: error instanceof Error ? error.message : error,
            });

            return {
                success: false,
                message: 'Internal error while creating Razorpay order.',
            };
        }
    }

    private async updateClientOrderStatus({ client_id, customer_id, currency, amount, razorPay_OrderId, razorPay_PaymentId, status, notes }: UpdateClientOrderStatusPayload): Promise<{ success: boolean; message: string; }> {
        try {
            if (!client_id && customer_id) {
                const res : RazorpayCustomerApiResponse = await razorpayApi.get(`/customers/${customer_id}`);
                client_id = res?.data?.notes?.client_id;
            }

            const query = `
                            CALL subscription.pr_upsert_orders_record($1, $2, $3, $4, $5, $6, $7)
                        `;

            await this.entityManager.query(query, [client_id, currency, amount, razorPay_OrderId, razorPay_PaymentId, status, notes]);

            this.logger.info(`Marked Order - ${razorPay_OrderId} as ${status} : ${razorPay_PaymentId} : client: ${client_id}`);

            return {
                success: true,
                message: `Order marked as ${status}`,
            };
        } catch (error) {
            this.logger.error(`Failed to mark order as ${status}`, { razorPay_OrderId, razorPay_PaymentId, status, error: error instanceof Error ? error.message : error });
            return {
                success: false,
                message: `Failed to mark Order as ${status}`,
            };
        }

    }

    // async getOrderRecords(client_id: string): Promise<{ success: boolean; message: string; orders?: any }> {

    //     try {
    //         const query = `
    //         SELECT * FROM subscription.orders
    //         WHERE client_id = $1
    //         `

    //         const res = await this.entityManager.query(query, [client_id])

    //         this.logger.info(`Fetched all Orders successfully: ${client_id}`,)

    //         return {
    //             success: true,
    //             message: 'Fetched all Orders successfully',
    //             orders: res
    //         };
    //     } catch (error) {
    //         this.logger.error('Error while fetching Order records')
    //         return {
    //             success: false,
    //             message: 'Failed to fetch Order records'
    //         }
    //     }
    // }

    // #region PAYMENT

    private async updateClientPaymentStatus({
        client_id,
        customer_id,
        currency,
        amount,
        method,
        razorPay_OrderId,
        rzrPay_PaymentId,
        status,
        notes
    }: UpdateClientPaymentStatusPayload): Promise<{ success: boolean; message: string; }> {
        try {
            if (!client_id && customer_id) {
                const res : RazorpayCustomerApiResponse = await razorpayApi.get(`/customers/${customer_id}`);
                client_id = res?.data?.notes?.client_id;
            }

            const query = `
                            CALL subscription.pr_upsert_payment_records($1, $2, $3, $4, $5, $6, $7, $8)
                        `;

            await this.entityManager.query(query, [client_id, currency, amount, razorPay_OrderId, rzrPay_PaymentId, method, status, notes,]);

            this.logger.info(`Marked Paymeny - ${rzrPay_PaymentId} as ${status} :  client: ${client_id}`);

            return {
                success: true,
                message: `Payment marked as ${status}`,
            };
        } catch (error) {
            this.logger.error(`Failed to mark Payment as ${status}`, { rzrPay_PaymentId, razorPay_OrderId, status, error: error instanceof Error ? error.message : error });
            return {
                success: false,
                message: `Failed to mark Payment as ${status}`,
            };
        }

    }

    async getPaymentRecords(client_id: string): Promise<{ success: boolean; message: string; payments?: PaymentRecordsDTO[] }> {
        try {
            const query = `
            SELECT * FROM subscription.payments
            WHERE client_id = $1
            `

            const payments: PaymentRecordsDTO[] | [] = await this.entityManager.query(query, [client_id])

            this.logger.info(`Fetched all Payments successfully: ${client_id}`,)

            if (payments.length) {
                return {
                    success: true,
                    message: 'Fetched all Payments successfully',
                    payments: payments
                };
            } else {
                return {
                    success: true,
                    message: 'No Payment recors found',
                };
            }
        } catch (error) {
            this.logger.error('Error while fetching Payments records')
            return {
                success: false,
                message: 'Failed to fetch Payments records'
            }
        }
    }

    // #region TOPUP

    private async updateClientTopup({
        client_id,
        currency,
        amount,
        razorPay_OrderId,
        razorPay_PaymentId,
        status,
        agents
    }: UpdateClientTopupPayload): Promise<{ success: boolean; message: string; }> {
        try {
            const query = `
        CALL subscription.pr_insert_topup_records($1, $2, $3, $4, $5, $6, $7)
    `;

            await this.entityManager.query(query, [client_id, currency, amount, razorPay_OrderId, razorPay_PaymentId, status, JSON.stringify(agents)]);

            this.logger.info(`Created topup - ${razorPay_OrderId} as ${status} : ${razorPay_OrderId}: ${razorPay_PaymentId} : client: ${client_id}`);

            return {
                success: true,
                message: `Created topup as ${status}`,
            };
        } catch (error) {
            this.logger.error(`Failed to create topup as ${status}`, { client_id, razorPay_OrderId, razorPay_PaymentId, status, error: error instanceof Error ? error.message : error });
            return {
                success: false,
                message: `Failed to created topup as ${status}`,
            };
        }


    }

    async getTopupRecords(client_id: string): Promise<{ success: boolean; message: string; topups?: TopupRecordsDTO[] }> {
        try {
            const query = `
            SELECT * FROM subscription.topups
            WHERE client_id = $1
            `

            const topups: TopupRecordsDTO[] | [] = await this.entityManager.query(query, [client_id])

            this.logger.info(`Fetched all topups successfully: ${client_id}`,)

            if (topups.length) {
                return {
                    success: true,
                    message: 'Fetched all topups successfully',
                    topups
                };
            } else {
                return {
                    success: true,
                    message: 'No Toupup recor forund'
                }
            }
        } catch (error) {
            this.logger.error('Error while fetching topup records')
            return {
                success: false,
                message: 'Failed to fetch topup records'
            }
        }
    }

    async getAgentUsages(client_id: string): Promise<{ success: boolean; message: string; agentUsages?: AgentUsageRecordsDTO[] }> {
        try {
            const query = `
                                SELECT 
                                    agent_id,
                                    MAX(agent_name) AS agent_name,
                                    MAX(tokens_total) AS total_tokens,
                                    SUM(tokens_used)::INT AS total_tokens_used
                                FROM subscription.agent_usages
                                WHERE client_id = $1
                                GROUP BY agent_id
                                ORDER BY total_tokens_used DESC;
                        `;


            const agentUsages: AgentUsageRecordsDTO[] | [] = await this.entityManager.query(query, [client_id]) || [];

            if (agentUsages.length) {
                return {
                    success: true,
                    message: 'Agent usage records fetched successfully',
                    agentUsages,
                };
            } else {
                return {
                    success: true,
                    message: 'No agent usage records found for this client',
                };
            }
        } catch (error) {
            this.logger.error('Error fetching agent usage records', {
                client_id,
                error: error instanceof Error ? error.message : error,
            });

            return {
                success: false,
                message: 'Failed to fetch agent usage records',
            };
        }
    }


    // #region INVOICE

    private async createInvocieRecord({
        client_id,
        customer_id,
        is_topup,
        currency,
        amount,
        razorpay_order_id,
        razorpay_payment_id
    }: CreateInvocieRecordPayload): Promise<{ success: boolean; message: string; }> {
        try {
            if (!client_id && customer_id) {
                const res : RazorpayCustomerApiResponse = await razorpayApi.get(`/customers/${customer_id}`);
                client_id = res?.data?.notes?.client_id;
            }

            const query = `
      SELECT * FROM subscription.fn_insert_invoice_records($1, $2, $3, $4, $5, $6)
    `;

            const result : InvoiceRecordsResponseDTO[] = await this.entityManager.query(query, [client_id, is_topup, currency, amount, razorpay_order_id, razorpay_payment_id]);
            const { success, message, invoice_id } : InvoiceRecordsResponseDTO = result[0];

            await this.generateAndSaveInvoicePdf(invoice_id as string)

            return {
                success,
                message,
            };
        } catch (error) {
            this.logger.error('Failed to generate invoice from stored procedure', {
                client_id,
                is_topup,
                amount,
                razorpay_payment_id,
                razorpay_order_id,
                error: error instanceof Error ? error.message : error,
            });

            return {
                success: false,
                message: 'Internal error while generating invoice.',
            };
        }
    }

    async generateAndSaveInvoicePdf(invoice_id: string): Promise<{ success: boolean; message: string; signedUrl?: string }> {
        try {
            const invoice = await this.getInvoice(invoice_id);
            if (!invoice) throw new Error("Invoice not found");

            const client = await this.getClient(invoice.client_id);
            if (!client) throw new Error("Client not found");

            const invoiceItems = invoice.is_topup ? await this.getTopupItems(invoice.topup_id as string) : [];

            const invoiceData: GenerateInvoiceParams = {
                invoiceTitle: 'Invoice',
                companyInfo: getCompanyInfo(),
                invoiceInfo: getInvoiceInfo(invoice),
                billedTo: getAddressBlock(client),
                shippedTo: getAddressBlock(client),
                items: buildInvoiceItems(invoice, invoiceItems),
                notes: buildInvoiceNotes(invoice),
                totals: getInvoiceTotals(invoice.amount),
                contact: getCompanyContact(),
            };

            const pdfBuffer = await this.genpdfservice.generateInvoicePDFBuffer(invoiceData);
            const fileName = `invoice-${Date.now()}.pdf`;
            const azureUrl = await this.azureService.uploadBuffer(pdfBuffer, fileName, 'application/pdf');
            const signedUrl = await this.azureService.decodeBlob({ image_url: azureUrl, longTerm: true });
            await this.saveInvoiceUrl(signedUrl, invoice_id)

            return {
                success: true,
                message: 'Generated and saved invoice PDF',
                signedUrl
            };
        } catch (error) {
            this.logger.error('Failed to generate and save invoice PDF', {
                error: error instanceof Error ? error.message : error,
            });
             return { success: false, message: 'Failed to generate and save the PDF' };
        }
    }

    private async getInvoice(invoice_id: string): Promise<InvoiceDTO> {
        const query = `SELECT * FROM subscription.invoices WHERE id = $1`;
        const result: InvoiceDTO[] = await this.entityManager.query(query, [invoice_id]);
        return result[0];
    }

    private async getClient(client_id: string): Promise<ClientDTO> {
        const query = `SELECT * FROM config.user_account WHERE client_id = $1`;
        const result: ClientDTO[] = await this.entityManager.query(query, [client_id]);
        return result[0];
    }

    private async getTopupItems(topup_id: string):Promise<TopupItemsDTO[]> {
        const query = `SELECT * FROM subscription.topup_items WHERE topup_id = $1`;
        return await this.entityManager.query(query, [topup_id]);
    }

    private async saveInvoiceUrl(signedUrl: string, invoice_id: string) {
        const saveinvoicequery = `
            UPDATE subscription.invoices
            SET url = $1,
                status = $2
            WHERE id = $3
            `
        await this.entityManager.query(saveinvoicequery, [signedUrl, 'created', invoice_id])
    }


    async getClientAllInvoiceRecord(client_id: string): Promise<{ success: boolean; message: string; invoices?: InvoiceRecordsDTO[] }> {
        try {
            const query = `
      SELECT * FROM subscription.invoices
      WHERE client_id = $1
      ORDER BY created_dt DESC
    `;

            const invoices: InvoiceRecordsDTO[] | [] = await this.entityManager.query(query, [client_id]) || [];

            if (invoices.length === 0) {
                return {
                    success: true,
                    message: 'No invoices found for this client.',
                    invoices: [],
                };
            }

            return {
                success: true,
                message: 'Invoices fetched successfully.',
                invoices,
            };
        } catch (error) {
            this.logger.error('Error fetching invoices for client', {
                client_id,
                error: error instanceof Error ? error.message : error,
            });

            return {
                success: false,
                message: 'Failed to fetch invoices.',
            };
        }
    }

    //#region WEBHOOK

    async SubscriptionWebhookEvents(event: SubscriptionWebhookEvent): Promise<{ success: boolean; message: string }> {
        try {
            const webhookEvent : string = event?.event
            const subEntity = event?.payload?.subscription?.entity;
            const clientId : string = subEntity?.notes?.client_id as string;
            switch (webhookEvent) {
                case 'subscription.activated': {
                    true
                    const res = await this.activateSubscriptionContract(clientId);
                    return {
                        success: res.success,
                        message: res.success
                            ? 'Activated subscription for the client'
                            : 'Failed to activate subscription for the client'
                    };
                }
                case 'subscription.pending': {
                    return {
                        success: true,
                        message: 'Sent mail to cleint about pending subscription'
                    };
                }
                case 'subscription.halted': {
                    const res = await this.updateSubscriptionContract({ clientId, status: 'expired', remarks: 'Exhausted retries and paused subscription contract' });
                    return {
                        success: res.success,
                        message: res.success
                            ? 'Expired subscription for the client'
                            : 'Failed to makr subscription as expired for the client'
                    };
                }
                case 'subscription.completed': {
                    const res = await this.updateSubscriptionContract({ clientId, status: 'completed', remarks: 'Subscription period completed' });
                    return {
                        success: res.success,
                        message: res.success
                            ? 'Subscription completed for the client'
                            : 'Failed to complete subscription for the client'
                    };
                }
                case 'payment.authorized': {
                    const payemntEntity = event?.payload?.payment?.entity
                    const amount = (payemntEntity?.amount) / 100
                    const notes = {
                        vpa: payemntEntity?.vpa,
                        token_id: payemntEntity?.token_id,
                        desc: payemntEntity?.description,
                        contact: payemntEntity?.contact,
                        email: payemntEntity?.email
                    }
                    const clientId : string = payemntEntity?.notes?.client_id as string;
                    const res = await this.updateClientPaymentStatus({
                        client_id: clientId,
                        customer_id: payemntEntity?.customer_id,
                        currency: payemntEntity?.currency,
                        amount,
                        method: payemntEntity?.method,
                        razorPay_OrderId: payemntEntity.order_id,
                        rzrPay_PaymentId: payemntEntity?.id,
                        status: 'authorized',
                        notes
                    });

                    return {
                        success: res.success,
                        message: res.success
                            ? 'Authorized payment for the client'
                            : 'Failed to authorize payment for the client'
                    };
                }
                case 'payment.captured': {
                    const payemntEntity = event?.payload?.payment?.entity
                    const amount = (payemntEntity?.amount) / 100
                    const notes = {
                        vpa: payemntEntity?.vpa,
                        token_id: payemntEntity?.token_id,
                        desc: payemntEntity?.description,
                        contact: payemntEntity?.contact,
                        email: payemntEntity?.email
                    }
                    const clientId : string = payemntEntity?.notes?.client_id as string;
                    const is_topup : boolean = payemntEntity?.notes?.topup as boolean ?? false;
                    const res = await this.updateClientPaymentStatus({
                        client_id: clientId,
                        customer_id: payemntEntity?.customer_id,
                        currency: payemntEntity?.currency,
                        amount,
                        method: payemntEntity?.method,
                        razorPay_OrderId: payemntEntity?.order_id,
                        rzrPay_PaymentId: payemntEntity?.id,
                        status: 'captured',
                        notes
                    });

                    if (is_topup) {
                        const query = `
                        UPDATE subscription.topups
                        SET status = 'paid',
                            razorpay_payment_id = $1
                        WHERE razorpay_order_id = $2;

                        `

                        await this.entityManager.query(query, [payemntEntity?.id, payemntEntity?.order_id])
                    }
                    await this.createInvocieRecord({ client_id: clientId, customer_id: payemntEntity?.customer_id, is_topup: is_topup, currency: payemntEntity?.currency, amount, razorpay_order_id: payemntEntity?.order_id, razorpay_payment_id: payemntEntity?.id })
                    return {
                        success: res.success,
                        message: res.success
                            ? 'Captured payment for the client'
                            : 'Failed to capture payment for the client'
                    };
                }

                case 'order.paid': {
                    const payemntEntity = event?.payload?.payment?.entity
                    const orderEntity = event?.payload?.order?.entity
                    const amount = (payemntEntity?.amount) / 100
                    const notes = {
                        vpa: payemntEntity?.vpa,
                        token_id: payemntEntity?.token_id,
                        desc: payemntEntity?.description,
                        contact: payemntEntity?.contact,
                        email: payemntEntity?.email
                    }
                    const clientId : string = orderEntity?.notes?.client_id as string;
                    const res = await this.updateClientOrderStatus({
                        client_id: clientId,
                        customer_id: payemntEntity?.customer_id,
                        currency: payemntEntity?.currency,
                        amount,
                        razorPay_OrderId: orderEntity?.id,
                        razorPay_PaymentId: payemntEntity?.id ?? null,
                        status: 'paid',
                        notes
                    });

                    return {
                        success: res.success,
                        message: res.success
                            ? 'Paid order for the client'
                            : 'Failed to pay order for the client'
                    };
                }

                default:
                    this.logger.warn(`Unhandled event: ${webhookEvent}`);
                    return {
                        success: true,
                        message: `Unhandled event type: ${webhookEvent}`
                    };
            }

        } catch (error) {
            this.logger.error('Webhook error:', { error: error instanceof Error ? error.message : error });
            return {
                success: false,
                message: 'Internal error in Subscription webhook'
            };
        }
    }

}