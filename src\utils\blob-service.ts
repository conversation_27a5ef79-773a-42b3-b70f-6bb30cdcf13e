import { logger } from '../config/logger';
import {
   BlobServiceClient,
   StorageSharedKeyCredential,
} from '@azure/storage-blob';

function parseData(input: string) {
   const dataArray = input
      .split('\n')
      .filter((line) => line.trim() !== '')
      .map((line) => JSON.parse(line));
   return dataArray;
}

class BlobService {
   private blobServiceClient: BlobServiceClient;

   constructor(accountName: string, accountKey: string) {
      const sharedKeyCredential = new StorageSharedKeyCredential(
         accountName,
         accountKey,
      );
      this.blobServiceClient = new BlobServiceClient(
         `https://${accountName}.blob.core.windows.net`,
         sharedKeyCredential,
      );
   }

   public async saveInBlob(
      blobName: string,
      data: string,
      folder: string = '',
      container: string = '',
   ): Promise<void> {
      try {
         const containerClient =
            this.blobServiceClient.getContainerClient(container);

         // Ensure the folder exists (create if necessary)
         await containerClient.createIfNotExists();

         // Combine the folder and blob names with a forward slash
         const fullBlobName = folder ? `${folder}/${blobName}` : blobName;

         const blobClient = containerClient.getBlockBlobClient(fullBlobName);

         // Upload the JSON data to the blob
         const uploadResponse = await blobClient.upload(
            data,
            Buffer.byteLength(data),
         );
         logger.info(
            `File "${fullBlobName}" uploaded.`,
            uploadResponse.versionId,
         );
      } catch (err) {
         logger.error(`Error while saving data in blob:`, err);
         throw err;
      }
   }

   public async fetchBlobStorage(
      blobName: string,
      container: string = '',
   ): Promise<any> {
      try {
         const containerClient =
            this.blobServiceClient.getContainerClient(container);

         // Get a block blob client pointing to the blob
         const blockBlobClient = containerClient.getBlockBlobClient(blobName);

         // Download the blob data as a buffer
         const blobData = await blockBlobClient.downloadToBuffer();

         // Convert the buffer to a string (assuming JSON data)
         const jsonData = blobData.toString();

         // console.log('JSON DATA ', jsonData);

         // Parse and return the JSON data
         return JSON.parse(jsonData);
      } catch (error) {
         logger.error(`Error fetching blob data:`, error);
         throw error;
      }
   }

   public async fetchAllBlobsFromContainer(container: string): Promise<any[]> {
      try {
         const containerClient =
            this.blobServiceClient.getContainerClient(container);
         const jsonDataArray: any[] = [];

         for await (const blob of containerClient.listBlobsFlat()) {
            const blockBlobClient = containerClient.getBlockBlobClient(
               blob.name,
            );

            try {
               const blobData = await blockBlobClient.downloadToBuffer();
               //    if (blobData.toString())
               //       console.log('blob', blobData.toString());
               //    const jsonData = JSON.parse(blobData.toString());
               const tableName = blob.name.split('/')[0];
               const payload = {
                  tableName,
                  data: parseData(blobData.toString()),
               };

               jsonDataArray.push(payload);
            } catch (err) {
               logger.error('Could not parse JSON for ' + blob.name);
            }
         }

         //  console.log('JSON DATA ARRAY', jsonDataArray);
         return jsonDataArray;
      } catch (error) {
         logger.error(`Error fetching all blobs from container:`, error);
         throw error;
      }
   }
}

export default BlobService;
