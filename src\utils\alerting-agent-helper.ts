import { Config } from '../config';
import { format, subDays } from 'date-fns';

export const parseRecipients = (recipients: string): string[] => {
   return recipients.replace(/['"]+/g, '').split(',');
};

export const compareValues = (
   todaysValue: number,
   baseValue: number,
   trend: string,
   threshold: number,
): boolean => {
   switch (trend) {
      case 'Decreasing':
         return todaysValue <= baseValue * (1 - threshold / 100);
      case 'Increasing':
         return todaysValue >= baseValue * (1 + threshold / 100);
      case 'More_than':
         return todaysValue > threshold;
      case 'Less_than':
         return todaysValue < threshold;
      default:
         return false;
   }
};

export const getTableName = (
   channel: string,
   isCampaignLevel: boolean,
): string => {
   const schema = Config.DB_Postgres_SCHEMA;
   if (isCampaignLevel) {
      switch (channel.toLowerCase()) {
         case 'facebookads':
            return `${schema}.m_campaign_daywise`;
         case 'googleads':
            return `${schema}.m_campaign_table_googleads`;
      }
   }
   return `${schema}.dashboard_kpi`;
};

export const mapChannel = (channel: string): string => {
   switch (channel.toLowerCase()) {
      case 'facebookads':
         return 'Meta Ads';
      case 'googleads':
         return 'Google Ads';
      case 'amazon_selling_partner':
         return 'Amazon Seller Partner';
      case 'amazon_ads':
         return 'Amazon Ads';
      case 'store':
         return 'Shopify';
      case 'web':
         return 'Web Analytics';
      default:
         return channel;
   }
};

const mapTrend = (trend: string): string => {
   switch (trend) {
      case 'Decreasing':
         return 'has declined';
      case 'Increasing':
         return 'has risen';
      case 'More_than':
         return 'has reached';
      case 'Less_than':
         return 'has reached';
      default:
         return trend;
   }
};

export const splitString = (str: string) => {
   return str.split('_').join(' ');
};

export const createDescriptionText = (
   channel: string,
   trend: string,
   value: string,
   value_type: string,
   comparison: string,
   campaign_names: string,
   kpi: string,
) => {
   const channelName = mapChannel(channel);
   const kpiName = splitString(kpi);
   const trendText = mapTrend(trend);

   const formattedValue = value_type === 'percentage' ? `${value}%` : value;

   const campaignArray = campaign_names
      ? campaign_names.split(',').map((name) => name.trim())
      : [];

   const campaignText = campaignArray.length
      ? `campaigns:
         <ol style="display: grid; grid-template-columns: repeat(2, minmax(0, 1fr)); gap: 4px; margin: 4px 0; padding-left: 20px;">
            ${campaignArray.map((name) => `<li><b>${name}</b></li>`).join('')}
         </ol>`
      : '';

   const comparisonText = comparison
      ? ` compared to <b>${splitString(comparison)}</b>`
      : '';

   const changeText =
      value_type === 'percentage'
         ? `by <b>${formattedValue}</b>`
         : `<b>${formattedValue}</b>`;

   return `Your <b>${kpiName}</b> on <b>${channelName}</b> ${campaignText} <b>${trendText}</b> ${changeText}${comparisonText}.`;
};

export const createQuestionText = (
   channel: string,
   trend: string,
   value: string,
   value_type: string,
   comparison: string,
   campaign_name: string,
   kpi: string,
) => {
   const channelName = mapChannel(channel);
   const kpiName = splitString(kpi).toUpperCase();
   const campaignText = campaign_name
      ? ` for ${campaign_name
           .split(',')
           .map((name) => `"${name.trim()}"`)
           .join(', ')}`
      : '';

   const formattedValue = value
      ? `${value}${value_type === 'percentage' ? '%' : ''}`
      : '';

   const yesterdayDate = subDays(new Date(), 1);
   const formattedDate = `on ${format(yesterdayDate, 'MMMM do yyyy')}`;

   let comparisonText = '';
   const daysMatch = comparison?.match(/(\d+)\s*days?/i);
   if (daysMatch) {
      comparisonText = `versus previous last ${daysMatch[1]} days`;
   } else if (comparison) {
      comparisonText = `versus previous ${splitString(comparison)}`;
   } else {
      comparisonText = 'compared to the previous day';
   }

   let operator = '';

   switch (trend) {
      case 'Less than':
         return encodeURIComponent(
            `Why was my ${channelName} ${kpiName}${campaignText} less than ${formattedValue} ${formattedDate}? Please find and analyze the root cause.`,
         );
      case 'More than':
         return encodeURIComponent(
            `Why was my ${channelName} ${kpiName}${campaignText} more than ${formattedValue} ${formattedDate}? Please find and analyze the root cause.`,
         );
      case 'Increasing':
      case 'Decreasing':
         operator =
            trend.toLowerCase() === 'increasing' ? 'increased' : 'decreased';
         return encodeURIComponent(
            `Why did my ${channelName} ${kpiName}${campaignText} ${operator} by ${formattedValue} ${formattedDate} ${comparisonText}? Please find and analyze the root cause.`,
         );
      default:
         operator = trend.toLowerCase();
         return encodeURIComponent(
            `Why did my ${channelName} ${kpiName}${campaignText} ${operator} by ${formattedValue} ${formattedDate} ${comparisonText}? Please find and analyze the root cause.`,
         );
   }
};

export const createDescriptionTextWhatsApp = (
   channel: string,
   trend: string,
   value: string,
   value_type: string,
   comparison: string,
   campaign_names: string,
   kpi: string,
) => {
   const channelName = mapChannel(channel);
   const kpiName = splitString(kpi);
   const trendText = mapTrend(trend);

   const formattedValue = value_type === 'percentage' ? `${value}%` : value;

   const campaignArray = campaign_names
      ? campaign_names.split(',').map((name) => name.trim())
      : [];

   const campaignText = campaignArray.length
      ? `\nCampaigns: ${campaignArray.join(', ')}`
      : '';

   const comparisonText = comparison
      ? ` compared to ${splitString(comparison)}`
      : '';

   const changeText =
      value_type === 'percentage'
         ? `by ${formattedValue}`
         : `${formattedValue}`;

   return `Your ${kpiName} on ${channelName}${campaignText} ${trendText} ${changeText}${comparisonText}.`;
};
